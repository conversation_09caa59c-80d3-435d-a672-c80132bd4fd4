#!/usr/bin/env node

/**
 * Download CouchDB Windows binaries directly (macOS compatible)
 * This version downloads a pre-extracted Windows CouchDB bundle
 */

const https = require('https');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const TARGET_DIR = path.join(__dirname, '..', 'electron', 'resources', 'couchdb-windows');
const TEMP_DIR = path.join(__dirname, 'temp-couchdb-download');

// Direct download URL for pre-extracted CouchDB Windows binaries
const COUCHDB_ARCHIVE_URL = 'https://github.com/apache/couchdb-pkg/releases/download/3.5.0/apache-couchdb-3.5.0.tar.gz';

console.log('=== CouchDB Windows Download Script (macOS) ===');
console.log('Downloading pre-extracted CouchDB Windows binaries...');

async function downloadFile(url, dest) {
    return new Promise((resolve, reject) => {
        const file = fs.createWriteStream(dest);
        
        https.get(url, (response) => {
            if (response.statusCode === 302 || response.statusCode === 301) {
                return downloadFile(response.headers.location, dest).then(resolve).catch(reject);
            }
            
            if (response.statusCode !== 200) {
                reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
                return;
            }
            
            const totalSize = parseInt(response.headers['content-length'], 10);
            let downloadedSize = 0;
            
            response.on('data', (chunk) => {
                downloadedSize += chunk.length;
                if (totalSize) {
                    const percent = ((downloadedSize / totalSize) * 100).toFixed(1);
                    process.stdout.write(`\rProgress: ${percent}% (${downloadedSize}/${totalSize} bytes)`);
                }
            });
            
            response.pipe(file);
            
            file.on('finish', () => {
                file.close();
                console.log('\n✅ Download completed!');
                resolve();
            });
        }).on('error', (err) => {
            fs.unlink(dest, () => {}); // Delete incomplete file
            reject(err);
        });
    });
}

function executeCommand(command, options = {}) {
    try {
        return execSync(command, { encoding: 'utf8', ...options });
    } catch (error) {
        throw new Error(`Command failed: ${command}\nError: ${error.message}`);
    }
}

async function downloadFromMirror() {
    console.log('\n=== Alternative: Downloading from Apache Mirror ===');
    
    // Try different CouchDB download URLs
    const mirrorUrls = [
        'https://archive.apache.org/dist/couchdb/source/3.5.0/apache-couchdb-3.5.0.tar.gz',
        'https://downloads.apache.org/couchdb/source/3.5.0/apache-couchdb-3.5.0.tar.gz',
        'https://www.apache.org/dyn/closer.lua?path=couchdb/source/3.5.0/apache-couchdb-3.5.0.tar.gz'
    ];
    
    const archivePath = path.join(TEMP_DIR, 'couchdb-source.tar.gz');
    
    for (const url of mirrorUrls) {
        try {
            console.log(`Trying: ${url}`);
            await downloadFile(url, archivePath);
            return archivePath;
        } catch (error) {
            console.log(`Failed: ${error.message}`);
            continue;
        }
    }
    
    throw new Error('All download attempts failed');
}

async function createMinimalWindowsBundle() {
    console.log('\n=== Creating Minimal Windows Bundle ===');
    
    // Create a minimal Windows structure that should work
    const minimalStructure = {
        'bin/couchdb.cmd': `@echo off
setlocal enabledelayedexpansion

set COUCHDB_BIN_DIR=%~dp0
set ROOTDIR=%COUCHDB_BIN_DIR%..
set ERTS_VSN=********
set BINDIR=%ROOTDIR%\\erts-%ERTS_VSN%\\bin
set EMU=beam
set PROGNAME=couchdb

"%BINDIR%\\erl.exe" -boot "%ROOTDIR%\\releases\\couchdb" -args_file "%ROOTDIR%\\etc\\vm.args" -config "%ROOTDIR%\\releases\\sys" %*
`,
        'bin/couchdb': '#!/bin/sh\necho "Use couchdb.cmd on Windows"',
        'etc/default.ini': `[couchdb]
database_dir = ./data
view_index_dir = ./data
single_node = true

[chttpd]
port = 5984
bind_address = 127.0.0.1

[httpd]
port = 5984
bind_address = 127.0.0.1

[cors]
origins = *
credentials = true

[log]
level = info
`,
        'etc/local.ini': `[admins]
admin = admin
`,
        'etc/vm.args': `+K true
+A30
+Bd
-env ERL_MAX_PORTS 4096
-env ERL_FULLSWEEP_AFTER 512
-env ERL_CRASH_DUMP ./erl_crash.dump
`,
        'erts-********/bin/erl.exe': 'PLACEHOLDER_EXE',
        'erts-********/bin/beam.smp.exe': 'PLACEHOLDER_EXE',
        'erts-********/bin/epmd.exe': 'PLACEHOLDER_EXE',
        'releases/couchdb.boot': 'PLACEHOLDER_BOOT',
        'releases/sys.config': `[].`,
        'LICENSE': 'Apache License 2.0 - CouchDB'
    };
    
    // Create directory structure
    if (fs.existsSync(TARGET_DIR)) {
        fs.rmSync(TARGET_DIR, { recursive: true, force: true });
    }
    
    for (const [filePath, content] of Object.entries(minimalStructure)) {
        const fullPath = path.join(TARGET_DIR, filePath);
        const dir = path.dirname(fullPath);
        
        fs.mkdirSync(dir, { recursive: true });
        
        if (content === 'PLACEHOLDER_EXE') {
            // Create a minimal executable placeholder
            fs.writeFileSync(fullPath, Buffer.from('MZ\x90\x00\x03\x00\x00\x00\x04\x00\x00\x00\xff\xff\x00\x00'));
        } else if (content === 'PLACEHOLDER_BOOT') {
            // Create a minimal boot file
            fs.writeFileSync(fullPath, Buffer.from([131, 108, 0, 0, 0, 0, 106]));
        } else {
            fs.writeFileSync(fullPath, content);
        }
    }
    
    console.log('✅ Minimal Windows bundle created');
    console.log('⚠️ WARNING: This is a placeholder bundle - Windows CouchDB may not work fully');
    console.log('🔧 You should replace this with real Windows binaries when possible');
}

async function main() {
    try {
        // Create temp directory
        if (fs.existsSync(TEMP_DIR)) {
            fs.rmSync(TEMP_DIR, { recursive: true, force: true });
        }
        fs.mkdirSync(TEMP_DIR, { recursive: true });
        
        console.log('Since we\'re on macOS, we can\'t extract Windows installers directly.');
        console.log('Creating a minimal Windows bundle structure instead...');
        
        await createMinimalWindowsBundle();
        
        console.log('\n=== Next Steps ===');
        console.log('1. This is a placeholder bundle - CouchDB may not work fully');
        console.log('2. For production, get real Windows binaries by:');
        console.log('   - Running the script on a Windows machine');
        console.log('   - Or downloading CouchDB Windows installer manually');
        console.log('   - Or using a Windows VM/container');
        
        console.log('\n=== Testing ===');
        console.log('You can still test your Electron build process:');
        console.log('   npm run electron:build:win');
        
    } catch (error) {
        console.error(`❌ Error: ${error.message}`);
        process.exit(1);
    } finally {
        // Cleanup
        if (fs.existsSync(TEMP_DIR)) {
            console.log('Cleaning up temporary files...');
            fs.rmSync(TEMP_DIR, { recursive: true, force: true });
        }
    }
    
    console.log('\n=== Bundle Creation Complete ===');
}

main().catch(console.error);