#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const os = require('os');

console.log('🔍 Debugging CouchDB macOS Service Issue...\n');

const userDataPath = path.join(os.homedir(), 'Library', 'Application Support', 'Bistro');
const configDir = path.join(userDataPath, 'couchdb-service');
const dataDir = path.join(userDataPath, 'couchdb-data');
const installDir = path.join(userDataPath, 'couchdb-install');

function checkPath(name, filePath) {
  const exists = fs.existsSync(filePath);
  console.log(`${exists ? '✅' : '❌'} ${name}: ${filePath}`);
  if (exists && fs.statSync(filePath).isFile()) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.length < 500) {
        console.log(`   Content preview: ${content.substring(0, 200)}...`);
      } else {
        console.log(`   File size: ${content.length} bytes`);
      }
    } catch (e) {
      console.log(`   Error reading file: ${e.message}`);
    }
  }
  return exists;
}

function findCouchDBInstall() {
  console.log('\n🔍 Looking for CouchDB installation...');
  
  // Check for bundled CouchDB
  const possiblePaths = [
    path.join(installDir, 'couchdb-darwin-v1.0.0'),
    path.join(__dirname, '..', 'electron', 'resources', 'couchdb-macos'),
  ];
  
  for (const couchdbPath of possiblePaths) {
    console.log(`\n📁 Checking: ${couchdbPath}`);
    if (fs.existsSync(couchdbPath)) {
      console.log('✅ Directory exists');
      
      const binPath = path.join(couchdbPath, 'bin', 'couchdb');
      const binExists = fs.existsSync(binPath);
      console.log(`${binExists ? '✅' : '❌'} CouchDB binary: ${binPath}`);
      
      if (binExists) {
        try {
          console.log('🧪 Testing CouchDB binary...');
          const output = execSync(`"${binPath}" -h`, { encoding: 'utf8', timeout: 5000 });
          console.log('✅ Binary test passed');
          console.log('   Help output:', output.substring(0, 200) + '...');
          return binPath;
        } catch (e) {
          console.log('❌ Binary test failed:', e.message);
        }
      }
    } else {
      console.log('❌ Directory not found');
    }
  }
  
  return null;
}

function checkServiceFiles() {
  console.log('\n🔍 Checking service files...');
  
  const plistPath = path.join(os.homedir(), 'Library/LaunchAgents/com.bistro.couchdb.plist');
  const configPath = path.join(configDir, 'local.ini');
  
  checkPath('LaunchAgent plist', plistPath);
  checkPath('CouchDB config', configPath);
  
  // Check if service is loaded
  try {
    const output = execSync('launchctl list | grep bistro', { encoding: 'utf8' });
    console.log('✅ Service status:', output.trim());
  } catch (e) {
    console.log('❌ Service not found in launchctl list');
  }
}

function checkLogs() {
  console.log('\n📋 Checking logs...');
  
  const logPaths = [
    path.join(dataDir, 'couchdb.out.log'),
    path.join(dataDir, 'couchdb.err.log'),
    path.join(dataDir, 'couchdb.log')
  ];
  
  for (const logPath of logPaths) {
    if (fs.existsSync(logPath)) {
      console.log(`\n📄 ${path.basename(logPath)}:`);
      try {
        const content = fs.readFileSync(logPath, 'utf8');
        const lines = content.split('\n').slice(-10); // Last 10 lines
        lines.forEach(line => {
          if (line.trim()) {
            console.log(`   ${line}`);
          }
        });
      } catch (e) {
        console.log(`   Error reading log: ${e.message}`);
      }
    } else {
      console.log(`❌ Log not found: ${logPath}`);
    }
  }
}

function testCouchDBCommand() {
  console.log('\n🧪 Testing CouchDB command variations...');
  
  const couchdbBinary = findCouchDBInstall();
  if (!couchdbBinary) {
    console.log('❌ No CouchDB binary found to test');
    return;
  }
  
  const configPath = path.join(configDir, 'local.ini');
  if (!fs.existsSync(configPath)) {
    console.log('❌ No config file found to test');
    return;
  }
  
  const testCommands = [
    `"${couchdbBinary}" -h`,
    `"${couchdbBinary}" -couch_ini "${configPath}"`,
    `"${couchdbBinary}" -config "${configPath}"`,
    `"${couchdbBinary}" -ini "${configPath}"`
  ];
  
  for (const cmd of testCommands) {
    console.log(`\n🔧 Testing: ${cmd}`);
    try {
      const output = execSync(cmd, { 
        encoding: 'utf8', 
        timeout: 3000,
        stdio: 'pipe'
      });
      console.log('✅ Command succeeded');
      if (output) {
        console.log('   Output:', output.substring(0, 200));
      }
    } catch (e) {
      console.log('❌ Command failed:', e.message);
      if (e.stdout) {
        console.log('   Stdout:', e.stdout.substring(0, 200));
      }
      if (e.stderr) {
        console.log('   Stderr:', e.stderr.substring(0, 200));
      }
    }
  }
}

async function main() {
  console.log('📊 System Info:');
  console.log(`   macOS Version: ${os.release()}`);
  console.log(`   User: ${os.userInfo().username}`);
  console.log(`   Home: ${os.homedir()}`);
  
  checkServiceFiles();
  checkLogs();
  testCouchDBCommand();
  
  console.log('\n💡 Next steps:');
  console.log('1. Check the error logs above for specific CouchDB startup issues');
  console.log('2. Verify the CouchDB binary has proper permissions');
  console.log('3. Test different command line arguments');
  console.log('4. Check if the config file has the correct admin credentials');
}

main().catch(console.error);
