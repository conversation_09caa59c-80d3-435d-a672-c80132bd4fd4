# Download and extract proper CouchDB for Windows
# This script downloads the official CouchDB Windows build and extracts it to the correct location

param(
    [string]$TargetDir = "../electron/resources/couchdb-windows",
    [string]$CouchDBVersion = "3.5.0",
    [string]$TempDir = "./temp-couchdb-download"
)

Write-Host "=== CouchDB Windows Download Script ===" -ForegroundColor Green
Write-Host "Downloading CouchDB $CouchDBVersion for Windows..." -ForegroundColor Yellow

# Create temp directory
if (Test-Path $TempDir) {
    Remove-Item $TempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $TempDir | Out-Null

# CouchDB Windows download URL (official release)
$CouchDBUrl = "https://archive.apache.org/dist/couchdb/binary/win/3.5.0/apache-couchdb-3.5.0-1.exe"
$InstallerPath = Join-Path $TempDir "couchdb-installer.exe"

Write-Host "Downloading from: $CouchDBUrl" -ForegroundColor Cyan

try {
    # Download the installer
    Invoke-WebRequest -Uri $CouchDBUrl -OutFile $InstallerPath -UseBasicParsing
    Write-Host "✅ Download completed!" -ForegroundColor Green
    
    # Since this is an NSIS installer, we need to extract it using 7zip or similar
    # First, try to find 7zip
    $SevenZipPaths = @(
        "C:\Program Files\7-Zip\7z.exe",
        "C:\Program Files (x86)\7-Zip\7z.exe",
        "${env:ProgramFiles}\7-Zip\7z.exe",
        "${env:ProgramFiles(x86)}\7-Zip\7z.exe"
    )
    
    $SevenZip = $null
    foreach ($path in $SevenZipPaths) {
        if (Test-Path $path) {
            $SevenZip = $path
            break
        }
    }
    
    if (-not $SevenZip) {
        Write-Host "❌ 7-Zip not found. Installing 7-Zip via winget..." -ForegroundColor Red
        winget install --id 7zip.7zip --silent --accept-package-agreements --accept-source-agreements
        
        # Check again after installation
        foreach ($path in $SevenZipPaths) {
            if (Test-Path $path) {
                $SevenZip = $path
                break
            }
        }
        
        if (-not $SevenZip) {
            throw "7-Zip installation failed or not found. Please install 7-Zip manually."
        }
    }
    
    Write-Host "Using 7-Zip: $SevenZip" -ForegroundColor Cyan
    
    # Extract the installer
    $ExtractPath = Join-Path $TempDir "extracted"
    New-Item -ItemType Directory -Path $ExtractPath | Out-Null
    
    Write-Host "Extracting installer..." -ForegroundColor Yellow
    & $SevenZip x $InstallerPath "-o$ExtractPath" -y | Out-Null
    
    # The CouchDB files should be in the extracted directory
    # Look for the main CouchDB directory structure
    $CouchDBSources = @(
        Join-Path $ExtractPath "\$PLUGINSDIR\app-64.7z",
        Join-Path $ExtractPath "\$PLUGINSDIR\app-32.7z",
        Join-Path $ExtractPath "app-64.7z",
        Join-Path $ExtractPath "app-32.7z"
    )
    
    $CouchDBArchive = $null
    foreach ($source in $CouchDBSources) {
        if (Test-Path $source) {
            $CouchDBArchive = $source
            Write-Host "Found CouchDB archive: $CouchDBArchive" -ForegroundColor Green
            break
        }
    }
    
    if (-not $CouchDBArchive) {
        # List what we actually extracted to help debug
        Write-Host "Available files in extraction:" -ForegroundColor Yellow
        Get-ChildItem $ExtractPath -Recurse | Select-Object FullName | Format-Table -AutoSize
        throw "Could not find CouchDB application archive in installer"
    }
    
    # Extract the CouchDB application
    $CouchDBExtractPath = Join-Path $TempDir "couchdb"
    New-Item -ItemType Directory -Path $CouchDBExtractPath | Out-Null
    
    Write-Host "Extracting CouchDB application..." -ForegroundColor Yellow
    & $SevenZip x $CouchDBArchive "-o$CouchDBExtractPath" -y | Out-Null
    
    # Find the actual CouchDB directory
    $CouchDBDirs = Get-ChildItem $CouchDBExtractPath -Directory | Where-Object { $_.Name -like "*couchdb*" -or $_.Name -eq "CouchDB" }
    
    if ($CouchDBDirs.Count -eq 0) {
        # Maybe the files are directly in the extract path
        $CouchDBDir = $CouchDBExtractPath
    } else {
        $CouchDBDir = $CouchDBDirs[0].FullName
    }
    
    Write-Host "CouchDB directory: $CouchDBDir" -ForegroundColor Cyan
    
    # Verify we have the correct structure
    $RequiredPaths = @("bin", "erts-*", "lib", "releases")
    foreach ($required in $RequiredPaths) {
        $paths = Get-ChildItem $CouchDBDir -Directory -Name $required
        if ($paths.Count -eq 0) {
            Write-Host "⚠️ Missing required directory: $required" -ForegroundColor Yellow
        } else {
            Write-Host "✅ Found: $required" -ForegroundColor Green
        }
    }
    
    # Check for Windows executables in bin
    $BinPath = Join-Path $CouchDBDir "bin"
    if (Test-Path $BinPath) {
        $ExeFiles = Get-ChildItem $BinPath -Filter "*.exe" -Name
        $CmdFiles = Get-ChildItem $BinPath -Filter "*.cmd" -Name
        Write-Host "Found executables: $($ExeFiles -join ', ')" -ForegroundColor Green
        Write-Host "Found batch files: $($CmdFiles -join ', ')" -ForegroundColor Green
    }
    
    # Remove existing target directory and copy new files
    $TargetPath = Resolve-Path $TargetDir -ErrorAction SilentlyContinue
    if ($TargetPath) {
        Write-Host "Removing existing CouchDB Windows files..." -ForegroundColor Yellow
        Remove-Item $TargetPath -Recurse -Force
    }
    
    Write-Host "Copying new CouchDB files to $TargetDir..." -ForegroundColor Yellow
    Copy-Item $CouchDBDir $TargetDir -Recurse -Force
    
    Write-Host "✅ CouchDB Windows files successfully updated!" -ForegroundColor Green
    Write-Host "Location: $TargetDir" -ForegroundColor Cyan
    
    # Verify the installation
    $BinPath = Join-Path $TargetDir "bin"
    $CouchDBCmd = Join-Path $BinPath "couchdb.cmd"
    
    if (Test-Path $CouchDBCmd) {
        Write-Host "✅ couchdb.cmd found at: $CouchDBCmd" -ForegroundColor Green
    } else {
        Write-Host "❌ couchdb.cmd not found! Installation may be incomplete." -ForegroundColor Red
    }

} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    # Cleanup
    if (Test-Path $TempDir) {
        Write-Host "Cleaning up temporary files..." -ForegroundColor Yellow
        Remove-Item $TempDir -Recurse -Force -ErrorAction SilentlyContinue
    }
}

Write-Host "=== Download Complete ===" -ForegroundColor Green
Write-Host "You can now rebuild your Electron app with proper Windows CouchDB binaries." -ForegroundColor Cyan