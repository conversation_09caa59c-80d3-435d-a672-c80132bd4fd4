#!/bin/bash

echo "🔥 Getting REAL CouchDB binaries for your app"

RESOURCES_DIR="./electron/resources"

# Try Homebrew approach but copy the binaries instead of system install
echo "🍺 Using Homebrew to get proper macOS binaries..."

# Check if CouchDB is available via Homebrew
if command -v brew &> /dev/null; then
    echo "📦 Downloading CouchDB via Homebrew (no install)..."
    
    # Download but don't install
    brew fetch couchdb
    
    # Find where Homebrew cached it
    COUCHDB_CACHE=$(brew --cache couchdb)
    echo "📁 CouchDB cache: $COUCHDB_CACHE"
    
    if [ -f "$COUCHDB_CACHE" ]; then
        TEMP_DIR=$(mktemp -d)
        cd "$TEMP_DIR"
        
        echo "📦 Extracting CouchDB from Homebrew cache..."
        tar -xf "$COUCHDB_CACHE"
        
        # Find the extracted directory
        EXTRACTED_DIR=$(find . -name "apache-couchdb-*" -type d | head -1)
        
        if [ -n "$EXTRACTED_DIR" ]; then
            echo "✅ Found extracted CouchDB: $EXTRACTED_DIR"
            
            # Clean and setup
            rm -rf "$RESOURCES_DIR/couchdb-macos"
            mkdir -p "$RESOURCES_DIR/couchdb-macos"
            
            # Copy everything
            cp -R "$EXTRACTED_DIR/"* "$RESOURCES_DIR/couchdb-macos/"
            
            # Fix permissions
            chmod -R +x "$RESOURCES_DIR/couchdb-macos/bin"
            
            echo "✅ macOS CouchDB binaries ready!"
            
            # Verify
            echo "🔍 Verifying binaries:"
            ls -la "$RESOURCES_DIR/couchdb-macos/bin/"
            
        else
            echo "❌ Could not find extracted CouchDB"
        fi
        
        cd - > /dev/null
        rm -rf "$TEMP_DIR"
    else
        echo "❌ Could not find CouchDB in Homebrew cache"
    fi
else
    echo "❌ Homebrew not found"
    echo "💡 Alternative: Download from GitHub releases"
    
    # Try GitHub releases
    echo "🐙 Trying GitHub releases..."
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    
    # Download latest source and build location info
    curl -s https://api.github.com/repos/apache/couchdb/releases/latest > latest.json
    
    # For now, let's use a direct download link
    echo "📥 Downloading CouchDB 3.3.3 source..."
    curl -L "https://archive.apache.org/dist/couchdb/source/3.3.3/apache-couchdb-3.3.3.tar.gz" -o couchdb.tar.gz
    
    if [ -f "couchdb.tar.gz" ]; then
        echo "📦 Extracting source..."
        tar -xzf couchdb.tar.gz
        
        EXTRACTED_DIR=$(find . -name "apache-couchdb-*" -type d | head -1)
        
        if [ -n "$EXTRACTED_DIR" ]; then
            echo "⚠️  Downloaded source code, but you need pre-built binaries"
            echo "💡 Building from source requires: make, gcc, erlang, etc."
        fi
    fi
    
    cd - > /dev/null
    rm -rf "$TEMP_DIR"
fi

echo ""
echo "🎯 Status:"
if [ -d "$RESOURCES_DIR/couchdb-macos/bin" ]; then
    echo "✅ macOS binaries ready"
else
    echo "❌ macOS binaries missing"
    echo ""
    echo "🆘 QUICKEST SOLUTION:"
    echo "1. Install CouchDB via Homebrew: brew install couchdb"
    echo "2. Copy from: /opt/homebrew/lib/couchdb (or /usr/local/lib/couchdb)"
    echo "3. To your: $RESOURCES_DIR/couchdb-macos/"
fi