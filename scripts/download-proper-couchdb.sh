#!/bin/bash

echo "📥 Downloading proper CouchDB binaries for bundling"

RESOURCES_DIR="./electron/resources"
TEMP_DIR=$(mktemp -d)

echo "🍎 Downloading macOS CouchDB 3.3.3..."
cd "$TEMP_DIR"
curl -L "https://archive.apache.org/dist/couchdb/binary/mac/3.3.3/Apache-CouchDB-3.3.3.zip" -o couchdb-macos.zip

if [ -f "couchdb-macos.zip" ]; then
    echo "📦 Extracting macOS CouchDB..."
    unzip -q couchdb-macos.zip
    
    # Find the extracted app
    EXTRACTED_APP=$(find . -name "Apache CouchDB.app" -type d | head -1)
    
    if [ -n "$EXTRACTED_APP" ]; then
        echo "✅ Found CouchDB app: $EXTRACTED_APP"
        
        # Clean existing resources
        rm -rf "$RESOURCES_DIR/couchdb-macos"
        mkdir -p "$RESOURCES_DIR/couchdb-macos"
        
        # Copy the actual CouchDB installation from the app bundle
        cp -R "$EXTRACTED_APP/Contents/Resources/couchdbx-core/"* "$RESOURCES_DIR/couchdb-macos/"
        
        # Fix permissions
        chmod -R +x "$RESOURCES_DIR/couchdb-macos/bin"
        
        echo "✅ macOS CouchDB binaries ready"
        echo "📁 Location: $RESOURCES_DIR/couchdb-macos"
        
        # Verify structure
        echo "🔍 Verifying structure:"
        ls -la "$RESOURCES_DIR/couchdb-macos/bin/"
        
    else
        echo "❌ Could not find CouchDB app in archive"
    fi
else
    echo "❌ Failed to download macOS CouchDB"
fi

echo "🪟 Downloading Windows CouchDB 3.3.3..."
curl -L "https://archive.apache.org/dist/couchdb/binary/win/3.3.3/apache-couchdb-3.3.3-1.x86_64.msi" -o couchdb-windows.msi

if [ -f "couchdb-windows.msi" ]; then
    echo "📦 Windows MSI downloaded (manual extraction needed)"
    echo "⚠️  You'll need to extract the MSI manually to get Windows binaries"
else
    echo "❌ Failed to download Windows CouchDB"
fi

# Cleanup
cd - > /dev/null
rm -rf "$TEMP_DIR"

echo ""
echo "🎯 Next steps:"
echo "1. Test macOS binaries: npm run electron:dev"
echo "2. For Windows: extract the MSI manually to get proper binaries"