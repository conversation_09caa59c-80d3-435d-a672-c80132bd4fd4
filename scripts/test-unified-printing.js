#!/usr/bin/env node

/**
 * Test script for the unified printing system
 * Run with: node scripts/test-unified-printing.js
 */

console.log('🖨️ Testing Unified Printing System');
console.log('==================================');

// Simulated test scenarios
const testScenarios = [
  {
    name: 'Receipt Print Test',
    job: {
      id: 'test-receipt-001',
      title: 'Test Receipt #123',
      content: `
==================
TEST RESTAURANT
==================
Order #123
Table 5

1x Burger         $12.99
1x Fries          $4.99
1x Soda           $2.99
------------------
Subtotal:        $20.97
Tax:              $2.10
------------------
TOTAL:           $23.07

Payment: Card
Thank you!
==================`,
      type: 'receipt',
      printerName: 'Print_to_PDF____MacBook_Air',
      copies: 1
    }
  },
  {
    name: 'Kitchen Order Test',
    job: {
      id: 'test-kitchen-001',
      title: 'Kitchen Order #123',
      content: `
========================
KITCHEN ORDER #123
========================
Table: 5
Time: ${new Date().toLocaleString()}

[ MAINS ]
1x Burger (Medium)
   - No pickles
   - Extra cheese

1x Chicken Wings (12pc)
   - BBQ sauce

[ SIDES ]  
2x French Fries
1x Onion Rings

[ DRINKS ]
1x Coca Cola
1x Orange Juice

========================
TOTAL ITEMS: 5
========================`,
      type: 'kitchen',
      printerName: 'Print_to_PDF____MacBook_Air',
      copies: 1
    }
  }
];

// Test electron API availability
function testElectronAPI() {
  console.log('\n📊 Environment Check:');
  
  if (typeof window !== 'undefined') {
    const hasElectron = !!(window.electronAPI);
    console.log(`   Electron API: ${hasElectron ? '✅ Available' : '❌ Not Available'}`);
    
    if (hasElectron) {
      const api = window.electronAPI;
      console.log(`   Printing API: ${api.printing ? '✅ Available' : '❌ Missing'}`);
      console.log(`   Reliable Print: ${api.printing && api.printing.reliablePrint ? '✅ Available' : '❌ Missing'}`);
    }
  } else {
    console.log('   Environment: Node.js (SSR)');
  }
}

// Test print job creation
function testJobCreation() {
  console.log('\n🔧 Job Creation Tests:');
  
  testScenarios.forEach((scenario, index) => {
    console.log(`   ${index + 1}. ${scenario.name}:`);
    console.log(`      ID: ${scenario.job.id}`);
    console.log(`      Type: ${scenario.job.type}`);
    console.log(`      Content Length: ${scenario.job.content.length} chars`);
    console.log(`      Target Printer: ${scenario.job.printerName}`);
  });
}

// Test HTML generation
function testHTMLGeneration() {
  console.log('\n🎨 HTML Generation Test:');
  
  const job = testScenarios[0].job;
  const width = job.type === 'receipt' ? '58mm' : '80mm';
  
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>${job.title}</title>
      <style>
        @page {
          size: ${width} auto;
          margin: 0;
        }
        body {
          margin: 0;
          padding: 3mm;
          font-family: 'Courier New', monospace;
          font-size: 11px;
          line-height: 1.3;
          color: #000;
          background: #fff;
          width: ${width};
          box-sizing: border-box;
        }
        .print-header {
          text-align: center;
          font-weight: bold;
          font-size: 13px;
          margin-bottom: 8px;
          text-transform: uppercase;
        }
        .print-content {
          white-space: pre-wrap;
          word-wrap: break-word;
          font-size: 11px;
        }
        .print-footer {
          text-align: center;
          margin-top: 8px;
          font-size: 10px;
          border-top: 1px dashed #000;
          padding-top: 5px;
        }
      </style>
    </head>
    <body>
      <div class="print-header">
        ${job.type.toUpperCase()}
      </div>
      <div class="print-content">${job.content}</div>
      <div class="print-footer">
        ${new Date().toLocaleString()}
      </div>
    </body>
    </html>
  `;
  
  console.log(`   ✅ HTML generated successfully`);
  console.log(`   📄 Size: ${htmlContent.length} characters`);
  console.log(`   📐 Paper width: ${width}`);
}

// Main test function
function runTests() {
  console.log('\n🚀 Starting Unified Print System Tests...\n');
  
  testElectronAPI();
  testJobCreation(); 
  testHTMLGeneration();
  
  console.log('\n✅ All Tests Completed!');
  console.log('\n💡 Next Steps:');
  console.log('   1. Run the Electron app: npm run electron:dev');
  console.log('   2. Go to Kitchen Settings');
  console.log('   3. Try the test buttons');
  console.log('   4. Create some orders and test printing');
}

// Run the tests
runTests();