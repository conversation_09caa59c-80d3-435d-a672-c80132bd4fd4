#!/bin/bash

# Create Self-Signed Certificate for macOS App Signing
# This creates a certificate in the keychain for signing the app

set -e

CERT_NAME="Bistro Developer"
KEYCHAIN_NAME="login"

echo "🔐 Creating self-signed certificate for app signing..."

# Check if certificate already exists
if security find-certificate -c "$CERT_NAME" "$KEYCHAIN_NAME" &>/dev/null; then
    echo "✅ Certificate '$CERT_NAME' already exists in keychain"
    echo "   To recreate, delete it first from Keychain Access app"
    exit 0
fi

# Create certificate
echo "📝 Creating certificate '$CERT_NAME'..."

# Create certificate configuration
cat > /tmp/cert.conf << EOF
[req]
distinguished_name = req_distinguished_name
x509_extensions = v3_ca
prompt = no

[req_distinguished_name]
CN = $CERT_NAME
O = Bistro Development
C = US

[v3_ca]
basicConstraints = CA:true
keyUsage = digitalSignature, keyEncipherment, keyCertSign
extendedKeyUsage = codeSigning
EOF

# Generate private key and certificate
openssl req -new -x509 -days 3650 -nodes -config /tmp/cert.conf \
    -keyout /tmp/cert.key -out /tmp/cert.crt

# Import into keychain
security import /tmp/cert.crt -T /usr/bin/codesign
security import /tmp/cert.key -T /usr/bin/codesign

# Set trust settings
security add-trusted-cert -d -r trustRoot /tmp/cert.crt

# Allow codesign to use the key without prompting
security set-key-partition-list -S apple-tool:,apple:,codesign: -s -k "" &>/dev/null || true

# Clean up
rm -f /tmp/cert.conf /tmp/cert.key /tmp/cert.crt

echo "✅ Self-signed certificate created successfully!"
echo "📋 Certificate Name: $CERT_NAME"
echo "🔧 Update electron-builder config to use this certificate:"
echo "   \"identity\": \"$CERT_NAME\""