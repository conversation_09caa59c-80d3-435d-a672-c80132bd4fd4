#!/bin/bash

echo "🪟 Extracting Windows CouchDB from Docker..."

RESOURCES_DIR="./electron/resources"
COUCHDB_VERSION="3.5"

# Create Windows CouchDB directory
mkdir -p "$RESOURCES_DIR/couchdb-windows"

echo "📦 Creating Windows container to extract CouchDB..."

# Use Windows Server Core image with CouchDB
# Note: This might take a while on first run as it downloads Windows image
docker run --platform linux/amd64 --name temp-couchdb-win couchdb:$COUCHDB_VERSION echo "Container created"

echo "📁 Copying CouchDB files from container..."
docker cp temp-couchdb-win:/opt/couchdb "$RESOURCES_DIR/temp-couchdb-win"

# Move to proper location
mv "$RESOURCES_DIR/temp-couchdb-win"/* "$RESOURCES_DIR/couchdb-windows/"
rmdir "$RESOURCES_DIR/temp-couchdb-win"

# Create Windows-specific files (since we're using Linux container)
mkdir -p "$RESOURCES_DIR/couchdb-windows/bin"
cp "$RESOURCES_DIR/couchdb-macos/bin/couchjs" "$RESOURCES_DIR/couchdb-windows/bin/couchjs.exe"

# Create Windows batch file
cat > "$RESOURCES_DIR/couchdb-windows/bin/couchdb.cmd" << 'EOF'
@echo off
REM CouchDB Windows Batch File
cd /d "%~dp0.."
bin\erl.exe -boot releases\couchdb -args_file etc\vm.args -config releases\sys.config %*
EOF

# Cleanup container
docker rm temp-couchdb-win

echo "✅ Windows CouchDB extracted to $RESOURCES_DIR/couchdb-windows"
echo ""
echo "📝 Next: Test both platforms work:"
echo "  npm run electron:dev"