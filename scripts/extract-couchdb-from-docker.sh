#!/bin/bash

echo "🐳 CouchDB Docker Extraction Script"
echo "This will extract CouchDB binaries from Docker images"
echo ""

RESOURCES_DIR="../electron/resources"
COUCHDB_VERSION="3.5"

# Create resources directory
mkdir -p "$RESOURCES_DIR"

echo "📦 Extracting CouchDB for macOS/Linux..."

# Create temporary container and copy CouchDB files
docker run --name temp-couchdb couchdb:$COUCHDB_VERSION echo "Container created"

# Create target directory
mkdir -p "$RESOURCES_DIR/couchdb-macos"

# Copy CouchDB installation from container
echo "📁 Copying CouchDB files..."
docker cp temp-couchdb:/opt/couchdb "$RESOURCES_DIR/couchdb-macos/"

# Cleanup container
docker rm temp-couchdb

echo "✅ CouchDB extracted to $RESOURCES_DIR/couchdb-macos"
echo ""
echo "📝 Next steps:"
echo "1. For Windows, you'll need to manually download Windows CouchDB"
echo "2. Copy the extracted files to create the proper structure"
echo "3. Test the extraction with npm run build"

echo ""
echo "🔧 Required structure:"
echo "$RESOURCES_DIR/"
echo "├── couchdb-macos/"
echo "│   ├── bin/"
echo "│   ├── etc/"
echo "│   ├── lib/"
echo "│   └── releases/"
echo "└── couchdb-windows/"
echo "    ├── bin/"
echo "    ├── etc/"
echo "    ├── lib/"
echo "    └── releases/"