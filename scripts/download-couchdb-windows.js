#!/usr/bin/env node

/**
 * Download and extract proper CouchDB for Windows
 * This script downloads the official CouchDB Windows build and extracts it
 */

const https = require('https');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const TARGET_DIR = path.join(__dirname, '..', 'electron', 'resources', 'couchdb-windows');
const TEMP_DIR = path.join(__dirname, 'temp-couchdb-download');
const COUCHDB_VERSION = '3.5.0';

// CouchDB Windows download URL (official binary)
const COUCHDB_URL = `https://archive.apache.org/dist/couchdb/binary/win/${COUCHDB_VERSION}/apache-couchdb-${COUCHDB_VERSION}-1.exe`;

console.log('=== CouchDB Windows Download Script ===');
console.log(`Downloading CouchDB ${COUCHDB_VERSION} for Windows...`);

async function downloadFile(url, dest) {
    return new Promise((resolve, reject) => {
        const file = fs.createWriteStream(dest);
        
        https.get(url, (response) => {
            if (response.statusCode === 302 || response.statusCode === 301) {
                // Handle redirects
                return downloadFile(response.headers.location, dest).then(resolve).catch(reject);
            }
            
            if (response.statusCode !== 200) {
                reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
                return;
            }
            
            const totalSize = parseInt(response.headers['content-length'], 10);
            let downloadedSize = 0;
            
            response.on('data', (chunk) => {
                downloadedSize += chunk.length;
                if (totalSize) {
                    const percent = ((downloadedSize / totalSize) * 100).toFixed(1);
                    process.stdout.write(`\rProgress: ${percent}% (${downloadedSize}/${totalSize} bytes)`);
                }
            });
            
            response.pipe(file);
            
            file.on('finish', () => {
                file.close();
                console.log('\n✅ Download completed!');
                resolve();
            });
        }).on('error', (err) => {
            fs.unlink(dest, () => {}); // Delete incomplete file
            reject(err);
        });
    });
}

function executeCommand(command, options = {}) {
    try {
        return execSync(command, { encoding: 'utf8', ...options });
    } catch (error) {
        throw new Error(`Command failed: ${command}\nError: ${error.message}`);
    }
}

async function main() {
    try {
        // Create temp directory
        if (fs.existsSync(TEMP_DIR)) {
            fs.rmSync(TEMP_DIR, { recursive: true, force: true });
        }
        fs.mkdirSync(TEMP_DIR, { recursive: true });
        
        const installerPath = path.join(TEMP_DIR, 'couchdb-installer.exe');
        
        console.log(`Downloading from: ${COUCHDB_URL}`);
        await downloadFile(COUCHDB_URL, installerPath);
        
        // Check if we have 7zip available
        let sevenZipCmd = null;
        const sevenZipPaths = [
            'C:\\Program Files\\7-Zip\\7z.exe',
            'C:\\Program Files (x86)\\7-Zip\\7z.exe',
            '7z' // If in PATH
        ];
        
        for (const sevenZipPath of sevenZipPaths) {
            try {
                executeCommand(`"${sevenZipPath}" --help`, { stdio: 'ignore' });
                sevenZipCmd = sevenZipPath;
                break;
            } catch (e) {
                // Try next path
            }
        }
        
        if (!sevenZipCmd) {
            console.log('❌ 7-Zip not found. Please install 7-Zip first:');
            console.log('   Download from: https://www.7-zip.org/download.html');
            console.log('   Or run: winget install 7zip.7zip');
            process.exit(1);
        }
        
        console.log(`Using 7-Zip: ${sevenZipCmd}`);
        
        // Extract the installer
        const extractPath = path.join(TEMP_DIR, 'extracted');
        fs.mkdirSync(extractPath, { recursive: true });
        
        console.log('Extracting installer...');
        executeCommand(`"${sevenZipCmd}" x "${installerPath}" -o"${extractPath}" -y`);
        
        // Look for the CouchDB application archive
        const possibleArchives = [
            path.join(extractPath, '$PLUGINSDIR', 'app-64.7z'),
            path.join(extractPath, '$PLUGINSDIR', 'app-32.7z'),
            path.join(extractPath, 'app-64.7z'),
            path.join(extractPath, 'app-32.7z')
        ];
        
        let couchdbArchive = null;
        for (const archive of possibleArchives) {
            if (fs.existsSync(archive)) {
                couchdbArchive = archive;
                console.log(`Found CouchDB archive: ${archive}`);
                break;
            }
        }
        
        if (!couchdbArchive) {
            console.log('Available files in extraction:');
            const files = fs.readdirSync(extractPath, { recursive: true });
            files.forEach(file => console.log(`  ${file}`));
            throw new Error('Could not find CouchDB application archive in installer');
        }
        
        // Extract the CouchDB application
        const couchdbExtractPath = path.join(TEMP_DIR, 'couchdb');
        fs.mkdirSync(couchdbExtractPath, { recursive: true });
        
        console.log('Extracting CouchDB application...');
        executeCommand(`"${sevenZipCmd}" x "${couchdbArchive}" -o"${couchdbExtractPath}" -y`);
        
        // Find the actual CouchDB directory
        const extractedItems = fs.readdirSync(couchdbExtractPath);
        let couchdbDir = couchdbExtractPath;
        
        // Look for a subdirectory that looks like CouchDB
        for (const item of extractedItems) {
            const itemPath = path.join(couchdbExtractPath, item);
            if (fs.statSync(itemPath).isDirectory() && 
                (item.toLowerCase().includes('couchdb') || item === 'CouchDB')) {
                couchdbDir = itemPath;
                break;
            }
        }
        
        console.log(`CouchDB directory: ${couchdbDir}`);
        
        // Verify we have the correct structure
        const requiredDirs = ['bin', 'lib', 'releases'];
        for (const required of requiredDirs) {
            const requiredPath = path.join(couchdbDir, required);
            if (fs.existsSync(requiredPath)) {
                console.log(`✅ Found: ${required}`);
            } else {
                console.log(`⚠️ Missing required directory: ${required}`);
            }
        }
        
        // Check for Windows executables in bin
        const binPath = path.join(couchdbDir, 'bin');
        if (fs.existsSync(binPath)) {
            const binFiles = fs.readdirSync(binPath);
            const exeFiles = binFiles.filter(f => f.endsWith('.exe'));
            const cmdFiles = binFiles.filter(f => f.endsWith('.cmd'));
            console.log(`Found executables: ${exeFiles.join(', ')}`);
            console.log(`Found batch files: ${cmdFiles.join(', ')}`);
        }
        
        // Remove existing target directory and copy new files
        if (fs.existsSync(TARGET_DIR)) {
            console.log('Removing existing CouchDB Windows files...');
            fs.rmSync(TARGET_DIR, { recursive: true, force: true });
        }
        
        console.log(`Copying new CouchDB files to ${TARGET_DIR}...`);
        fs.cpSync(couchdbDir, TARGET_DIR, { recursive: true });
        
        console.log('✅ CouchDB Windows files successfully updated!');
        console.log(`Location: ${TARGET_DIR}`);
        
        // Verify the installation
        const couchdbCmd = path.join(TARGET_DIR, 'bin', 'couchdb.cmd');
        if (fs.existsSync(couchdbCmd)) {
            console.log(`✅ couchdb.cmd found at: ${couchdbCmd}`);
        } else {
            console.log('❌ couchdb.cmd not found! Installation may be incomplete.');
        }
        
    } catch (error) {
        console.error(`❌ Error: ${error.message}`);
        process.exit(1);
    } finally {
        // Cleanup
        if (fs.existsSync(TEMP_DIR)) {
            console.log('Cleaning up temporary files...');
            fs.rmSync(TEMP_DIR, { recursive: true, force: true });
        }
    }
    
    console.log('=== Download Complete ===');
    console.log('You can now rebuild your Electron app with proper Windows CouchDB binaries.');
}

main().catch(console.error);