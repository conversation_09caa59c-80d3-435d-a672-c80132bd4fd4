#!/bin/bash

echo "🚀 FASTEST SOLUTION: Copy CouchDB from Homebrew"
echo "This will temporarily install CouchDB, copy binaries, then clean up"

RESOURCES_DIR="./electron/resources"

read -p "🤔 Install CouchDB temporarily to get binaries? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Cancelled"
    exit 1
fi

echo "📦 Installing CouchDB via Homebrew..."
brew install couchdb

echo "📁 Finding CouchDB installation..."
COUCHDB_PATH=""

# Common Homebrew paths
POSSIBLE_PATHS=(
    "/opt/homebrew/lib/couchdb"
    "/usr/local/lib/couchdb" 
    "/opt/homebrew/Cellar/couchdb"
    "/usr/local/Cellar/couchdb"
)

for path in "${POSSIBLE_PATHS[@]}"; do
    if [ -d "$path" ]; then
        # Find the actual version directory
        VERSION_DIR=$(find "$path" -name "*" -type d -maxdepth 1 | head -1)
        if [ -n "$VERSION_DIR" ] && [ -d "$VERSION_DIR/bin" ]; then
            COUCHDB_PATH="$VERSION_DIR"
            break
        elif [ -d "$path/bin" ]; then
            COUCHDB_PATH="$path"
            break
        fi
    fi
done

if [ -z "$COUCHDB_PATH" ]; then
    echo "❌ Could not find CouchDB installation"
    echo "🔍 Searching manually..."
    find /opt/homebrew /usr/local -name "couchdb" -type f 2>/dev/null | head -5
    exit 1
fi

echo "✅ Found CouchDB at: $COUCHDB_PATH"

# Clean existing
rm -rf "$RESOURCES_DIR/couchdb-macos"
mkdir -p "$RESOURCES_DIR/couchdb-macos"

echo "📋 Copying CouchDB binaries..."
cp -R "$COUCHDB_PATH/"* "$RESOURCES_DIR/couchdb-macos/"

# Fix permissions
chmod -R +x "$RESOURCES_DIR/couchdb-macos/bin"

echo "🧹 Cleaning up (uninstalling Homebrew CouchDB)..."
brew uninstall couchdb

echo ""
echo "✅ SUCCESS!"
echo "📁 CouchDB binaries copied to: $RESOURCES_DIR/couchdb-macos"
echo "🔍 Verifying:"
ls -la "$RESOURCES_DIR/couchdb-macos/bin/"

echo ""
echo "🎯 Now test with: npm run electron:dev"