#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync } = require('child_process');

console.log('🪟 Fixing Windows CouchDB Bundle...');

const RESOURCES_DIR = path.join(__dirname, '..', 'electron', 'resources');
const COUCHDB_WINDOWS_DIR = path.join(RESOURCES_DIR, 'couchdb-windows');
const TEMP_DIR = path.join(__dirname, '..', 'temp-couchdb-fix');

// CouchDB Windows MSI download URL
const COUCHDB_VERSION = '3.3.3';
const COUCHDB_MSI_URL = `https://archive.apache.org/dist/couchdb/binary/win/${COUCHDB_VERSION}/apache-couchdb-${COUCHDB_VERSION}.msi`;

async function downloadFile(url, dest) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(dest);
    https.get(url, (response) => {
      if (response.statusCode === 302 || response.statusCode === 301) {
        return downloadFile(response.headers.location, dest).then(resolve).catch(reject);
      }
      
      if (response.statusCode !== 200) {
        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
        return;
      }
      
      const totalSize = parseInt(response.headers['content-length'], 10);
      let downloadedSize = 0;
      
      response.on('data', (chunk) => {
        downloadedSize += chunk.length;
        if (totalSize) {
          const percent = Math.round((downloadedSize / totalSize) * 100);
          process.stdout.write(`\r📥 Downloading: ${percent}%`);
        }
      });
      
      response.pipe(file);
      file.on('finish', () => {
        console.log('\n✅ Download complete');
        file.close();
        resolve();
      });
    }).on('error', reject);
  });
}

function validateCurrentBundle() {
  console.log('🔍 Validating current Windows CouchDB bundle...');
  
  const issues = [];
  
  // Check if directory exists
  if (!fs.existsSync(COUCHDB_WINDOWS_DIR)) {
    issues.push('CouchDB Windows directory does not exist');
    return issues;
  }
  
  // Check critical files
  const criticalFiles = [
    'bin/couchdb.cmd',
    'releases/start_erl.data',
    'etc/vm.args',
    'releases/sys.config'
  ];
  
  for (const file of criticalFiles) {
    const filePath = path.join(COUCHDB_WINDOWS_DIR, file);
    if (!fs.existsSync(filePath)) {
      issues.push(`Missing critical file: ${file}`);
    }
  }
  
  // Check batch file content
  const batchFile = path.join(COUCHDB_WINDOWS_DIR, 'bin', 'couchdb.cmd');
  if (fs.existsSync(batchFile)) {
    const content = fs.readFileSync(batchFile, 'utf8');
    if (content.includes('binrl.exe') || content.length < 50) {
      issues.push('Corrupted batch file (couchdb.cmd)');
    }
  }
  
  // Check ERTS version
  const startErlData = path.join(COUCHDB_WINDOWS_DIR, 'releases', 'start_erl.data');
  if (fs.existsSync(startErlData)) {
    const ertsVersion = fs.readFileSync(startErlData, 'utf8').trim().split(' ')[0];
    const ertsDir = path.join(COUCHDB_WINDOWS_DIR, `erts-${ertsVersion}`);
    const erlExe = path.join(ertsDir, 'bin', 'erl.exe');
    
    if (!fs.existsSync(erlExe)) {
      issues.push(`Missing Erlang executable: erts-${ertsVersion}/bin/erl.exe`);
    }
  }
  
  return issues;
}

function fixBatchFile() {
  console.log('🔧 Fixing Windows batch file...');
  
  const startErlData = path.join(COUCHDB_WINDOWS_DIR, 'releases', 'start_erl.data');
  if (!fs.existsSync(startErlData)) {
    throw new Error('start_erl.data file not found');
  }
  
  const ertsVersion = fs.readFileSync(startErlData, 'utf8').trim().split(' ')[0];
  console.log(`📋 Detected ERTS version: ${ertsVersion}`);
  
  const batchContent = `@echo off
setlocal
REM CouchDB Windows Batch File (robust erts detection)
set ROOTDIR=%~dp0..
for /f "tokens=1" %%v in (%ROOTDIR%\\releases\\start_erl.data) do set ERTS_VSN=%%v
set BINDIR=%ROOTDIR%\\erts-%ERTS_VSN%\\bin
cd /d "%ROOTDIR%"
"%BINDIR%\\erl.exe" -boot "%ROOTDIR%\\releases\\couchdb" -args_file "%ROOTDIR%\\etc\\vm.args" -config "%ROOTDIR%\\releases\\sys.config" %*
endlocal`;
  
  const batchFile = path.join(COUCHDB_WINDOWS_DIR, 'bin', 'couchdb.cmd');
  fs.writeFileSync(batchFile, batchContent);
  console.log('✅ Fixed couchdb.cmd batch file');
}

async function downloadAndExtractCouchDB() {
  console.log('📥 Downloading official Windows CouchDB...');
  
  // Create temp directory
  if (!fs.existsSync(TEMP_DIR)) {
    fs.mkdirSync(TEMP_DIR, { recursive: true });
  }
  
  const msiPath = path.join(TEMP_DIR, 'couchdb.msi');
  
  try {
    await downloadFile(COUCHDB_MSI_URL, msiPath);
  } catch (error) {
    throw new Error(`Failed to download CouchDB: ${error.message}`);
  }
  
  console.log('📦 Extracting MSI...');
  
  // Try different extraction methods
  let extractSuccess = false;
  
  // Method 1: Try msiexec (Windows only)
  if (process.platform === 'win32') {
    try {
      const extractDir = path.join(TEMP_DIR, 'extracted');
      fs.mkdirSync(extractDir, { recursive: true });
      execSync(`msiexec /a "${msiPath}" /qn TARGETDIR="${extractDir}"`, { stdio: 'ignore' });
      extractSuccess = true;
      console.log('✅ Extracted using msiexec');
    } catch (e) {
      console.log('⚠️ msiexec extraction failed, trying alternatives...');
    }
  }
  
  // Method 2: Try 7z
  if (!extractSuccess) {
    try {
      execSync('which 7z', { stdio: 'ignore' });
      execSync(`7z x "${msiPath}" -o"${TEMP_DIR}/extracted" -y`, { stdio: 'ignore' });
      extractSuccess = true;
      console.log('✅ Extracted using 7z');
    } catch (e) {
      console.log('⚠️ 7z extraction failed');
    }
  }
  
  if (!extractSuccess) {
    throw new Error('Could not extract MSI. Please install 7-zip or run on Windows with msiexec.');
  }
  
  // Find CouchDB directory in extracted files
  const extractedDir = path.join(TEMP_DIR, 'extracted');
  const findCouchDBDir = (dir) => {
    const items = fs.readdirSync(dir);
    for (const item of items) {
      const itemPath = path.join(dir, item);
      if (fs.statSync(itemPath).isDirectory()) {
        if (item.toLowerCase().includes('couchdb') || 
            fs.existsSync(path.join(itemPath, 'bin', 'couchdb.cmd'))) {
          return itemPath;
        }
        // Recursively search
        const found = findCouchDBDir(itemPath);
        if (found) return found;
      }
    }
    return null;
  };
  
  const couchdbDir = findCouchDBDir(extractedDir);
  if (!couchdbDir) {
    throw new Error('Could not find CouchDB directory in extracted MSI');
  }
  
  console.log(`📁 Found CouchDB at: ${couchdbDir}`);
  
  // Backup existing directory
  if (fs.existsSync(COUCHDB_WINDOWS_DIR)) {
    const backupDir = `${COUCHDB_WINDOWS_DIR}.backup.${Date.now()}`;
    fs.renameSync(COUCHDB_WINDOWS_DIR, backupDir);
    console.log(`💾 Backed up existing CouchDB to: ${backupDir}`);
  }
  
  // Copy new CouchDB
  fs.cpSync(couchdbDir, COUCHDB_WINDOWS_DIR, { recursive: true });
  console.log('✅ Copied new CouchDB bundle');
  
  // Cleanup
  fs.rmSync(TEMP_DIR, { recursive: true, force: true });
}

async function main() {
  try {
    // Validate current bundle
    const issues = validateCurrentBundle();
    
    if (issues.length === 0) {
      console.log('✅ Windows CouchDB bundle is already valid!');
      return;
    }
    
    console.log('❌ Issues found with current bundle:');
    issues.forEach(issue => console.log(`  - ${issue}`));
    
    // Try to fix batch file first (quick fix)
    if (issues.some(i => i.includes('batch file')) && 
        fs.existsSync(path.join(COUCHDB_WINDOWS_DIR, 'releases', 'start_erl.data'))) {
      try {
        fixBatchFile();
        
        // Re-validate
        const remainingIssues = validateCurrentBundle();
        if (remainingIssues.length === 0) {
          console.log('🎉 Fixed! Windows CouchDB bundle is now valid.');
          return;
        }
      } catch (e) {
        console.log('⚠️ Quick fix failed, will download fresh bundle');
      }
    }
    
    // Download and replace entire bundle
    console.log('🔄 Downloading fresh Windows CouchDB bundle...');
    await downloadAndExtractCouchDB();
    
    // Fix the batch file in the new bundle
    fixBatchFile();
    
    // Final validation
    const finalIssues = validateCurrentBundle();
    if (finalIssues.length === 0) {
      console.log('🎉 Success! Windows CouchDB bundle is now ready for production.');
    } else {
      console.log('❌ Some issues remain:');
      finalIssues.forEach(issue => console.log(`  - ${issue}`));
    }
    
  } catch (error) {
    console.error('❌ Error fixing Windows CouchDB:', error.message);
    process.exit(1);
  }
}

main();
