#!/usr/bin/env node

const https = require('https');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 CouchDB Resource Downloader');
console.log('This script will download and prepare CouchDB binaries for your app\n');

const COUCHDB_VERSION = '3.3.2';
const RESOURCES_DIR = path.join(__dirname, '../electron/resources');

// Ensure resources directory exists
if (!fs.existsSync(RESOURCES_DIR)) {
  fs.mkdirSync(RESOURCES_DIR, { recursive: true });
}

const downloads = [
  {
    platform: 'windows',
    url: `https://archive.apache.org/dist/couchdb/binary/win/3.3.2/apache-couchdb-${COUCHDB_VERSION}-1.x86_64.msi`,
    targetDir: path.join(RESOURCES_DIR, 'couchdb-windows'),
    instructions: 'You will need to manually extract the MSI and copy the CouchDB files'
  },
  {
    platform: 'macos',
    url: `https://archive.apache.org/dist/couchdb/binary/mac/3.3.2/Apache-CouchDB-${COUCHDB_VERSION}.zip`,
    targetDir: path.join(RESOURCES_DIR, 'couchdb-macos'),
    instructions: 'Download and extract the macOS CouchDB application'
  }
];

async function downloadFile(url, outputPath) {
  return new Promise((resolve, reject) => {
    console.log(`📥 Downloading: ${url}`);
    
    const file = fs.createWriteStream(outputPath);
    
    https.get(url, (response) => {
      if (response.statusCode === 302 || response.statusCode === 301) {
        // Handle redirects
        return downloadFile(response.headers.location, outputPath).then(resolve).catch(reject);
      }
      
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download: HTTP ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`✅ Downloaded: ${path.basename(outputPath)}`);
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(outputPath, () => {}); // Delete incomplete file
        reject(err);
      });
    }).on('error', reject);
  });
}

async function main() {
  console.log('\n📋 Available downloads:\n');
  
  downloads.forEach((download, index) => {
    console.log(`${index + 1}. ${download.platform.toUpperCase()}`);
    console.log(`   URL: ${download.url}`);
    console.log(`   Target: ${download.targetDir}`);
    console.log(`   Note: ${download.instructions}\n`);
  });
  
  console.log('🎯 RECOMMENDED APPROACH:');
  console.log('Instead of downloading these large files, you should:');
  console.log('');
  console.log('1. Check if you have a backup of your electron/resources/ folder');
  console.log('2. Look for the original CouchDB binaries you used before');
  console.log('3. Or use the build scripts to create new CouchDB distributions');
  console.log('');
  console.log('If you want to proceed with downloads, run this script with --download flag');
  console.log('Example: node scripts/download-couchdb-resources.js --download');
  
  if (process.argv.includes('--download')) {
    console.log('\n🚀 Starting downloads...\n');
    
    for (const download of downloads) {
      try {
        const downloadPath = path.join(RESOURCES_DIR, `couchdb-${download.platform}-installer`);
        if (!fs.existsSync(downloadPath)) {
          fs.mkdirSync(downloadPath, { recursive: true });
        }
        
        const filename = path.basename(download.url);
        const filePath = path.join(downloadPath, filename);
        
        await downloadFile(download.url, filePath);
        
        console.log(`\n📝 Next steps for ${download.platform}:`);
        console.log(`   1. Extract: ${filePath}`);
        console.log(`   2. Copy CouchDB files to: ${download.targetDir}`);
        console.log(`   3. Ensure bin/, etc/, lib/, releases/ directories are present\n`);
        
      } catch (error) {
        console.error(`❌ Failed to download ${download.platform}:`, error.message);
      }
    }
  }
}

main().catch(console.error);