#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🪟 Setting up Windows Production Environment...');
console.log('This script will prepare all Windows-specific dependencies for production builds.\n');

const RESOURCES_DIR = path.join(__dirname, '..', 'electron', 'resources');

function checkRequirement(name, checkFn, installInstructions) {
  try {
    const result = checkFn();
    if (result) {
      console.log(`✅ ${name}: Available`);
      return true;
    } else {
      console.log(`❌ ${name}: Not available`);
      if (installInstructions) {
        console.log(`   ${installInstructions}`);
      }
      return false;
    }
  } catch (error) {
    console.log(`❌ ${name}: Error checking - ${error.message}`);
    if (installInstructions) {
      console.log(`   ${installInstructions}`);
    }
    return false;
  }
}

function checkNodeJS() {
  try {
    const version = execSync('node --version', { encoding: 'utf8' }).trim();
    console.log(`✅ Node.js: ${version}`);
    return true;
  } catch {
    return false;
  }
}

function checkExtractionTools() {
  const tools = [];
  
  // Check for unzip
  try {
    execSync('which unzip', { stdio: 'ignore' });
    tools.push('unzip');
  } catch {}
  
  // Check for 7z
  try {
    execSync('which 7z', { stdio: 'ignore' });
    tools.push('7z');
  } catch {}
  
  if (tools.length > 0) {
    console.log(`✅ Archive extraction: ${tools.join(', ')} available`);
    return true;
  } else {
    console.log('❌ Archive extraction: No tools available');
    console.log('   Install 7-zip: https://www.7-zip.org/download.html');
    return false;
  }
}

function checkCouchDBBundle() {
  const couchdbDir = path.join(RESOURCES_DIR, 'couchdb-windows');
  const criticalFiles = [
    'bin/couchdb.cmd',
    'releases/start_erl.data',
    'etc/vm.args',
    'releases/sys.config'
  ];
  
  if (!fs.existsSync(couchdbDir)) {
    console.log('❌ CouchDB Windows Bundle: Directory not found');
    return false;
  }
  
  const missingFiles = criticalFiles.filter(file => 
    !fs.existsSync(path.join(couchdbDir, file))
  );
  
  if (missingFiles.length > 0) {
    console.log('❌ CouchDB Windows Bundle: Missing files');
    console.log(`   Missing: ${missingFiles.join(', ')}`);
    return false;
  }
  
  // Check batch file content
  const batchFile = path.join(couchdbDir, 'bin', 'couchdb.cmd');
  const content = fs.readFileSync(batchFile, 'utf8');
  if (content.includes('binrl.exe') || content.length < 50) {
    console.log('❌ CouchDB Windows Bundle: Corrupted batch file');
    return false;
  }
  
  console.log('✅ CouchDB Windows Bundle: Valid');
  return true;
}

function checkNSSMBundle() {
  const nssmDir = path.join(RESOURCES_DIR, 'nssm');
  const nssmFiles = ['nssm-x64.exe', 'nssm-x86.exe'];
  
  if (!fs.existsSync(nssmDir)) {
    console.log('❌ NSSM Bundle: Directory not found');
    return false;
  }
  
  const missingFiles = nssmFiles.filter(file => 
    !fs.existsSync(path.join(nssmDir, file))
  );
  
  if (missingFiles.length > 0) {
    console.log('❌ NSSM Bundle: Missing files');
    console.log(`   Missing: ${missingFiles.join(', ')}`);
    return false;
  }
  
  console.log('✅ NSSM Bundle: Available');
  return true;
}

async function runSetup() {
  console.log('🔍 Checking requirements...\n');
  
  // Check basic requirements
  const nodeOk = checkNodeJS();
  const extractionOk = checkExtractionTools();
  
  if (!nodeOk) {
    console.log('\n❌ Node.js is required but not found. Please install Node.js first.');
    process.exit(1);
  }
  
  if (!extractionOk) {
    console.log('\n⚠️ Archive extraction tools missing. Some operations may fail.');
  }
  
  console.log('\n🔍 Checking Windows-specific bundles...\n');
  
  // Check bundles
  const couchdbOk = checkCouchDBBundle();
  const nssmOk = checkNSSMBundle();
  
  console.log('\n🔧 Setting up missing components...\n');
  
  // Fix CouchDB bundle if needed
  if (!couchdbOk) {
    console.log('📦 Fixing Windows CouchDB bundle...');
    try {
      execSync('node scripts/fix-windows-couchdb.js', { stdio: 'inherit' });
      console.log('✅ CouchDB bundle fixed');
    } catch (error) {
      console.error('❌ Failed to fix CouchDB bundle:', error.message);
      console.log('   You may need to manually download and extract CouchDB for Windows');
    }
  }
  
  // Download NSSM if needed
  if (!nssmOk) {
    console.log('📦 Downloading NSSM...');
    try {
      execSync('node scripts/download-nssm.js', { stdio: 'inherit' });
      console.log('✅ NSSM downloaded');
    } catch (error) {
      console.error('❌ Failed to download NSSM:', error.message);
      console.log('   You may need to manually download NSSM from https://nssm.cc/');
    }
  }
  
  console.log('\n🎯 Final validation...\n');
  
  // Re-check everything
  const finalCouchdbOk = checkCouchDBBundle();
  const finalNssmOk = checkNSSMBundle();
  
  console.log('\n📊 Setup Summary:');
  console.log(`   Node.js: ${nodeOk ? '✅' : '❌'}`);
  console.log(`   Archive Tools: ${extractionOk ? '✅' : '⚠️'}`);
  console.log(`   CouchDB Bundle: ${finalCouchdbOk ? '✅' : '❌'}`);
  console.log(`   NSSM Bundle: ${finalNssmOk ? '✅' : '❌'}`);
  
  if (finalCouchdbOk && finalNssmOk) {
    console.log('\n🎉 Windows production environment is ready!');
    console.log('\nNext steps:');
    console.log('   1. Build Windows app: npm run electron:build:win');
    console.log('   2. Test the installer on a clean Windows machine');
    console.log('   3. CouchDB will be installed as a Windows service automatically');
  } else {
    console.log('\n⚠️ Some components are still missing. Please check the errors above.');
    console.log('\nManual steps may be required:');
    if (!finalCouchdbOk) {
      console.log('   - Download CouchDB 3.3.3 for Windows from https://couchdb.apache.org/');
      console.log('   - Extract to electron/resources/couchdb-windows/');
    }
    if (!finalNssmOk) {
      console.log('   - Download NSSM from https://nssm.cc/');
      console.log('   - Extract nssm.exe files to electron/resources/nssm/');
    }
  }
}

runSetup().catch(error => {
  console.error('❌ Setup failed:', error);
  process.exit(1);
});
