#!/usr/bin/env node

const https = require('https');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Downloading NSSM (Non-Sucking Service Manager) for Windows...');

const NSSM_VERSION = '2.24';
const NSSM_URL = `https://nssm.cc/release/nssm-${NSSM_VERSION}.zip`;
const RESOURCES_DIR = path.join(__dirname, '..', 'electron', 'resources');
const NSSM_DIR = path.join(RESOURCES_DIR, 'nssm');
const TEMP_DIR = path.join(__dirname, '..', 'temp-nssm');

async function downloadFile(url, dest) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(dest);
    https.get(url, (response) => {
      if (response.statusCode === 302 || response.statusCode === 301) {
        // Handle redirect
        return downloadFile(response.headers.location, dest).then(resolve).catch(reject);
      }
      
      if (response.statusCode !== 200) {
        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
        return;
      }
      
      response.pipe(file);
      file.on('finish', () => {
        file.close();
        resolve();
      });
    }).on('error', reject);
  });
}

async function main() {
  try {
    // Create directories
    if (!fs.existsSync(RESOURCES_DIR)) {
      fs.mkdirSync(RESOURCES_DIR, { recursive: true });
    }
    
    if (!fs.existsSync(TEMP_DIR)) {
      fs.mkdirSync(TEMP_DIR, { recursive: true });
    }

    // Download NSSM
    const zipPath = path.join(TEMP_DIR, 'nssm.zip');
    console.log(`📥 Downloading from: ${NSSM_URL}`);
    await downloadFile(NSSM_URL, zipPath);
    console.log('✅ Download complete');

    // Extract ZIP
    console.log('📦 Extracting NSSM...');
    
    // Check if unzip is available
    let extractCmd;
    try {
      execSync('which unzip', { stdio: 'ignore' });
      extractCmd = `unzip -q "${zipPath}" -d "${TEMP_DIR}"`;
    } catch {
      try {
        execSync('which 7z', { stdio: 'ignore' });
        extractCmd = `7z x "${zipPath}" -o"${TEMP_DIR}" -y`;
      } catch {
        console.error('❌ Neither unzip nor 7z found. Please install one of them.');
        process.exit(1);
      }
    }
    
    execSync(extractCmd);
    console.log('✅ Extraction complete');

    // Find extracted NSSM directory
    const extractedItems = fs.readdirSync(TEMP_DIR);
    const nssmExtractedDir = extractedItems.find(item => 
      item.startsWith('nssm-') && fs.statSync(path.join(TEMP_DIR, item)).isDirectory()
    );
    
    if (!nssmExtractedDir) {
      throw new Error('Could not find extracted NSSM directory');
    }

    // Copy NSSM binaries to resources
    const nssmSourceDir = path.join(TEMP_DIR, nssmExtractedDir);
    
    // Clean existing NSSM directory
    if (fs.existsSync(NSSM_DIR)) {
      fs.rmSync(NSSM_DIR, { recursive: true, force: true });
    }
    fs.mkdirSync(NSSM_DIR, { recursive: true });

    // Copy x64 and x86 versions
    const win64Dir = path.join(nssmSourceDir, 'win64');
    const win32Dir = path.join(nssmSourceDir, 'win32');
    
    if (fs.existsSync(win64Dir)) {
      fs.copyFileSync(
        path.join(win64Dir, 'nssm.exe'),
        path.join(NSSM_DIR, 'nssm-x64.exe')
      );
      console.log('✅ Copied nssm-x64.exe');
    }
    
    if (fs.existsSync(win32Dir)) {
      fs.copyFileSync(
        path.join(win32Dir, 'nssm.exe'),
        path.join(NSSM_DIR, 'nssm-x86.exe')
      );
      console.log('✅ Copied nssm-x86.exe');
    }

    // Copy license and readme
    const licenseFile = path.join(nssmSourceDir, 'README.txt');
    if (fs.existsSync(licenseFile)) {
      fs.copyFileSync(licenseFile, path.join(NSSM_DIR, 'README.txt'));
    }

    // Cleanup temp directory
    fs.rmSync(TEMP_DIR, { recursive: true, force: true });
    
    console.log('🎉 NSSM bundling complete!');
    console.log(`📁 NSSM binaries available at: ${NSSM_DIR}`);
    console.log('📋 Files created:');
    console.log('  - nssm-x64.exe (for 64-bit Windows)');
    console.log('  - nssm-x86.exe (for 32-bit Windows)');
    console.log('  - README.txt (license and documentation)');
    
  } catch (error) {
    console.error('❌ Error downloading NSSM:', error.message);
    process.exit(1);
  }
}

main();
