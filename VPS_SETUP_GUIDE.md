# VPS Setup Guide for Restaurant Management System

## Critical Environment Variables

Your VPS must have these environment variables configured for authentication to work:

### Required Environment Variables

```bash
# MongoDB Connection (CRITICAL)
MONGODB_URI=*********************************************************
# OR for MongoDB Atlas:
MONGODB_URI=mongodb+srv://username:<EMAIL>/resto

# JWT Secret for authentication (CRITICAL)
JWT_SECRET=your-super-secure-jwt-secret-key-at-least-32-characters
# OR alternatively:
NEXTAUTH_SECRET=your-super-secure-jwt-secret-key-at-least-32-characters

# Node Environment
NODE_ENV=production

# Optional: Port (defaults to 3000)
PORT=3000

# Optional: VPS Base URL for client apps
NEXT_PUBLIC_VPS_BASE_URL=https://bistro.icu
VPS_BASE_URL=https://bistro.icu
```

## Setup Steps

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Configure Environment Variables**
   Create a `.env.local` file or set environment variables:
   ```bash
   export MONGODB_URI="your-mongodb-connection-string"
   export JWT_SECRET="your-secure-jwt-secret"
   export NODE_ENV="production"
   ```

3. **Build the Application**
   ```bash
   npm run build
   ```

4. **Start the Server**
   ```bash
   npm start
   ```

## Debugging CORS and Auth Issues

1. **Test Health Endpoint**
   ```bash
   curl -X GET https://bistro.icu/api/health \
     -H "Origin: app://-" \
     -H "Content-Type: application/json"
   ```

2. **Test Auth Endpoint**
   ```bash
   curl -X POST https://bistro.icu/api/auth/login \
     -H "Origin: app://-" \
     -H "Content-Type: application/json" \
     -d '{"identifier":"<EMAIL>","password":"your-password"}'
   ```

3. **Check Server Logs**
   Monitor server logs for MongoDB connection issues or authentication failures.

## Common Issues

1. **CORS Errors**: Fixed by using proper CORS headers that allow `app://-` origin
2. **MongoDB Connection**: Ensure MONGODB_URI is correctly set and MongoDB is accessible
3. **JWT Secret Missing**: Ensure JWT_SECRET or NEXTAUTH_SECRET is set
4. **Port Issues**: Ensure the VPS port (default 3000) is open and accessible

## Files Fixed for CORS Support

- `lib/utils/cors.ts` - Enhanced CORS utilities
- `app/api/auth/login/route.ts` - Updated to use CORS utilities
- `app/api/auth/register/route.ts` - Updated to use CORS utilities
- `app/api/health/route.ts` - Enhanced health check with MongoDB status
- `lib/validation/api-validation.ts` - Added CORS to validation errors
- `middleware.ts` - Enhanced CORS handling

## Testing the Fix

The authentication system now properly handles:
- Electron app origins (`app://-`)
- Proper CORS headers on all responses
- MongoDB connection validation
- Detailed error logging for debugging