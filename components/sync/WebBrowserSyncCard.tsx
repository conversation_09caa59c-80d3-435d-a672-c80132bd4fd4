"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Wifi, 
  WifiOff, 
  Globe, 
  Loader2, 
  AlertTriangle, 
  CheckCircle,
  Info
} from 'lucide-react';
import { webBrowserSyncService, WebBrowserSyncStatus } from '@/lib/services/web-browser-sync';
import { isWebBrowser } from '@/lib/utils/build-utils';

interface WebBrowserSyncCardProps {
  vpsBaseUrl?: string;
  authToken?: string;
  deviceId?: string;
  onStatusChange?: (status: WebBrowserSyncStatus) => void;
}

export function WebBrowserSyncCard({
  vpsBaseUrl,
  authToken,
  deviceId,
  onStatusChange
}: WebBrowserSyncCardProps) {
  const [status, setStatus] = useState<WebBrowserSyncStatus>(webBrowserSyncService.getStatus());
  const [isConfiguring, setIsConfiguring] = useState(false);

  useEffect(() => {
    const unsubscribe = webBrowserSyncService.onStatusChange((newStatus) => {
      setStatus(newStatus);
      onStatusChange?.(newStatus);
    });

    return unsubscribe;
  }, [onStatusChange]);

  // Auto-configure if props are provided
  useEffect(() => {
    if (authToken && deviceId && !status.internetSyncEnabled) {
      setIsConfiguring(true);
      webBrowserSyncService.configure({
        vpsBaseUrl: vpsBaseUrl || 'https://bistro.icu', // Default to bistro.icu
        authToken,
        deviceId
      });
      setIsConfiguring(false);
    }
  }, [vpsBaseUrl, authToken, deviceId, status.internetSyncEnabled]);

  // Don't render if not in web browser
  if (!isWebBrowser()) {
    return null;
  }

  const handleStartSync = async () => {
    try {
      await webBrowserSyncService.startInternetSync();
    } catch (error) {
      console.error('Failed to start web browser sync:', error);
    }
  };

  const handleStopSync = async () => {
    try {
      await webBrowserSyncService.stopSync();
    } catch (error) {
      console.error('Failed to stop web browser sync:', error);
    }
  };

  const getStatusIcon = () => {
    switch (status.connectionStatus) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'connecting':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <WifiOff className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusText = () => {
    switch (status.connectionStatus) {
      case 'connected':
        return 'متصل';
      case 'connecting':
        return 'جاري الاتصال...';
      case 'error':
        return 'خطأ في الاتصال';
      default:
        return 'غير متصل';
    }
  };

  const getStatusBadgeVariant = () => {
    switch (status.connectionStatus) {
      case 'connected':
        return 'default' as const;
      case 'connecting':
        return 'secondary' as const;
      case 'error':
        return 'destructive' as const;
      default:
        return 'outline' as const;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              مزامنة المتصفح
            </CardTitle>
            <CardDescription>
              المزامنة عبر الإنترنت للمتصفحات
            </CardDescription>
          </div>
          <Badge variant={getStatusBadgeVariant()} className="flex items-center gap-1">
            {getStatusIcon()}
            {getStatusText()}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Web Browser Info */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            متصفحات الويب تستخدم المزامنة عبر الإنترنت فقط لأسباب أمنية
          </AlertDescription>
        </Alert>

        {/* Configuration Status */}
        {!status.internetSyncEnabled && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              إعدادات المزامنة غير مكتملة - يرجى التحقق من إعدادات الخادم
            </AlertDescription>
          </Alert>
        )}

        {/* Error Display */}
        {status.error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {webBrowserSyncService.getUserFriendlyError() || status.error}
            </AlertDescription>
          </Alert>
        )}

        {/* Sync Instructions */}
        {status.connectionStatus === 'disconnected' && status.internetSyncEnabled && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">تعليمات المزامنة:</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              {webBrowserSyncService.getSyncInstructions().map((instruction, index) => (
                <li key={index}>{instruction}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Connection Info */}
        {status.lastSync && (
          <div className="text-sm text-muted-foreground">
            آخر مزامنة: {status.lastSync.toLocaleTimeString('ar-SA')}
          </div>
        )}

        {/* Controls */}
        <div className="flex gap-2">
          {status.connectionStatus === 'disconnected' && (
            <Button 
              onClick={handleStartSync}
              disabled={!status.internetSyncEnabled || isConfiguring}
              className="flex items-center gap-2"
            >
              <Wifi className="h-4 w-4" />
              بدء المزامنة
            </Button>
          )}

          {(status.connectionStatus === 'connected' || status.connectionStatus === 'connecting') && (
            <Button 
              onClick={handleStopSync}
              variant="outline"
              className="flex items-center gap-2"
            >
              <WifiOff className="h-4 w-4" />
              إيقاف المزامنة
            </Button>
          )}
        </div>

        {/* Capabilities Info */}
        <div className="pt-4 border-t">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">الشبكة المحلية:</span>
              <span className="ml-2 text-red-500">غير متاح</span>
            </div>
            <div>
              <span className="text-muted-foreground">الإنترنت:</span>
              <span className="ml-2 text-green-500">متاح</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}