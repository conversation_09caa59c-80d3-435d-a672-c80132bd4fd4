"use client"

import {
  BanknoteIcon,
  BarChart4Icon,
  ChefHat,
  ClipboardList,
  Home,
  Menu,
  Package,
  Package2,
  Settings,
  Truck,
  User,
  UserCog,
  Users,
  WifiOff,
  Crown,
  Server,
  Database,
  Network,
  Smartphone,
  TestTube,
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar"

import {
  Sheet,
  SheetContent,
  SheetTrigger,
  SheetClose,
} from "@/components/ui/sheet"

import { useAuth } from "@/lib/context/multi-user-auth-provider"
import { usePermissions } from "@/lib/hooks/use-permissions"
import { useSettings } from '@/lib/context/settings-context'
import { isAdmin as checkIsAdmin } from "@/lib/auth/role-utils"
import { usePathname } from "next/navigation"
import { useMobileLayout, useTouchOptimization } from '@/hooks/use-mobile-layout'
import { useOSDetection } from '@/hooks/use-os-detection'
import { NavMain } from "./nav-main"
import { NavMobile } from "./nav-mobile"
import { NavUser } from "./nav-user"
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import React from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { getCurrentRestaurantId as getCurrentRestaurantIdUtil } from '@/lib/db/v4/utils/restaurant-id';
import { NavItem } from './nav-main';
import { RefreshCw } from "lucide-react"; // Import RefreshCw for loading indicator
import { SimpleNetworkIndicator } from './simple-network-indicator';
import Link from "next/link"
import { cn } from "@/lib/utils"

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const currentPathname = usePathname() || ""
  const { user } = useAuth()
  const { hasPageAccess, isOwner, isLoading } = usePermissions()
  const { isCogsEnabled } = useSettings()
  const { isMobile: isSidebarMobile } = useSidebar()
  const { isMobile: isMobileLayout } = useMobileLayout()
  const { isMobile: isMobileOS, platform, isCapacitor } = useOSDetection()
  
  // Use hybrid approach: OS detection for true mobile platforms, but fallback to screen size for browsers
  // Electron should always be treated as desktop regardless of platform detection
  const { isElectron } = useOSDetection()
  const isMobile = !isElectron && (isCapacitor || platform === 'ios' || platform === 'android' || (platform === 'unknown' && isMobileLayout))

  // Wait for permissions to be ready
  if (isLoading) {
    return (
      <Sidebar collapsible={isMobile ? "offcanvas" : "icon"} {...props}>
        <SidebarHeader />
        <SidebarContent>
          <div className="flex items-center justify-center h-full">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
          </div>
        </SidebarContent>
        <SidebarFooter />
        <SidebarRail />
      </Sidebar>
    );
  }

  // Use the utility functions directly with the user object
  const isAdmin = checkIsAdmin(user)

  // 👤 User data for NavUser component
  const userData = {
    name: user?.name || 'Unknown User',
    email: user?.email || '',
    avatar: '' // Required by NavUser component
  };

  // Create mobile navigation items (only 3 items)
  const createMobileNavItems = () => {
    const items = []

    // Waiter - Primary mobile action
    if (hasPageAccess('orders')) {
      items.push({
        title: "Waiter",
        url: "/waiter",
        icon: User,
        isActive: currentPathname === "/waiter",
      })
    }

    // Finance
    if (hasPageAccess('finance')) {
      items.push({
        title: "Finance",
        url: "/finance",
        icon: BanknoteIcon,
        isActive: currentPathname === "/finance" || currentPathname.startsWith("/finance/"),
      })
    }

    // Analytics
    if (hasPageAccess('analytics')) {
      items.push({
        title: "Analytics",
        url: "/analytics",
        icon: BarChart4Icon,
        isActive: currentPathname === "/analytics" || currentPathname.startsWith("/analytics/"),
      })
    }

    return items
  }

  // Create full navigation items for desktop
  const createNavItems = () => {
    const items = []

    // Mobile-first: Only show core operational items
    if (isMobile) {
      // Waiter - Core mobile functionality
      if (hasPageAccess('orders')) {
        items.push({
          title: "Waiter",
          url: "/waiter",
          icon: User,
          isActive: currentPathname === "/waiter",
        })
      }

      // Finance - Essential for operations
      if (hasPageAccess('finance')) {
        items.push({
          title: "Finance",
          url: "/finance",
          icon: BanknoteIcon,
          isActive: currentPathname === "/finance" || currentPathname.startsWith("/finance/"),
        })
      }

      // Analytics - Quick insights
      if (hasPageAccess('analytics')) {
        items.push({
          title: "Analytics",
          url: "/analytics",
          icon: BarChart4Icon,
          isActive: currentPathname === "/analytics" || currentPathname.startsWith("/analytics/"),
        })
      }

      return items
    }

    // Desktop: Full navigation
    // Menu - Production
    if (hasPageAccess('menu')) {
      items.push({
        title: "Menu",
        url: "/menu",
        icon: Package,
        isActive: currentPathname === "/menu",
      })
    }

    // Orders - Production
    if (hasPageAccess('orders')) {
      items.push({
        title: "Orders",
        url: "/ordering",
        icon: ClipboardList,
        isActive: currentPathname === "/ordering",
      });
    }

    // Inventory - Production
    if (hasPageAccess('inventory')) {
      items.push({
        title: "Inventory",
        url: "/inventory",
        icon: Package2,
        isActive: currentPathname === "/inventory",
      })
    }

    // Suppliers - Production
    if (hasPageAccess('suppliers')) {
      items.push({
        title: "Suppliers",
        url: "/suppliers",
        icon: Truck,
        isActive: currentPathname === "/suppliers",
      })
    }

    // Staff - Production
    if (hasPageAccess('staff')) {
      items.push({
        title: "Staff",
        url: "/staff",
        icon: UserCog,
        isActive: currentPathname === "/staff",
      })
    }

    // Finance - Production
    if (hasPageAccess('finance')) {
      items.push({
        title: "Finance",
        url: "/finance",
        icon: BanknoteIcon,
        isActive: currentPathname === "/finance" || currentPathname.startsWith("/finance/"),
      })
    }

    // Analytics - Production
    if (hasPageAccess('analytics')) {
      items.push({
        title: "Analytics",
        url: "/analytics",
        icon: BarChart4Icon,
        isActive: currentPathname === "/analytics" || currentPathname.startsWith("/analytics/"),
      })
    }

    // Waiter - Production
    if (hasPageAccess('orders')) {
      items.push({
        title: "Waiter",
        url: "/waiter",
        icon: User,
        isActive: currentPathname === "/waiter",
      })
    }

    // Settings - Production
    if (hasPageAccess('settings')) {
      items.push({
        title: "Settings",
        url: "/settings",
        icon: Settings,
        isActive: currentPathname === "/settings",
      })
    }

    // Admin routes - Production
    if (isAdmin) {
      items.push({
        title: "Users",
        url: "/admin/users",
        icon: Users,
        isActive: currentPathname === "/admin/users",
      })
    }

    // Development routes - only in development mode
    if (process.env.NODE_ENV === 'development') {
      items.push({
        title: "Mobile Ordering",
        url: "/mobile-ordering",
        icon: Smartphone,
        isActive: currentPathname === "/mobile-ordering",
      })

      if (hasPageAccess('settings')) {
        items.push({
          title: "Offline Test",
          url: "/offline-test",
          icon: WifiOff,
          isActive: currentPathname === "/offline-test",
        })
      }

      items.push({
        title: "HTTP Sync Monitor",
        url: "/p2p-sync",
        icon: Network,
        isActive: currentPathname === "/p2p-sync",
      })

      items.push({
        title: "CouchDB Debug",
        url: "/debug/couchdb", 
        icon: Database,
        isActive: currentPathname === "/debug/couchdb",
      })

      items.push({
        title: "CouchDB Service",
        url: "/debug/couchdb-service",
        icon: Server,
        isActive: currentPathname === "/debug/couchdb-service",
      })
    }

    // CouchDB Service - always available for production troubleshooting
    if (isAdmin) {
      items.push({
        title: "CouchDB Service",
        url: "/debug/couchdb-service",
        icon: Server,
        isActive: currentPathname === "/debug/couchdb-service",
      })
    }

    // Debug routes - always available for troubleshooting
    items.push({
      title: "System Debug",
      url: "/debug/couchdb",
      icon: TestTube,
      isActive: currentPathname === "/debug/couchdb",
    })

    return items
  }

  const navItems = createNavItems();

  // Unified sidebar for both desktop and mobile
  return (
    <Sidebar collapsible={isMobile ? "offcanvas" : "icon"} {...props}>
      <SidebarHeader className={cn(isMobile ? "p-3" : "")}>
        <div className="flex items-center justify-between">
          <div className={cn(
            "flex items-center gap-2 group-data-[collapsible=icon]:hidden",
            isMobile ? "px-1 py-1" : "px-4 py-2"
          )}>
            <div className={cn(
              "flex aspect-square items-center justify-center rounded-lg bg-primary text-primary-foreground",
              isMobile ? "size-6" : "size-8"
            )}>
              <ChefHat className={cn(isMobile ? "size-3" : "size-4")} />
            </div>
            <div className="grid flex-1 text-left leading-tight">
              <span className={cn(
                "truncate font-semibold",
                isMobile ? "text-xs" : "text-sm"
              )}>Bistro</span>
              <span className={cn(
                "truncate text-muted-foreground",
                isMobile ? "text-[10px]" : "text-xs"
              )}>Restaurant Manager</span>
            </div>
          </div>
          <div className={cn(
            "flex items-center justify-center group-data-[collapsible=icon]:w-full group-data-[collapsible=icon]:px-0",
            isMobile ? "px-1" : "px-4"
          )}>
            <SidebarTrigger className={cn(
              "group-data-[collapsible=icon]:rounded-md group-data-[collapsible=icon]:hover:bg-sidebar-accent",
              isMobile ? "size-6" : "group-data-[collapsible=icon]:size-8"
            )} />
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent className={cn(isMobile ? "px-2" : "")}>
        <NavMain items={navItems} isMobile={isMobile} />
      </SidebarContent>
      <SidebarFooter className={cn(isMobile ? "p-2 space-y-1" : "")}>
        {!isMobile && (
          <div className="p-1">
            <SimpleNetworkIndicator />
          </div>
        )}
        <NavUser user={userData} isMobile={isMobile} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

export function MobileSidebar() {
  const currentPathname = usePathname() || ""
  const { user } = useAuth()
  const { hasPageAccess, isLoading } = usePermissions()
  
  if (isLoading) {
    return (
      <button className="h-8 w-8 rounded-md bg-muted/50 animate-pulse" disabled />
    )
  }

  const userData = {
    name: user?.name || 'Unknown User',
    email: user?.email || '',
    avatar: ''
  };

  const mobileNavItems = []

  // Waiter - Primary mobile action
  if (hasPageAccess('orders')) {
    mobileNavItems.push({
      title: "Waiter",
      url: "/waiter",
      icon: User,
      isActive: currentPathname === "/waiter",
    })
  }

  // Finance
  if (hasPageAccess('finance')) {
    mobileNavItems.push({
      title: "Finance",
      url: "/finance",
      icon: BanknoteIcon,
      isActive: currentPathname === "/finance" || currentPathname.startsWith("/finance/"),
    })
  }

  // Analytics
  if (hasPageAccess('analytics')) {
    mobileNavItems.push({
      title: "Analytics",
      url: "/analytics",
      icon: BarChart4Icon,
      isActive: currentPathname === "/analytics" || currentPathname.startsWith("/analytics/"),
    })
  }

  return (
    <Sheet>
      <SheetTrigger asChild>
        <button className="h-8 w-8 rounded-md bg-primary/10 hover:bg-primary/20 text-primary border-0 flex items-center justify-center transition-colors shrink-0">
          <Menu className="h-4 w-4" />
          <span className="sr-only">Toggle Menu</span>
        </button>
      </SheetTrigger>
      <SheetContent side="left" className="w-60 p-0 border-r bg-background">
        <div className="flex flex-col h-full">
          {/* Ultra-compact Header */}
          <div className="flex items-center gap-2 p-3 border-b bg-muted/50">
            <div className="flex aspect-square size-6 items-center justify-center rounded-md bg-primary text-primary-foreground">
              <ChefHat className="size-3" />
            </div>
            <div className="grid flex-1 text-left leading-tight">
              <span className="truncate font-semibold text-xs">Bistro</span>
              <span className="truncate text-[10px] text-muted-foreground">Restaurant Manager</span>
            </div>
          </div>
          
          {/* Ultra-compact Navigation */}
          <div className="flex-1 py-2 px-3">
            <div className="space-y-1">
              {mobileNavItems.map((item) => (
                <Link
                  key={item.title}
                  href={item.url}
                  className={cn(
                    "flex items-center gap-2 h-9 px-3 rounded-md text-sm font-medium transition-colors",
                    item.isActive 
                      ? "bg-primary text-primary-foreground" 
                      : "hover:bg-muted text-muted-foreground hover:text-foreground"
                  )}
                >
                  <item.icon className="size-4 shrink-0" />
                  <span>{item.title}</span>
                </Link>
              ))}
            </div>
          </div>
          
          {/* Ultra-compact Footer */}
          <div className="border-t p-3 bg-muted/30">
            <div className="flex items-center gap-2 text-xs">
              <div className="flex aspect-square size-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-[10px] font-medium">
                {userData.name.charAt(0).toUpperCase()}
              </div>
              <div className="flex-1 min-w-0">
                <p className="truncate font-medium text-[11px]">{userData.name}</p>
                <p className="truncate text-[10px] text-muted-foreground">{userData.email}</p>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}

