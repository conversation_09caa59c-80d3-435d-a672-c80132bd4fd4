"use client"

import React, { use<PERSON>emo, useState, useEffect } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { format, parseISO, getDay, getHours, addDays, startOfWeek, endOfWeek, addWeeks, subWeeks, isWithinInterval, startOfMonth, endOfMonth, eachWeekOfInterval, getMonth, getYear, isEqual, subMonths, isSameMonth } from 'date-fns';
import { fr } from 'date-fns/locale';
import { ClockIcon, TrendingUpIcon, ArrowUpIcon, Users2Icon, InfoIcon, ChevronLeftIcon, ChevronRightIcon, CheckIcon, LayersIcon, PlusCircleIcon, MinusCircleIcon, XCircleIcon, CalendarDaysIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatCurrency } from '@/lib/utils/currency';
import { 
  Popover,
  <PERSON>over<PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";

interface PeakHoursHeatmapProps {
  orders: any[]; // Replace with proper type
  className?: string;
}

export default function PeakHoursHeatmap({
  orders,
  className
}: PeakHoursHeatmapProps) {
  const [hoveredCell, setHoveredCell] = useState<{ day: number; hour: number } | null>(null);
  const [currentWeekOffset, setCurrentWeekOffset] = useState(0);
  
  const dayNamesInFrench = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
  
  const hourlyData = useMemo(() => {
    const days = dayNamesInFrench;
    const hours = Array.from({ length: 24 }, (_, i) => i);
    const processedData: { count: number, total: number }[][] = Array(7).fill(0).map(() => 
      Array(24).fill(0).map(() => ({ count: 0, total: 0 }))
    );

    const weekStart = startOfWeek(addWeeks(new Date(), currentWeekOffset), { weekStartsOn: 0 });
    const weekEnd = endOfWeek(addWeeks(new Date(), currentWeekOffset), { weekStartsOn: 0 });

    const filteredOrders = orders.filter(order => {
      if (!order.createdAt || order.status !== 'completed') return false;
      try {
        const orderDate = parseISO(order.createdAt);
        return isWithinInterval(orderDate, { start: weekStart, end: weekEnd });
      } catch (error) {
        console.error('Error parsing order date for filtering:', error, order.createdAt);
        return false;
      }
    });
    
    filteredOrders.forEach(order => {
      try {
        const orderDate = parseISO(order.createdAt);
        const dayIndex = getDay(orderDate); 
        const hourIndex = getHours(orderDate);
        
        processedData[dayIndex][hourIndex].count += 1;
        processedData[dayIndex][hourIndex].total += order.total || 0;
      } catch (error) {
        console.error('Error parsing date within hourlyData calculation:', error);
      }
    });
    
    return { days, hours, data: processedData };
  }, [orders, currentWeekOffset]);

  const { maxCount, maxTotal, totalOrders, totalSales, nonZeroValues } = useMemo(() => {
    let maxCount = 0;
    let maxTotal = 0;
    let totalOrders = 0;
    let totalSales = 0;
    let nonZeroValues = 0;
    
    for (let i = 0; i < 7; i++) {
      for (let j = 0; j < 24; j++) {
        maxCount = Math.max(maxCount, hourlyData.data[i][j].count);
        maxTotal = Math.max(maxTotal, hourlyData.data[i][j].total);
        totalOrders += hourlyData.data[i][j].count;
        totalSales += hourlyData.data[i][j].total;
        if (hourlyData.data[i][j].count > 0) {
          nonZeroValues++;
        }
      }
    }
    return { maxCount, maxTotal, totalOrders, totalSales, nonZeroValues };
  }, [hourlyData]);

  const dynamicColors = useMemo(() => {
    const colorMap = new Map<number, string>();
    colorMap.set(0, 'bg-muted/10 dark:bg-muted/20');
    
    if (maxCount <= 1) {
      colorMap.set(1, 'bg-blue-400 dark:bg-blue-600/70');
      return colorMap;
    }
    
    const getColor = (value: number) => {
      const intensity = maxCount > 0 ? Math.floor((value / maxCount) * 100) : 0;

      if (intensity === 0) return 'bg-muted/10 dark:bg-muted/20'; 
      if (intensity < 20) return 'bg-blue-100 dark:bg-blue-900/30';
      if (intensity < 40) return 'bg-blue-300 dark:bg-blue-800/40';
      if (intensity < 60) return 'bg-blue-500 dark:bg-blue-700/60';
      if (intensity < 80) return 'bg-blue-600 dark:bg-blue-500/80';
      return 'bg-blue-700 dark:bg-blue-500'; 
    };
    
    for (let count = 1; count <= maxCount; count++) {
      colorMap.set(count, getColor(count));
    }
    
    return colorMap;
  }, [maxCount]);

  const getColorForCount = (count: number) => {
    return dynamicColors.get(count) || 'bg-muted/10';
  };

  const formatHour = (hour: number) => {
    return `${hour}:00`;
  };

  const businessHours = hourlyData.hours.filter(hour => hour >= 8 && hour <= 23);

  const dayTotals = useMemo(() => {
    return hourlyData.data.map(day => 
      day.reduce((acc, hour) => ({ 
        count: acc.count + hour.count, 
        total: acc.total + hour.total 
      }), { count: 0, total: 0 })
    );
  }, [hourlyData.data]);

  const hourTotals = useMemo(() => {
    const totals = Array(24).fill(0).map(() => ({ count: 0, total: 0 }));
    
    for (let dayIndex = 0; dayIndex < 7; dayIndex++) {
      for (let hourIndex = 0; hourIndex < 24; hourIndex++) {
        totals[hourIndex].count += hourlyData.data[dayIndex][hourIndex].count;
        totals[hourIndex].total += hourlyData.data[dayIndex][hourIndex].total;
      }
    }
    
    return totals;
  }, [hourlyData.data]);

  const busiestDayIndex = useMemo(() => {
    return dayTotals.reduce((maxIndex, current, index, array) => 
      current.count > array[maxIndex].count ? index : maxIndex, 0);
  }, [dayTotals]);

  const busiestHour = useMemo(() => {
    let maxCount = 0;
    let busiestHour = 0;
    let busiestDay = 0;

    for (let dayIndex = 0; dayIndex < hourlyData.data.length; dayIndex++) {
      for (let hourIndex = 0; hourIndex < 24; hourIndex++) {
        const count = hourlyData.data[dayIndex][hourIndex].count;
        if (count > maxCount) {
          maxCount = count;
          busiestHour = hourIndex;
          busiestDay = dayIndex;
        }
      }
    }

    return {
      dayIndex: busiestDay,
      hourIndex: busiestHour,
      count: maxCount,
      total: hourlyData.data[busiestDay][busiestHour].total
    };
  }, [hourlyData.data]);
  
  const busiestHourOfDay = useMemo(() => {
    const maxHour = hourTotals.reduce((maxIdx, curr, idx, arr) => 
      curr.count > arr[maxIdx].count ? idx : maxIdx, 0);
    
    return {
      hour: maxHour,
      count: hourTotals[maxHour].count,
      total: hourTotals[maxHour].total
    };
  }, [hourTotals]);

  const handleCellHover = (dayIndex: number, hourIndex: number) => {
    setHoveredCell({ day: dayIndex, hour: hourIndex });
  };

  const handleCellLeave = () => {
    setHoveredCell(null);
  };

  const getPercentage = (count: number) => {
    if (totalOrders === 0) return '0%';
    return `${((count / totalOrders) * 100).toFixed(1)}%`;
  };

  const formatDateForDayLabel = (dayIndex: number) => {
    const targetWeekStart = startOfWeek(addWeeks(new Date(), currentWeekOffset), { weekStartsOn: 0 });
    const date = addDays(targetWeekStart, dayIndex);
    return format(date, 'd MMM', { locale: fr });
  };

  const getCurrentWeekDisplayString = (offset?: number) => {
    const targetOffset = offset === undefined ? currentWeekOffset : offset;

    if (targetOffset === 0 && offset === undefined) return "Cette semaine";
    if (targetOffset === -1 && offset === undefined) return "Semaine dernière";

    const weekStart = startOfWeek(addWeeks(new Date(), targetOffset), { weekStartsOn: 0 });
    const weekEnd = endOfWeek(addWeeks(new Date(), targetOffset), { weekStartsOn: 0 });
    
    if (weekStart.getMonth() !== weekEnd.getMonth()) {
      return `${format(weekStart, 'd MMM', { locale: fr })} - ${format(weekEnd, 'd MMM', { locale: fr })}`;
    }
    return `${format(weekStart, 'd', { locale: fr })} - ${format(weekEnd, 'd MMM', { locale: fr })}`;
  };
  
  const legendColors = useMemo(() => {
    const samples = [
      { count: 0, color: getColorForCount(0) },
      { count: Math.floor(maxCount * 0.2), color: getColorForCount(Math.floor(maxCount * 0.2)) },
      { count: Math.floor(maxCount * 0.5), color: getColorForCount(Math.floor(maxCount * 0.5)) },
      { count: Math.floor(maxCount * 0.8), color: getColorForCount(Math.floor(maxCount * 0.8)) },
      { count: maxCount, color: getColorForCount(maxCount) },
    ];
    const uniqueSamples = samples.reduce((acc, current) => {
      if (!acc.find(item => item.count === current.count)) {
        acc.push(current);
      }
      return acc;
    }, [] as {count: number, color: string}[]);
    if (maxCount === 0) return [{ count: 0, color: getColorForCount(0) }];
    return uniqueSamples.sort((a,b) => a.count - b.count);
  }, [maxCount, getColorForCount]);

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="pt-3 pb-2 px-2 sm:pt-4 sm:pb-3 sm:px-4">
        <div className="flex flex-col gap-2 sm:flex-row sm:items-start sm:justify-between sm:gap-0">
          <div>
            <CardTitle className="text-sm font-medium flex items-center gap-1.5 sm:text-base sm:gap-2">
              <ClockIcon className="h-3.5 w-3.5 text-primary sm:h-4 sm:w-4" />
              Heures de Pointe
            </CardTitle>
            <CardDescription className="text-xs sm:text-sm">
              Distribution des commandes par jour et heure
            </CardDescription>
          </div>

          <div className="flex flex-row gap-3 sm:flex-col sm:gap-1 sm:items-end">
            <div>
              <p className="text-[10px] text-muted-foreground sm:text-xs sm:text-right">Total Commandes</p>
              <p className="text-sm font-semibold sm:text-base sm:text-right">{totalOrders}</p>
            </div>
            <div>
              <p className="text-[10px] text-muted-foreground sm:text-xs sm:text-right">Ventes Totales</p>
              <p className="text-sm font-semibold sm:text-base sm:text-right">{formatCurrency(totalSales)}</p>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-1.5 sm:pt-3 px-1.5 sm:px-4 overflow-x-auto -mx-1.5 px-2 sm:-mx-2 sm:px-3">
        <div className="mb-2 sm:mb-3 flex justify-center">
          <div className="inline-flex items-center bg-muted p-0.5 rounded-md shadow-sm border sm:p-1 sm:rounded-lg">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      setCurrentWeekOffset(prev => prev - 1)
                    }}
                    className="h-5 w-5 sm:h-6 sm:w-6"
                  >
                    <ChevronLeftIcon className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                    <span className="sr-only">Précédente</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="p-1.5 sm:p-2"><p className="text-xs sm:text-sm">Semaine précédente</p></TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="ghost" 
                    className={cn(
                      "px-1.5 py-0.5 sm:px-2 sm:py-1 h-5 sm:h-6 text-[10px] font-medium tabular-nums min-w-[90px] sm:min-w-[120px] sm:text-xs",
                    )}
                  >
                     {getCurrentWeekDisplayString()}
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="p-1.5 sm:p-2">
                  <p className="text-xs sm:text-sm">{getCurrentWeekDisplayString()}</p> 
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      if (currentWeekOffset < 0) setCurrentWeekOffset(prev => prev + 1)
                    }}
                    disabled={currentWeekOffset === 0}
                    className="h-5 w-5 sm:h-6 sm:w-6"
                  >
                    <ChevronRightIcon className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                    <span className="sr-only">Suivante</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="p-1.5 sm:p-2"><p className="text-xs sm:text-sm">Semaine suivante</p></TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        <div className="relative min-w-[500px] sm:min-w-[650px]">
          <div className="flex border-b pb-1 mb-1 sm:mb-1.5">
            <div className="w-16 sm:w-24 flex-shrink-0"></div>
            <div className="flex-1 flex">
              {businessHours.map(hour => {
                const isMaxHour = hour === busiestHourOfDay.hour;
                return (
                  <div key={hour} className="flex-1 flex flex-col items-center">
                    <div className={cn(
                      "text-[9px] font-medium mb-0.5 sm:text-xs sm:mb-1",
                      isMaxHour ? "text-primary" : "text-muted-foreground"
                    )}>
                      {formatHour(hour)}
                      {isMaxHour && <ArrowUpIcon className="inline h-1.5 w-1.5 sm:h-2 sm:w-2 ml-0.5" />}
                    </div>
                    <div className={cn(
                      "text-[8px] px-0.5 rounded sm:text-[10px] sm:px-1",
                      isMaxHour ? "bg-primary/10 text-primary font-medium" : ""
                    )}>
                      {hourTotals[hour].count}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
          
          {hourlyData.days.map((day, dayIndex) => (
            <div 
              key={day} 
              className={cn(
                "flex mb-1 group sm:mb-1.5",
                dayIndex === busiestDayIndex && "bg-muted/5 rounded"
              )}
            >
              <div className={cn(
                "w-16 flex-shrink-0 py-1 px-1 text-[10px] flex flex-col justify-center sm:w-24 sm:text-xs sm:px-2",
                dayIndex === busiestDayIndex && "text-primary font-medium"
              )}>
                <div className="flex items-center gap-1">
                  {dayIndex === busiestDayIndex && <div className="h-1 w-1 rounded-full bg-primary sm:h-1.5 sm:w-1.5" />}
                  <span className="truncate">{day.slice(0, 3)}</span>
                </div>
                <div className="text-[9px] text-muted-foreground mt-0.5 sm:text-[10px]">
                  {formatDateForDayLabel(dayIndex)}
                  <span className="ml-1">({dayTotals[dayIndex].count})</span>
                </div>
              </div>
              
              <div className="flex-1 flex">
                {businessHours.map(hour => {
                  const value = hourlyData.data[dayIndex][hour];
                  const isBusiestCell = dayIndex === busiestHour.dayIndex && hour === busiestHour.hourIndex && value.count > 0;
                  const isHovered = hoveredCell?.day === dayIndex && hoveredCell?.hour === hour;
                  
                  return (
                    <Popover key={`${day}-${hour}`} open={isHovered}>
                      <PopoverTrigger asChild>
                        <motion.div 
                          className={cn(
                            "flex-1 h-6 m-px rounded-sm flex items-center justify-center transition-all sm:h-10 sm:rounded-md",
                            getColorForCount(value.count),
                            isBusiestCell && "ring-1 ring-primary/70 shadow-sm sm:ring-2",
                            value.count > 0 && "cursor-pointer hover:z-10 hover:shadow-sm sm:hover:shadow-md"
                          )}
                          onHoverStart={() => handleCellHover(dayIndex, hour)}
                          onHoverEnd={handleCellLeave}
                          initial={{ scale: 1 }}
                          whileHover={{ scale: 1.1 }}
                          transition={{ duration: 0.1 }}
                        >
                          {value.count > 0 && (
                            <span className={cn(
                              "text-[8px] font-medium sm:text-xs",
                              value.count >= maxCount * 0.7 ? "text-white drop-shadow-sm" : ""
                            )}>
                              {value.count}
                            </span>
                          )}
                        </motion.div>
                      </PopoverTrigger>
                      
                      <PopoverContent 
                        side="top" 
                        className="w-48 p-0 border-primary/20 shadow-md sm:w-56" 
                        sideOffset={5}
                      >
                        <div className="bg-primary/10 p-1.5 rounded-t-md border-b border-primary/20 sm:p-2">
                          <div className="font-medium text-sm flex justify-between items-center">
                            <span>{day}</span>
                            <span>{formatHour(hour)}</span>
                          </div>
                          <div className="text-xs text-muted-foreground mt-0.5">
                            {formatDateForDayLabel(dayIndex)}
                          </div>
                        </div>
                        
                        <div className="p-2 sm:p-3">
                          <div className="grid grid-cols-2 gap-y-1.5 gap-x-2 text-xs sm:text-sm sm:gap-y-2 sm:gap-x-3">
                            <div>
                              <p className="text-[10px] text-muted-foreground sm:text-xs">Commandes</p>
                              <p className="font-medium">{value.count}</p>
                            </div>
                            <div>
                              <p className="text-[10px] text-muted-foreground sm:text-xs">% du Total</p>
                              <p className="font-medium">{getPercentage(value.count)}</p>
                            </div>
                            <div>
                              <p className="text-[10px] text-muted-foreground sm:text-xs">Ventes</p>
                              <p className="font-medium">{formatCurrency(value.total)}</p>
                            </div>
                            <div>
                              <p className="text-[10px] text-muted-foreground sm:text-xs">Ticket Moyen</p>
                              <p className="font-medium">
                                {value.count > 0 ? formatCurrency(value.total / value.count) : "-"}
                              </p>
                            </div>
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
        
        <div className="flex flex-col items-start gap-2 mt-3 border-t pt-2 sm:flex-row sm:items-center sm:justify-between sm:gap-0 sm:mt-4 sm:pt-3">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-1 text-[10px] text-muted-foreground cursor-help sm:text-xs">
                  <InfoIcon className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                  <span>Échelle adaptative</span>
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="p-1.5 w-48 sm:p-2 sm:w-60">
                <p className="text-[10px] sm:text-xs">
                  L'intensité des couleurs s'adapte au volume maximum de commandes ({maxCount} commandes max / créneau).
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <div className="flex items-center gap-0.5">
            <div className="text-[9px] text-muted-foreground mr-1 sm:text-[10px]">0</div>
            {legendColors.filter(c => c.count > 0 || maxCount === 0).map((colorItem, i) => (
              <TooltipProvider key={`legend-${i}`}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className={cn(
                      "h-2 w-2 rounded-sm sm:h-3 sm:w-3", 
                      colorItem.color, 
                      maxCount === 0 && "w-4 sm:w-8"
                    )}></div>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="p-1 sm:p-1.5">
                    <p className="text-[10px] sm:text-xs">{colorItem.count} cmd.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ))}
            {maxCount > 0 && <div className="text-[9px] text-muted-foreground ml-1 sm:text-[10px]">{maxCount}</div>}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
