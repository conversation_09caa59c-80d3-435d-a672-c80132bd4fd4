"use client";

import { useState, useEffect } from 'react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { StockItem } from '@/types/stock';
import { Supplier } from '@/types/suppliers';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { AlertCircle, InfoIcon, DollarSign, TrendingUp, ShoppingCart, Truck, Package, CircleDashed, PlusCircle, Calculator } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { useSuppliersV4 as useSuppliersDB } from '@/lib/hooks/useSuppliersV4';
import { useToast } from "@/components/ui/use-toast";
import { SimpleSupplierForm } from '@/components/suppliers/SimpleSupplierForm';

// Schema for purchase validation
const purchaseSchema = z.object({
  supplierId: z.string().min(1, { message: "Please select a supplier." })
    .transform(val => val === "none" ? "" : val),
  quantity: z.coerce.number().min(0.01, { message: "Quantity must be greater than zero." }),
  purchaseUnitId: z.string().optional(),
  costPerUnit: z.coerce.number().min(0.01, { message: "Cost per unit must be greater than zero." }),
  amountPaid: z.coerce.number().min(0, { message: "Amount paid must be a positive number." })
    .optional()
    .transform(val => val || 0),
  notes: z.string().optional(),
});

interface PurchaseFormProps {
  onSubmit: (data: z.infer<typeof purchaseSchema>) => Promise<void>;
  stockItem: StockItem;
  suppliers: Supplier[];
  suggestedSupplierId?: string;
  compact?: boolean;
}

export function PurchaseForm({ onSubmit, stockItem, suppliers, suggestedSupplierId, compact = false }: PurchaseFormProps) {
  const [totalCost, setTotalCost] = useState<number>(0);
  const [remainingBalance, setRemainingBalance] = useState<number>(0);
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [quickQuantity, setQuickQuantity] = useState<number | null>(null);
  const [isAddSupplierDialogOpen, setIsAddSupplierDialogOpen] = useState(false);
  const [selectedPurchaseUnit, setSelectedPurchaseUnit] = useState<any>(null);
  const [baseQuantity, setBaseQuantity] = useState<number>(0);
  const [costPerBaseUnit, setCostPerBaseUnit] = useState<number>(0);
  const { createSupplier, refreshSuppliers } = useSuppliersDB();
  const { toast } = useToast();
  
  // Get available purchase units (including base unit)
  const availablePurchaseUnits = [
    // Base unit (always available)
    {
      id: 'base',
      name: stockItem.unit,
      conversionToBase: 1,
      isDefault: !stockItem.purchaseUnits?.some(u => u.isDefault)
    },
    // Additional purchase units
    ...(stockItem.purchaseUnits || [])
  ];

  // Get default purchase unit
  const defaultPurchaseUnit = availablePurchaseUnits.find(u => u.isDefault) || availablePurchaseUnits[0];
  
  // Initialize form with default values
  const form = useForm<z.infer<typeof purchaseSchema>>({
    resolver: zodResolver(purchaseSchema),
    defaultValues: {
      supplierId: suggestedSupplierId || 'none',
      quantity: 0,
      purchaseUnitId: defaultPurchaseUnit?.id || 'base',
      costPerUnit: typeof stockItem.costPerUnit === 'number' ? stockItem.costPerUnit : 0,
      amountPaid: 0,
      notes: ''
    },
  });

  // Watch for changes to calculate totals
  const quantity = form.watch('quantity');
  const costPerUnit = form.watch('costPerUnit');
  const amountPaid = form.watch('amountPaid') || 0;
  const supplierId = form.watch('supplierId');
  const purchaseUnitId = form.watch('purchaseUnitId');
  
  // Update selected supplier when supplierId changes
  useEffect(() => {
    const supplier = suppliers.find(s => s.id === supplierId);
    setSelectedSupplier(supplier || null);
  }, [supplierId, suppliers]);
  
  // Update selected purchase unit and calculate conversions
  useEffect(() => {
    const unit = availablePurchaseUnits.find(u => u.id === purchaseUnitId);
    setSelectedPurchaseUnit(unit);
    
    if (unit && quantity > 0) {
      // Calculate base quantity
      const calculatedBaseQuantity = quantity * unit.conversionToBase;
      setBaseQuantity(calculatedBaseQuantity);
      
      // Calculate cost per base unit
      const calculatedCostPerBaseUnit = costPerUnit / unit.conversionToBase;
      setCostPerBaseUnit(calculatedCostPerBaseUnit);
    } else {
      setBaseQuantity(0);
      setCostPerBaseUnit(0);
    }
  }, [purchaseUnitId, quantity, costPerUnit, availablePurchaseUnits]);
  
  // Calculate totals when values change
  useEffect(() => {
    const calculatedTotal = quantity * costPerUnit;
    setTotalCost(calculatedTotal);
    
    // Calculate remaining balance
    const newDebt = calculatedTotal - amountPaid;
    const currentBalance = selectedSupplier?.balance || 0;
    const newBalance = currentBalance + newDebt;
    setRemainingBalance(newBalance);
  }, [quantity, costPerUnit, amountPaid, selectedSupplier]);

  // Handle quick quantity selection
  const handleQuickQuantity = (value: number) => {
    setQuickQuantity(value);
    form.setValue('quantity', value);
  };

  // Set quick amount paid
  const handleFullPayment = () => {
    form.setValue('amountPaid', totalCost);
  };
  
  // REPLACE: Handle quick supplier creation with new implementation
  const handleAddNewSupplier = async (data: any) => {
    try {
      const newSupplier = await createSupplier(data);
      if (newSupplier && newSupplier.id) {
        // Attempt to refresh the supplier list in the parent context if function exists
        if (typeof refreshSuppliers === 'function') {
           await refreshSuppliers();
        }
        form.setValue('supplierId', newSupplier.id); // Set the new supplier in the main form
        setIsAddSupplierDialogOpen(false); // Close the dialog
        toast({
          title: "Supplier Added",
          description: `${data.name} has been added successfully.`,
        });
        return true;
      } else {
        throw new Error("Failed to get new supplier details.");
      }
    } catch (error) {
      console.error("Error adding new supplier:", error);
      toast({
        title: "Error",
        description: "Could not add the supplier. Please try again.",
        variant: "destructive",
      });
      return false;
    }
  };

  const handleFormSubmit = async (data: z.infer<typeof purchaseSchema>) => {
    try {
      // Add calculated fields
      const enrichedData = {
        ...data,
        totalAmount: totalCost,
        transactionType: 'purchase' as const,
        items: [{
          stockItemId: stockItem.id,
          quantity: data.quantity,
          purchaseUnitId: data.purchaseUnitId === 'base' ? undefined : data.purchaseUnitId,
          costPerUnit: data.costPerUnit,
          totalCost: data.quantity * data.costPerUnit
        }]
      };
      await onSubmit(enrichedData);
      form.reset();
    } catch (error) {
      console.error('Error submitting purchase:', error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-4">
        {/* Select Supplier */}
        <FormField
          control={form.control}
          name="supplierId"
          render={({ field }) => (
            <FormItem>
              <div className="flex items-center justify-between gap-2 mb-1">
                <div className="flex items-center gap-2">
                  <Truck className="h-4 w-4 text-muted-foreground" />
                  <FormLabel className="mb-0">Supplier</FormLabel>
                </div>
                {/* UPDATED: Add New Supplier Button */}
                <Dialog open={isAddSupplierDialogOpen} onOpenChange={setIsAddSupplierDialogOpen}>
                  <DialogTrigger asChild>
                    <Button type="button" variant="ghost" size="sm" className="text-xs h-7 px-2">
                      <PlusCircle className="h-3 w-3 mr-1" />
                      Add New
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[450px]">
                    <DialogHeader>
                      <DialogTitle>Add New Supplier</DialogTitle>
                      <DialogDescription>
                        Quickly add a new supplier here. More details can be added later on the Suppliers page.
                      </DialogDescription>
                    </DialogHeader>
                    <SimpleSupplierForm
                      onSubmit={handleAddNewSupplier}
                      onCancel={() => setIsAddSupplierDialogOpen(false)}
                      compact={true}
                    />
                  </DialogContent>
                </Dialog>
              </div>
              <Select onValueChange={(value) => {
                  field.onChange(value);
                  const supplier = suppliers.find(s => s.id === value);
                  setSelectedSupplier(supplier || null);
              }} value={field.value || 'none'}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a supplier" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  {suppliers.map((supplier) => (
                    <SelectItem key={supplier.id} value={supplier.id}>
                      {supplier.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {selectedSupplier && (
                <FormDescription className="text-xs mt-1">
                  Balance: 
                  <span className={cn(
                    "ml-1 font-medium",
                    selectedSupplier.balance > 0 ? "text-red-600" : 
                    selectedSupplier.balance < 0 ? "text-green-600" : ""
                  )}>
                    ${Math.abs(selectedSupplier.balance).toFixed(2)}
                    {selectedSupplier.balance > 0 ? " (you owe)" : selectedSupplier.balance < 0 ? " (credit)" : ""}
                  </span>
                </FormDescription>
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Purchase Details Section */}
        <div className="space-y-3">
          {/* Purchase Unit Selection - Always show */}
          <FormField
            control={form.control}
            name="purchaseUnitId"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center gap-2 mb-1">
                  <Package className="h-4 w-4 text-muted-foreground" />
                  <FormLabel className="mb-0">Unité d'achat</FormLabel>
                </div>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner l'unité d'achat" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {availablePurchaseUnits.map((unit) => (
                      <SelectItem key={unit.id} value={unit.id}>
                        <div className="flex items-center justify-between w-full">
                          <span className="font-medium">{unit.name}</span>
                          {unit.conversionToBase !== 1 && (
                            <span className="text-xs text-muted-foreground ml-2">
                              (1 = {unit.conversionToBase} {stockItem.unit})
                            </span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription className="text-xs">
                  {availablePurchaseUnits.length === 1 
                    ? `Achat direct en ${stockItem.unit}. Vous pouvez ajouter des unités d'achat (cartons, sacs, etc.) dans les paramètres de l'article.`
                    : "Comment achetez-vous cet article? (ex: par carton, plateau, sac, etc.)"
                  }
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2 mb-1">
                    <Package className="h-4 w-4 text-muted-foreground" />
                    <FormLabel className="mb-0">Quantité achetée</FormLabel>
                  </div>
                  
                  <div className="relative">
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="Combien?" 
                        min="0.01" 
                        step="0.01"
                        className="pr-20"
                        {...field} 
                      />
                    </FormControl>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-muted-foreground">
                      {selectedPurchaseUnit?.name || stockItem.unit}
                    </div>
                  </div>
                  
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="costPerUnit"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2 mb-1">
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                    <FormLabel className="mb-0">Prix par {selectedPurchaseUnit?.name || 'unité'}</FormLabel>
                  </div>
                  <div className="relative">
                    <span className="absolute left-3 top-2.5 text-muted-foreground">$</span>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="Prix unitaire" 
                        min="0.01" 
                        step="0.01"
                        className="pl-7"
                        {...field} 
                      />
                    </FormControl>
                  </div>
                  
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Conversion Preview Card */}
          {selectedPurchaseUnit && quantity > 0 && costPerUnit > 0 && (
            <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 transition-all duration-300 ease-in-out">
              <CardContent className="p-3 sm:p-4">
                <div className="flex items-center gap-2 mb-2 sm:mb-3">
                  <Calculator className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-semibold text-blue-900">Résumé de l'achat</span>
                </div>
                <div className="space-y-1.5 sm:space-y-2 text-sm">
                  <div className="flex justify-between items-center py-1">
                    <span className="text-blue-700 text-xs sm:text-sm">Vous achetez:</span>
                    <span className="font-semibold text-blue-900 bg-white px-2 py-1 rounded text-xs sm:text-sm">
                      {quantity} × {selectedPurchaseUnit.name}
                    </span>
                  </div>
                  {selectedPurchaseUnit.conversionToBase !== 1 && (
                    <div className="flex justify-between items-center py-1 border-t border-blue-200">
                      <span className="text-blue-700 text-xs sm:text-sm">Équivaut à:</span>
                      <span className="font-semibold text-blue-900 bg-white px-2 py-1 rounded text-xs sm:text-sm">
                        {baseQuantity.toFixed(3)} {stockItem.unit}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between items-center py-2 border-t border-blue-300 bg-blue-100 -mx-3 sm:-mx-4 px-3 sm:px-4 rounded-b">
                    <span className="text-blue-800 font-medium text-sm">Coût total:</span>
                    <span className="font-bold text-blue-900 text-base sm:text-lg">
                      ${totalCost.toFixed(2)}
                    </span>
                  </div>
                  {selectedPurchaseUnit.conversionToBase !== 1 && costPerBaseUnit > 0 && (
                    <div className="flex justify-between text-xs pt-1 text-blue-600">
                      <span>Coût par {stockItem.unit}:</span>
                      <span className="font-medium">
                        ${costPerBaseUnit.toFixed(4)}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Cost Summary */}
        <Card className={cn(
          "border border-muted bg-muted/30 p-3",
          totalCost > 0 && "bg-blue-50 border-blue-200"
        )}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-blue-500" />
              <span className="font-medium">Total: ${totalCost.toFixed(2)}</span>
            </div>
            {totalCost > 0 && (
              <span className="text-sm text-blue-700 bg-blue-100 px-2 py-0.5 rounded-full">
                {quantity} × ${typeof costPerUnit === 'number' ? costPerUnit.toFixed(2) : '0.00'}
              </span>
            )}
          </div>
        </Card>

        {/* Payment */}
        <FormField
          control={form.control}
          name="amountPaid"
          render={({ field }) => (
            <FormItem>
              <div className="flex items-center gap-2 mb-1">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <FormLabel className="mb-0">Amount Paid</FormLabel>
              </div>
              
              <div className="flex gap-2 mb-2">
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm"
                  className={cn("flex-1", amountPaid === 0 && "bg-blue-50 border-blue-200")}
                  onClick={() => form.setValue('amountPaid', 0)}
                >
                  Pay Later
                </Button>
                <Button 
                  type="button"
                  variant="outline"
                  size="sm"
                  className={cn("flex-1", amountPaid === totalCost && "bg-blue-50 border-blue-200")}
                  onClick={handleFullPayment}
                >
                  Pay in Full
                </Button>
              </div>
              
              <div className="relative">
                <span className="absolute left-3 top-2.5 text-muted-foreground">$</span>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="0.00" 
                    min="0" 
                    step="0.01" 
                    className="pl-7"
                    {...field} 
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end pt-2">
          <Button 
            type="submit" 
            className="bg-blue-600 hover:bg-blue-700"
            disabled={totalCost <= 0}
          >
            <ShoppingCart className="mr-2 h-4 w-4" />
            Complete Purchase
          </Button>
        </div>
      </form>
    </Form>
  );
} 