"use client";

// knowledge:start v4 fix - create a client wrapper for layout.tsx
import React, { useState, useEffect } from 'react';
import { SidebarProvider } from '@/components/ui/sidebar';
import { VersionChecker } from '@/components/version-checker';
import { Toaster } from 'sonner';
import { Button } from '@/components/ui/button';
import { RefreshCw, AlertCircle, Wifi, WifiOff } from 'lucide-react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { RestrictionGuard } from '@/components/auth/RestrictionGuard';
import { TrainingModeBanner } from '@/components/training-mode-banner';
// Removed StagewiseToolbar to prevent WebSocket reconnection spam

export function LayoutClientWrapper({
  children
}: {
  children: React.ReactNode
}) {
  // Track if we're in Electron to modify UI behavior
  const [isElectronApp, setIsElectronApp] = useState(false);
  // Track if we've mounted to prevent hydration issues
  const [mounted, setMounted] = useState(false);
  // Track loading timeout
  const [loadingTimeout, setLoadingTimeout] = useState(false);
  const [showRecovery, setShowRecovery] = useState(false);
  
  const { loading: authLoading, isOfflineMode, offlineLogin } = useAuth();

  useEffect(() => {
    setMounted(true);
    // Check if we're in Electron
    if (typeof window !== 'undefined') {
      setIsElectronApp(window.IS_DESKTOP_APP === true);
    }
  }, []);

  // Loading timeout detection
  useEffect(() => {
    if (!authLoading) {
      setLoadingTimeout(false);
      setShowRecovery(false);
      return;
    }

    // Set timeout for loading detection
    const timeoutId = setTimeout(() => {
      console.log('🕐 [Layout] Loading timeout detected - showing recovery options');
      setLoadingTimeout(true);
      setShowRecovery(true);
    }, 15000); // 15 second timeout

    return () => clearTimeout(timeoutId);
  }, [authLoading]);

  // Handle recovery actions
  const handleRefresh = () => {
    console.log('🔄 [Layout] User requested refresh');
    window.location.reload();
  };

  const handleOfflineMode = async () => {
    console.log('📱 [Layout] User requested offline mode');
    try {
      await offlineLogin('owner');
      setShowRecovery(false);
    } catch (error) {
      console.error('❌ [Layout] Offline login failed:', error);
    }
  };

  // Don't render anything until mounted (prevents hydration issues)
  if (!mounted) {
    return null;
  }

  // Show recovery UI if loading is stuck
  if (showRecovery && authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center space-y-6 max-w-md mx-auto p-6">
          <div className="space-y-2">
            <AlertCircle className="h-12 w-12 text-amber-500 mx-auto" />
            <h2 className="text-xl font-semibold">App Loading Issue</h2>
            <p className="text-sm text-muted-foreground">
              The app is taking longer than expected to load. This might be due to:
            </p>
            <ul className="text-xs text-muted-foreground text-left space-y-1 mt-4">
              <li>• Database initialization timeout</li>
              <li>• P2P sync service startup delay</li>
              <li>• Network connectivity issues</li>
              <li>• Electron API initialization problems</li>
            </ul>
          </div>
          
          <div className="space-y-3">
            <Button 
              onClick={handleRefresh}
              className="w-full"
              variant="default"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh App
            </Button>
            
            {isElectronApp && (
              <Button 
                onClick={handleOfflineMode}
                className="w-full"
                variant="outline"
              >
                {isOfflineMode ? (
                  <WifiOff className="h-4 w-4 mr-2" />
                ) : (
                  <Wifi className="h-4 w-4 mr-2" />
                )}
                Continue in Offline Mode
              </Button>
            )}
          </div>
          
          <div className="text-xs text-muted-foreground">
            If this problem persists, try restarting the application.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen-mobile">
      <SidebarProvider
        style={{
          "--sidebar-width": "16rem",
          "--sidebar-width-mobile": "18rem"
        } as React.CSSProperties}
      >
        <TrainingModeBanner />
        <RestrictionGuard>
          {children}
        </RestrictionGuard>
        <Toaster />
        {isElectronApp && <VersionChecker />}
      </SidebarProvider>
    </div>
  );
}
// knowledge:end