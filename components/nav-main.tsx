"use client"

import React from "react"
import { type LucideIcon } from "lucide-react"
import Link from "next/link"
import { useStaticNavigation, isStaticMode } from "@/lib/utils/navigation"
import { useMobileLayout } from "@/hooks/use-mobile-layout"
import { cn } from "@/lib/utils"

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

export type NavItem = {
  title: string
  url: string
  icon?: LucideIcon
  isActive?: boolean
};

export function NavMain({
  items,
  isMobile = false,
}: {
  items: NavItem[];
  isMobile?: boolean;
}) {
  const { navigate } = useStaticNavigation()

  // Handle navigation click
  const handleNavClick = (href: string, e: React.MouseEvent) => {
    if (isStaticMode()) {
      e.preventDefault()
      const cleanPath = href.replace(/^\//, '')
      navigate(cleanPath)
    }
    // In dynamic mode, let Link handle it normally
  }

  return (
    <SidebarGroup className={cn(isMobile ? "py-1" : "")}>
      <SidebarMenu className={cn(isMobile ? "space-y-0.5" : "")}>
        {items.map((item) => (
          <SidebarMenuItem key={item.title}>
            <SidebarMenuButton 
              asChild 
              tooltip={item.title}
              isActive={item.isActive}
              className={cn(
                isMobile ? "h-8 px-2 text-xs font-medium" : ""
              )}
            >
              <Link
                href={item.url}
                onClick={(e) => handleNavClick(item.url, e)}
                className={cn(
                  "flex items-center gap-2",
                  isMobile ? "gap-1.5" : "gap-2"
                )}
              >
                {item.icon && (
                  <item.icon className={cn(isMobile ? "size-3.5" : "")} />
                )}
                <span className={cn(isMobile ? "text-xs" : "")}>{item.title}</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
