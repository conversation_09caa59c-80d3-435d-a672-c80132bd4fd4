'use client';

import { useEffect } from 'react';
import { useDesktopRegistration } from '@/lib/hooks/use-desktop-registration';

interface SimpleRegistrationProviderProps {
  children: React.ReactNode;
}

export function SimpleRegistrationProvider({ children }: SimpleRegistrationProviderProps) {
  const registration = useDesktopRegistration();

  useEffect(() => {
    console.log('🏭 [SimpleRegistrationProvider] Provider mounted');
    
    // Log registration status changes
    if (registration.isRegistered) {
      console.log('✅ [SimpleRegistrationProvider] Desktop registered as internet sync server');
    } else if (registration.error) {
      console.log('⚠️ [SimpleRegistrationProvider] Registration error:', registration.error);
    }
  }, [registration.isRegistered, registration.error]);

  // Provider doesn't render anything, just runs the registration service
  return <>{children}</>;
}