import React, { useEffect, useState } from 'react';
// Removed Button, Receipt, Eye, Printer as they are managed by parent component
// import { Button } from '@/components/ui/button';
import { Printer } from 'lucide-react';
import { kitchenPrintService } from '@/lib/services/kitchen-print-service';
import { Order } from '@/lib/db/v4/schemas/order-schema';
import { formatCurrency } from '@/lib/utils/currency';

// Local replacements for removed print-service helpers
const generateRestaurantHeaderHtml = async ({ header = 12, normal = 10, bold = 11 } = {}) => {
  // Lightweight header builder that uses passed-in restaurant info from props
  return `
    <div style="text-align: center; margin-bottom: 6px;">
      ${/* Show logo when available */ ''}
      <div style="font-size: ${header}px; font-weight: 700;">${/* name injected by caller */ ''}</div>
    </div>
  `;
};

const generateRestaurantFooterHtml = ({ normal = 9 } = {}) => {
  return `
    <div style="text-align: center; margin-top: 6px; font-size: ${normal}px; color: #444;">
      ${/* footer injected by caller */ ''}
    </div>
  `;
};

interface ReceiptPreviewProps {
  className?: string;
  restaurantName: string;
  restaurantPhone: string;
  restaurantAddress: string;
  restaurantSecondaryPhone: string;
  restaurantLogoUrl: string;
  restaurantFooter: string;
}

export function ReceiptPreview({
  className,
  restaurantName,
  restaurantPhone,
  restaurantAddress,
  restaurantSecondaryPhone,
  restaurantLogoUrl,
  restaurantFooter,
}: ReceiptPreviewProps) {
  const [receiptHtml, setReceiptHtml] = useState('');
  const [loading, setLoading] = useState(false);

  // Sample order for preview
  const sampleOrder: Order = {
    _id: 'order:20250721-001',
    id: 'order:20250721-001',
    type: 'order_document',
    schemaVersion: 'v4.0',
    tableId: 'table-5',
    orderType: 'dine-in',
    items: [
      {
        id: 'item-1',
        name: 'Pizza Margherita',
        price: 1200,
        quantity: 1,
        menuItemId: 'pizza-margherita',
        addons: [
          { id: 'addon-1', name: 'Extra Cheese', price: 0 }
        ]
      },
      {
        id: 'item-2',
        name: 'Coca Cola',
        price: 300,
        quantity: 2,
        menuItemId: 'coca-cola'
      },
      {
        id: 'item-3',
        name: 'Salade César',
        price: 0,
        quantity: 0, // Voided item
        menuItemId: 'salade-cesar',
        isVoided: true,
        originalQuantity: 1,
        voidedQuantity: 1
      }
    ],
    total: 1200, // Pizza + 2 Cokes (voided salad excluded)
    status: 'completed',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  const samplePayment = {
    method: 'cash',
    received: 1600, 
    change: 400
  };

  const generatePreview = async () => {
    setLoading(true);
    try {
      // Build header HTML locally (print-service removed)
      let restaurantHeaderHtml = await generateRestaurantHeaderHtml({
        header: 12,
        normal: 10,
        bold: 11,
      });

      // Inject values into header
      restaurantHeaderHtml = restaurantHeaderHtml.replace('<div style="font-size: 12px; font-weight: 700;">', `<div style="font-size: 12px; font-weight: 700;">${restaurantName || ''}`);

      const orderItemsHtml = sampleOrder.items
        .map((item) => {
          const itemTotal = item.isVoided ? 0 : item.price * item.quantity;
          const addonsHtml = item.addons
            ?.map((addon) => `
              <div style="padding-left: 12px; font-size: 8px; color: #666; line-height: 1.2; margin-bottom: 1px;">
                + ${addon.name} <span style="float: right;">${formatCurrency(addon.price)}</span>
              </div>
            `)
            .join('');

          return `
            <div style="margin-bottom: 4px;">
              <div style="display: flex; justify-content: space-between; align-items: flex-start; font-size: 10px; line-height: 1.3;">
                <div style="flex: 1; padding-right: 8px;">
                  <span style="font-weight: 500;">${item.name}</span>
                  ${item.isVoided ? '<span style="color: #e74c3c; font-size: 8px; margin-left: 4px;">(REMBOURSÉ)</span>' : ''}
                  <div style="font-size: 8px; color: #666; margin-top: 1px;">x${item.quantity || item.originalQuantity || 1}</div>
                </div>
                <div style="font-weight: 500; text-align: right;">
                  ${formatCurrency(itemTotal)}
                </div>
              </div>
              ${addonsHtml || ''}
            </div>
          `;
        })
        .join('');

  let footerHtml = generateRestaurantFooterHtml({ normal: 9 });
  footerHtml = footerHtml.replace('', restaurantFooter || '');

      const fullReceiptHtml = `
        <div style="font-family: 'Liberation Mono', monospace; font-size: 10px; line-height: 1.3; color: #000; width: 220px; margin: 0 auto; padding: 8px; box-sizing: border-box; background-color: #fff;">
          ${restaurantHeaderHtml}
          
          <div style="text-align: center; margin: 8px 0; border-bottom: 1px solid #ddd; padding-bottom: 6px;">
            <div style="font-size: 12px; font-weight: bold; letter-spacing: 1px; margin-bottom: 3px;">RECEIPT</div>
            <div style="font-size: 9px; color: #666;">Order #${sampleOrder._id.split('-').pop()}</div>
            <div style="font-size: 9px; color: #666;">${new Date(sampleOrder.createdAt!).toLocaleString('fr-FR', { 
              day: '2-digit', 
              month: '2-digit', 
              year: 'numeric', 
              hour: '2-digit', 
              minute: '2-digit' 
            })}</div>
          </div>
          
          <div style="margin: 8px 0;">
            ${orderItemsHtml}
          </div>
          
          <div style="border-top: 1px dashed #999; padding-top: 6px; margin-top: 8px;">
            <div style="display: flex; justify-content: space-between; font-size: 12px; font-weight: bold; margin-bottom: 4px;">
              <span>TOTAL:</span>
              <span>${formatCurrency(sampleOrder.total)}</span>
            </div>
          </div>
          
          ${footerHtml}
        </div>
      `;

      setReceiptHtml(fullReceiptHtml);

    } catch (error) {
      console.error('Failed to generate receipt preview:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Regenerate preview whenever relevant props change
    generatePreview();
  }, [restaurantName, restaurantPhone, restaurantAddress, restaurantSecondaryPhone, restaurantLogoUrl, restaurantFooter]);

  return (
    <div className={className}>
      {receiptHtml ? (
        <div className="border rounded-lg bg-white shadow-sm flex justify-center items-center overflow-hidden" style={{ width: '240px', height: 'auto', padding: '4px' }}>
          <div 
            className="receipt-content-wrapper"
            dangerouslySetInnerHTML={{ __html: receiptHtml }}
            style={{
              transform: 'scale(0.95)',
              transformOrigin: 'top center'
            }}
          />
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center h-64 border rounded-lg p-4 text-center text-muted-foreground bg-muted/50 w-full">
          <Printer className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p className="text-sm">No preview available</p>
          <p className="text-xs">Configure restaurant information to see preview</p>
        </div>
      )}
    </div>
  );
} 