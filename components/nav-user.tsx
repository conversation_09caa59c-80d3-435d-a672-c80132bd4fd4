"use client"

import {
  Badge<PERSON>he<PERSON>,
  Bell,
  ChevronsUpDown,
  CreditCard,
  LogOut,
  Moon,
  Settings,
  Sun,
  Trash2,
  User,
  Cloud,
  Wifi,
  Network,
  GraduationCap,
} from "lucide-react"

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import Link from "next/link"
import { useAuth } from "@/lib/context/multi-user-auth-provider"
import { useTheme } from "next-themes"
import { useRouter } from "next/navigation"
import { CacheCleaner } from "@/components/CacheCleaner"
import { Capacitor } from "@capacitor/core"
import { SwitchUserButton } from "@/components/multi-user/SwitchUserButton"
import { UserSwitchDialog } from "@/components/multi-user/UserSwitchDialog"
import { useStaticNavigation, isStaticMode } from "@/lib/utils/navigation"
import { P2PDebugInterface } from "@/components/debug/P2PDebugInterface"
import { useState } from "react"
import { cn } from "@/lib/utils"
import { useTrainingMode } from "@/lib/context/training-mode-provider"

export function NavUser({
  user,
  extraLinks = [],
  isMobile = false,
}: {
  user: {
    name: string
    email: string
    avatar: string
  },
  extraLinks?: { title: string; url: string; icon: React.ElementType; mobileOnly?: boolean }[]
  isMobile?: boolean;
}) {
  const { isMobile: sidebarMobile, toggleSidebar } = useSidebar()
  const { logout } = useAuth()
  const { theme, setTheme } = useTheme()
  const router = useRouter()
  const isNativePlatform = Capacitor.isNativePlatform()
  const { navigate } = useStaticNavigation()
  const [isP2PDebugOpen, setIsP2PDebugOpen] = useState(false)
  const [isUserSwitchDialogOpen, setIsUserSwitchDialogOpen] = useState(false)
  const { isTrainingMode, toggleTrainingMode, clearTrainingData, isClearing } = useTrainingMode()

  // Filter links based on mobileOnly flag
  const filteredLinks = extraLinks.filter(link => 
    !link.mobileOnly || (link.mobileOnly && isNativePlatform)
  );

  const handleSignOut = async () => {
    logout();
  };

  // Handle navigation click
  const handleNavClick = (href: string, e: React.MouseEvent) => {
    if (isStaticMode()) {
      e.preventDefault()
      const cleanPath = href.replace(/^\//, '')
      navigate(cleanPath)
    }
    // In dynamic mode, let Link handle it normally
  }

  const handleClearTrainingData = async () => {
    try {
      await clearTrainingData()
      // Optionally show a success message
    } catch (error) {
      console.error('Failed to clear training data:', error)
      // Optionally show an error message
    }
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size={isMobile ? "sm" : "lg"}
              className={cn(
                "data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",
                isMobile ? "h-8 px-2" : ""
              )}
            >
              <Avatar className={cn(isMobile ? "h-5 w-5 rounded-md" : "h-8 w-8 rounded-lg")}>
                {user.avatar ? (
                  <AvatarImage src={user.avatar} alt={user.name} />
                ) : (
                  <AvatarFallback className={cn(isMobile ? "rounded-md text-[10px]" : "rounded-lg")}>
                    {user.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                )}
              </Avatar>
              <div className={cn(
                "grid flex-1 text-left leading-tight",
                isMobile ? "text-xs" : "text-sm"
              )}>
                <span className={cn(
                  "truncate font-semibold",
                  isMobile ? "text-[11px]" : ""
                )}>{user.name}</span>
                <span className={cn(
                  "truncate text-muted-foreground",
                  isMobile ? "text-[9px]" : "text-xs"
                )}>{user.email}</span>
              </div>
              <ChevronsUpDown className={cn(
                "ml-auto",
                isMobile ? "size-3" : "size-4"
              )} />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={sidebarMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  {user.avatar ? (
                    <AvatarImage src={user.avatar} alt={user.name} />
                  ) : (
                    <AvatarFallback className="rounded-lg">CN</AvatarFallback>
                  )}
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">{user.name}</span>
                  <span className="truncate text-xs">{user.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            
            {/* Extra Links */}
            {filteredLinks.length > 0 && (
              <>
                <DropdownMenuGroup>
                  {filteredLinks.map((link) => (
                    <DropdownMenuItem key={link.url} asChild>
                      <Link href={link.url} onClick={(e) => handleNavClick(link.url, e)} className="flex w-full items-center">
                        <link.icon className="h-4 w-4 mr-2" />
                        {link.title}
                      </Link>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
              </>
            )}

            {/* Training Mode Toggle */}
            <DropdownMenuItem onClick={toggleTrainingMode}>
              <GraduationCap className={cn(
                "h-4 w-4 mr-2",
                isTrainingMode ? "text-orange-600" : "text-muted-foreground"
              )} />
              Training Mode
              {isTrainingMode && (
                <span className="ml-auto text-xs bg-orange-100 text-orange-800 px-1.5 py-0.5 rounded">ON</span>
              )}
            </DropdownMenuItem>


            {/* Theme Toggle */}
            <DropdownMenuItem onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>
              {theme === 'dark' ? (
                <>
                  <Sun className="h-4 w-4 mr-2" />
                  Light Mode
                </>
              ) : (
                <>
                  <Moon className="h-4 w-4 mr-2" />
                  Dark Mode
                </>
              )}
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem asChild>
                <Link href="/p2p-sync" onClick={(e) => handleNavClick("/p2p-sync", e)} className="flex w-full items-center">
                  <Wifi className="h-4 w-4 mr-2" />
                  Sync Monitor
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setIsP2PDebugOpen(true)} className="cursor-pointer flex items-center">
                <Network className="h-4 w-4 mr-2" />
                P2P Debug
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/settings" onClick={(e) => handleNavClick("/settings", e)} className="flex w-full items-center">
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </Link>
              </DropdownMenuItem>
              
              {/* Cache Cleaner */}
              {process.env.NODE_ENV === 'development' && (
                <DropdownMenuItem className="cursor-pointer flex items-center">
                  <Trash2 className="h-4 w-4 mr-2 text-destructive" />
                  <CacheCleaner />
                </DropdownMenuItem>
              )}
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            
            {/* Use the new secure switch user button */}
            <DropdownMenuItem onClick={() => {
              console.log('🔄 Switch User clicked in nav-user');
              setIsUserSwitchDialogOpen(true);
            }}>
              <User className="h-4 w-4 mr-2" />
              Switch User
            </DropdownMenuItem>
            
            <DropdownMenuItem onClick={handleSignOut}>
              <LogOut className="h-4 w-4 mr-2" />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
      
      {/* P2P Debug Modal */}
      <Dialog open={isP2PDebugOpen} onOpenChange={setIsP2PDebugOpen}>
        <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Network className="h-5 w-5" />
              P2P Sync Debug Interface
            </DialogTitle>
          </DialogHeader>
          <P2PDebugInterface />
        </DialogContent>
      </Dialog>

      {/* User Switch Dialog */}
      <UserSwitchDialog 
        open={isUserSwitchDialogOpen} 
        onOpenChange={setIsUserSwitchDialogOpen} 
      />
    </SidebarMenu>
  )
}
