"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  <PERSON>er,
  <PERSON>an,
  Users,
  Info,
  Wifi,
  Search,
  Eye,
  TestTube,
  Zap,
  FileText
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { kitchenPrintService, PrintJob, PrintingFeatures } from '@/lib/services/kitchen-print-service';
import { simpleHTMLPrintService } from '@/lib/services/simple-html-print';

import { Order, OrderItem } from '@/lib/db/v4/schemas/order-schema';
import { v4 as uuidv4 } from 'uuid';
import { getMenu } from '@/lib/db/v4/operations/menu-ops';

// 🆕 Define the structure of an OS-level printer retrieved from Electron
interface OSPrinterInfo {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'unknown';
  rawStatus: number; // The raw numerical status code from the OS
  type: 'thermal' | 'inkjet' | 'laser' | string;
  description?: string;
  isDefault?: boolean;
  ipAddress?: string; // Add ipAddress as it's part of the returned data
}

// Types

interface PrinterDevice {
  id: string;
  name: string;
  ipAddress?: string;
  status: 'online' | 'offline' | 'unknown';
  assignedCategories: string[];
  type: 'thermal' | 'inkjet' | 'laser';
  isReceiptPrinter?: boolean;
  simulated: boolean;
  enabled?: boolean; // Flag to track if printer is enabled/selected
}

interface BarcodeScannerDevice {
  id: string;
  name: string;
  status: 'connected' | 'disconnected';
  batteryLevel?: number;
  enabled?: boolean;
}

interface KitchenPrintingSetupProps {
  categories: Array<{
    id: string;
    name: string;
    emoji?: string;
  }>;
}


export function KitchenPrintingSetup({ categories }: KitchenPrintingSetupProps) {
  const { toast } = useToast();
  const [printingFeatures, setPrintingFeatures] = useState<PrintingFeatures>({ queueEnabled: true, barcodeEnabled: true });
  const [printers, setPrinters] = useState<PrinterDevice[]>([]);
  const [scanners, setScanners] = useState<BarcodeScannerDevice[]>([]);

  // 🆕 OS-level printer discovery state
  const [isDiscoveringPrinters, setIsDiscoveringPrinters] = useState(false);

  // State for Quick Preview
  

  useEffect(() => {
    // Load printing features from the service
    const features = kitchenPrintService.getPrintingFeatures();
    setPrintingFeatures(features);
  }, []);

  // 🆕 Always fetch fresh printer data from OS
  useEffect(() => {
    const loadPrinters = async () => {
      try {
        console.log('🔄 Loading printers on component mount...');
        // Always discover fresh OS printers - no delete functionality
        await discoverOSPrinters();
      } catch (error) {
        console.warn('Failed to load printers:', error);
      }
    };

    loadPrinters();
  }, []);

  // 🔄 Auto-refresh printers every 30 seconds to check status
  useEffect(() => {
    const interval = setInterval(async () => {
      await discoverOSPrinters();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, []);

  // 🆕 Detect USB/Bluetooth scanners (only when barcode feature is enabled)
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    const fetchScanners = async () => {
      // Only discover scanners if barcode feature is enabled
      if (!printingFeatures.barcodeEnabled) {
        setScanners([]);
        return;
      }

      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        try {
          const usbDevices = await (window as any).electronAPI.invoke('get-usb-devices');
          const scanners = usbDevices.filter((d: any) =>
            d.deviceClass === 3 && d.deviceName && (d.deviceName.toLowerCase().includes('scanner') || d.deviceName.toLowerCase().includes('barcode'))
          ).map((d: any, idx: number) => ({
            id: d.deviceAddress || d.deviceName || `scanner-${idx}`,
            name: d.deviceName || `Scanner ${idx + 1}`,
            status: 'connected',
            batteryLevel: undefined,
            enabled: false,
          }));
          setScanners(scanners);
        } catch (err) {
          setScanners([]);
        }
      } else {
        setScanners([]);
      }
    };
    fetchScanners();
    interval = setInterval(fetchScanners, 2000);
    return () => { if (interval) clearInterval(interval); };
  }, [printingFeatures.barcodeEnabled]);

  const handleFeatureToggle = (feature: keyof PrintingFeatures) => {
    const newFeatures = { ...printingFeatures, [feature]: !printingFeatures[feature] };
    setPrintingFeatures(newFeatures);
    kitchenPrintService.setPrintingFeatures(newFeatures);

    const featureName = feature === 'queueEnabled' ? 'Queue System' : 'Barcode Scanning';
    const status = newFeatures[feature] ? 'enabled' : 'disabled';

    toast({
      title: `${featureName} ${status}! ${newFeatures[feature] ? '✅' : '❌'}`,
      description: `${featureName} is now ${status} for kitchen printing`,
    });
  };

  const handlePrinterSelect = (printerId: string) => {
    // Toggle printer enabled state and save to service
    setPrinters(prev => {
      const updatedPrinters = prev.map(printer => 
        printer.id === printerId ? { ...printer, enabled: !printer.enabled } : printer
      );
      
      // Save to kitchen print service
      try {
        const { kitchenPrintService } = require('@/lib/services/kitchen-print-service');
        kitchenPrintService.setPrinters(updatedPrinters);
      } catch (error) {
        console.warn('Failed to save printer selection:', error);
      }
      
      return updatedPrinters;
    });
  };

  const handleScannerSelect = (scannerId: string) => {
    setScanners(prev => prev.map(scanner => 
      scanner.id === scannerId ? { ...scanner, enabled: !scanner.enabled } : scanner
    ));
  };

  const assignCategoryToPrinter = (printerId: string, categoryName: string) => {
    setPrinters(prev => {
      const updatedPrinters = prev.map(printer => {
        if (printer.id === printerId) {
          const newCategories = printer.assignedCategories.includes(categoryName)
            ? printer.assignedCategories.filter(name => name !== categoryName)
            : [...printer.assignedCategories, categoryName];
          return { ...printer, assignedCategories: newCategories };
        }
        return printer;
      });

      // 🆕 Save to kitchen print service
      try {
        const { kitchenPrintService } = require('@/lib/services/kitchen-print-service');
        kitchenPrintService.setPrinters(updatedPrinters);
      } catch (error) {
        console.warn('Failed to save printer configuration:', error);
      }

      return updatedPrinters;
    });
  };

  const toggleReceiptPrinter = (printerId: string) => {
    setPrinters(prev => {
      const updatedPrinters = prev.map(printer => ({
        ...printer,
        isReceiptPrinter: printer.id === printerId ? !printer.isReceiptPrinter : false // Only one receipt printer allowed
      }));

      // 🆕 Save to kitchen print service
      try {
        const { kitchenPrintService } = require('@/lib/services/kitchen-print-service');
        kitchenPrintService.setPrinters(updatedPrinters);
      } catch (error) {
        console.warn('Failed to save printer configuration:', error);
      }

      return updatedPrinters;
    });
  };

  // 🆕 Discover OS-level printers
  const discoverOSPrinters = async () => {
    setIsDiscoveringPrinters(true);
    try {
      let osPrinters: any[] = [];

      // Try Electron API first
      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        try {
          console.log('🔍 Discovering system printers...');

          // Call the real API
          const realPrinters = await (window as any).electronAPI.invoke('get-system-printers') as OSPrinterInfo[];
          if (realPrinters && realPrinters.length > 0) {
            osPrinters = realPrinters;
            console.log('✅ Real system printers discovered:', realPrinters);
          } else {
            console.log('📝 No system printers found on this device');

            // Show helpful message for macOS
            if (navigator.userAgent.includes('Mac')) {
              toast({
                title: "No Printers Found on macOS",
                description: "Make sure printers are added in System Preferences > Printers & Scanners and are online.",
              });
            }
          }

        } catch (error) {
          console.warn('Electron printer discovery failed:', error);
          toast({
            title: "Printer Discovery Error",
            description: `Failed to discover system printers: ${error instanceof Error ? error.message : 'Unknown error'}`,
            variant: "destructive"
          });
          osPrinters = [];
        }
      } else {
        console.log('📱 Running in browser mode - no system printer access');
        osPrinters = [];
      }

      // Log discovered printers
      console.log(`🔍 Discovered ${osPrinters.length} OS printers`);

      // Always update existing kitchen service printers (even if no OS printers found)
      const { kitchenPrintService } = await import('@/lib/services/kitchen-print-service');
      const existingPrinters = await kitchenPrintService.getPrinters();

      if (osPrinters.length > 0) {
        // Update existing printers with current status from OS discovery
        const updatedPrinters = existingPrinters.map(printer => {
          const osMatch = osPrinters.find(os => os.id === printer.id || os.name === printer.name);
          if (osMatch) {
            return {
              ...printer,
              status: osMatch.status,
              ipAddress: osMatch.ipAddress || printer.ipAddress
            };
          }
          return { ...printer, status: 'offline' as const }; // Mark as offline if not found
        });

        // Add new OS printers that aren't in the kitchen service yet
        const newOSPrinters = osPrinters.filter(osPrinter =>
          !existingPrinters.some(existing => existing.id === osPrinter.id || existing.name === osPrinter.name)
        ).map(osPrinter => ({
          id: osPrinter.id,
          name: osPrinter.name,
          ipAddress: osPrinter.ipAddress,
          status: osPrinter.status || 'offline' as const, // Show offline printers too
          assignedCategories: [] as string[],
          type: osPrinter.type || 'thermal' as const,
          simulated: false,
          isReceiptPrinter: false,
          enabled: false
        }));

        const allPrinters = [...updatedPrinters, ...newOSPrinters];

        // Always save and display all discovered printers (including offline ones)
        await kitchenPrintService.setPrinters(allPrinters);
        setPrinters(allPrinters);

        console.log(`🖨️ Updated printer status: ${allPrinters.length} total printers (including offline)`);
        console.log('Printers:', allPrinters.map(p => `${p.name} (${p.status})`));

        // Force UI update
        setTimeout(() => {
          console.log('🔄 Force updating UI state with printers:', allPrinters.length);
          setPrinters([...allPrinters]);
        }, 100);
      } else {
        // No OS printers found - just update UI with existing printers (may be empty in production)
        setPrinters(existingPrinters);
        console.log(`📋 No OS printers found. Showing ${existingPrinters.length} existing printers`);
        console.log('Existing printers:', existingPrinters);
      }

      // Debug: Log final printer state
      console.log('🔍 Final printer state in UI:', printers.length > 0 ? printers : 'No printers in state');

    } catch (error) {
      console.warn('OS printer discovery failed:', error);
      // Don't show error toast on every refresh, just log it
    } finally {
      setIsDiscoveringPrinters(false);
    }
  };

  // 🔄 Refresh printer status manually
  const handleRefreshPrinters = async () => {
    toast({
      title: "🔄 Refreshing Printers...",
      description: "Checking printer status and discovering new devices"
    });
    await discoverOSPrinters();
    toast({
      title: "✅ Printers Refreshed!",
      description: "Printer status updated"
    });
  };





  // 🆕 Generate Sample Order for Preview (using real menu data)
  const generateSampleOrder = async (): Promise<Order> => {
    const date = new Date();
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');

    // 🎯 FIX: Use new order ID format for preview consistency
    const dateStr = `${year}${month}${day}`;
    const orderId = `order:${dateStr}-001`; // Use new format: order:YYYYMMDD-XXX

    const sampleItems: OrderItem[] = [];
    let hasRealItems = false;

    try {
      const menu = await getMenu();
      const availableCategories = menu.categories.filter(cat => cat.items.length > 0);

      if (availableCategories.length >= 2) {
        // Pick one item from each of the first two categories
        for (let i = 0; i < 2; i++) {
          const category = availableCategories[i];
          const item = category.items[0]; // Take the first item from the category
          if (item) {
            sampleItems.push({
              id: uuidv4(),
              menuItemId: item.id,
              name: item.name,
              quantity: i % 2 === 0 ? 1 : 2, // Vary quantity
              price: item.prices[Object.keys(item.prices)[0]] || 0, // Take first available price
              size: Object.keys(item.prices)[0] || 'Regular',
              notes: i % 2 === 0 ? 'Extra notes for item' : '',
              categoryName: category.name,
              addons: i % 2 !== 0 ? [{ id: uuidv4(), name: 'Sample Addon', price: 5 }] : []
            });
            hasRealItems = true;
          }
        }
      } else if (availableCategories.length === 1) {
        // If only one category, take one item from it
        const category = availableCategories[0];
        const item = category.items[0];
        if (item) {
          sampleItems.push({
            id: uuidv4(),
            menuItemId: item.id,
            name: item.name,
            quantity: 1,
            price: item.prices[Object.keys(item.prices)[0]] || 0,
            size: Object.keys(item.prices)[0] || 'Regular',
            notes: 'Single category item',
            categoryId: category.id,
            addons: []
          });
          hasRealItems = true;
        }
      }
    } catch (error) {
      console.warn('Failed to fetch real menu data for sample order:', error);
      // Fallback to dummy data if menu fetching fails
      hasRealItems = false;
    }

    if (!hasRealItems || sampleItems.length === 0) {
      // Fallback if no categories or items are loaded, or if fetching failed
      sampleItems.push({
        id: uuidv4(),
        menuItemId: 'dummy-item-1',
        name: 'Sample Mock Item 1',
        quantity: 1,
        price: 120,
        size: 'Medium',
        notes: 'No onions',
        categoryId: 'Uncategorized',
        addons: []
      });
      sampleItems.push({
        id: uuidv4(),
        menuItemId: 'dummy-item-2',
        name: 'Sample Mock Item 2',
        quantity: 2,
        price: 80,
        size: 'Small',
        notes: '',
        categoryId: 'Uncategorized',
        addons: [{ id: uuidv4(), name: 'Extra Sauce', price: 5 }]
      });
    }

    const total = sampleItems.reduce((sum, item) => {
      const itemPrice = item.price + (item.addons?.reduce((addonSum, addon) => addonSum + addon.price, 0) || 0);
      return sum + itemPrice * item.quantity;
    }, 0);

    return {
      _id: orderId,
      id: orderId,
      type: "order_document",
      schemaVersion: "v4.0",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tableId: "TEST-TABLE",
      status: "pending",
      orderType: "dine-in",
      items: sampleItems,
      total: total,
      notes: "This is a sample order for printer preview testing using real/mock data.",
      paymentStatus: "paid",
      paymentMethod: "cash"
    };
  };

  const testPrinter = async (printerName: string) => {
    if (!printerName) {
      toast({
        title: "❌ No Printer Selected",
        description: "Please select a printer to test.",
        variant: "destructive"
      });
      return;
    }

    toast({
      title: "🖨️ Testing Printer...",
      description: `Sending test print to ${printerName}`
    });

    try {
      // Check if we're in Electron environment
      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        console.log(`🖨️ Testing printer: ${printerName}`);
        
        // Use simple HTML print test instead of Electron API
        const result = await simpleHTMLPrintService.print({
          id: simpleHTMLPrintService.generateJobId(),
          title: `Printer Test - ${printerName}`,
          type: 'receipt',
          content: `
=====================
PRINTER TEST
=====================

Printer: ${printerName}
Test Time: ${new Date().toLocaleString()}

If you can read this clearly,
your printer is working!

✅ HTML Print System
✅ Universal Compatibility
✅ Real User Feedback

Test completed successfully.

=====================
          `
        });
        
        if (result.success) {
          toast({
            title: "✅ Print Test Window Opened!",
            description: `Click the Print button in the new window to test ${printerName}`,
          });
          
          // Refresh printer status after successful test
          await discoverOSPrinters();
        } else {
          toast({
            title: "❌ Test Print Failed",
            description: result.error || "Could not open print window - please allow pop-ups",
            variant: "destructive"
          });
        }
      } else {
        // Even in web environment, HTML printing should work
        const result = await simpleHTMLPrintService.print({
          id: simpleHTMLPrintService.generateJobId(),
          title: `Printer Test - ${printerName}`,
          type: 'receipt',
          content: `
=====================
PRINTER TEST
=====================

Printer: ${printerName}
Test Time: ${new Date().toLocaleString()}

This works in web browsers too!

✅ HTML Print System
✅ Universal Compatibility
✅ Cross-platform Support

Test completed successfully.

=====================
          `
        });

        if (result.success) {
          toast({
            title: "✅ Print Test Window Opened!",
            description: `Click the Print button in the new window to test printing`,
          });
        } else {
          toast({
            title: "❌ Test Failed",
            description: result.error || "Could not open print window - please allow pop-ups",
            variant: "destructive"
          });
        }
      }
    } catch (error) {
      console.error('Error testing printer:', error);
      toast({
        title: "❌ Test Failed",
        description: error instanceof Error ? error.message : "Could not open print window - please allow pop-ups",
        variant: "destructive"
      });
    }
  };

  const handleQuickPreview = async () => {
    const order = await generateSampleOrder(); // Await the async function

    try {
      const result = await kitchenPrintService.printKitchenOrder(order, order.tableId, { fontSize: 'medium' });

      if (result.success) {
        toast({
          title: "✅ Sample Print Sent!",
          description: "A sample print job has been sent to your configured printers.",
        });
      } else {
        toast({
          title: "Preview Generation Failed",
          description: result.error || "An unknown error occurred during preview generation.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error during quick preview:', error);
      toast({
        title: "Error",
        description: "Failed to generate quick preview. Check console for details.",
        variant: "destructive"
      });
    }
  };

  return (
    <TooltipProvider>
      <div className="space-y-2 max-w-2xl mx-auto">
        {/* System Info Section */}
        <div className="border rounded-md p-2 mb-2 bg-blue-50/50 border-blue-200">
          <div className="flex items-center gap-2 text-xs">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="font-medium text-blue-900">Multi-Ticket Kitchen System</span>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-3 w-3 text-blue-600 cursor-pointer" />
              </TooltipTrigger>
              <TooltipContent side="top" className="text-xs max-w-xs">
                <div className="space-y-1">
                  <div className="font-medium">Unified printing system that:</div>
                  <div>• Generates separate tickets per category</div>
                  <div>• Works with single or multiple printers</div>
                  <div>• Optional queue coordination & barcode tracking</div>
                </div>
              </TooltipContent>
            </Tooltip>
            <span className="text-blue-700 ml-auto">Active</span>
          </div>
        </div>

        {/* Feature Toggles Section - Compact Style */}
        <div className="border rounded-md divide-y bg-muted/30 mb-4">
          <div className="px-2 py-1.5 bg-muted/50">
            <h3 className="font-semibold text-xs flex items-center gap-2">
              🖨️ Kitchen Printing Features
            </h3>
          </div>

          <div className="flex items-center gap-2 px-2 py-1 text-xs">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="font-medium">Queue System</span>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-3 w-3 text-muted-foreground cursor-pointer" />
              </TooltipTrigger>
              <TooltipContent side="top" className="text-xs max-w-xs">
                Shows queue coordination info on tickets to help kitchen stations coordinate timing
              </TooltipContent>
            </Tooltip>
            <div className="ml-auto flex items-center gap-2">
              <span className="text-xs text-muted-foreground">
                {printingFeatures.queueEnabled ? 'Enabled' : 'Disabled'}
              </span>
              <Switch
                checked={printingFeatures.queueEnabled}
                onCheckedChange={() => handleFeatureToggle('queueEnabled')}
                className="scale-75"
              />
            </div>
          </div>

          <div className="flex items-center gap-2 px-2 py-1 text-xs">
            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            <span className="font-medium">Barcode Scanning</span>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-3 w-3 text-muted-foreground cursor-pointer" />
              </TooltipTrigger>
              <TooltipContent side="top" className="text-xs max-w-xs">
                Generates barcodes on tickets for completion tracking and automatic expo notifications
              </TooltipContent>
            </Tooltip>
            <div className="ml-auto flex items-center gap-2">
              <span className="text-xs text-muted-foreground">
                {printingFeatures.barcodeEnabled ? 'Enabled' : 'Disabled'}
              </span>
              <Switch
                checked={printingFeatures.barcodeEnabled}
                onCheckedChange={() => handleFeatureToggle('barcodeEnabled')}
                className="scale-75"
              />
            </div>
          </div>
        </div>
        <div className="flex items-center gap-4 text-xs mb-1">
          {printers.length > 0 && (
            <span className="flex items-center gap-1 text-green-700"><Printer className="h-4 w-4" />{printers.length} printers</span>
          )}
          {printingFeatures.barcodeEnabled && scanners.length > 0 && (
            <span className="flex items-center gap-1 text-purple-700"><Scan className="h-4 w-4" />{scanners.length} scanners</span>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={handleQuickPreview}
            className="ml-auto flex items-center gap-1"
          >
            <Eye className="h-4 w-4" />
            Quick Preview
          </Button>
        </div>
        {/* Helper text above printers */}
        <div className="mb-1 text-xs text-muted-foreground flex items-center justify-between">
          <div className="flex items-center gap-2">
            Select printers and assign categories (stations)
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-3 w-3 ml-1 text-muted-foreground cursor-pointer" />
                </TooltipTrigger>
                <TooltipContent side="top" className="text-xs max-w-xs">
                  Select printers to use for kitchen orders. Assign menu categories to each printer so items are printed at the correct station.
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Button
            variant="outline"
            size="sm"
            className="h-6 px-2 text-xs"
            onClick={handleRefreshPrinters}
            disabled={isDiscoveringPrinters}
          >
            <Search className="h-3 w-3 mr-1" />
            {isDiscoveringPrinters ? 'Discovering...' : 'Refresh'}
          </Button>
        </div>


        {printers.length > 0 ? (
          <div className="border rounded-md divide-y bg-muted/30">
            {printers.filter(printer => {
              // Filter out simulated printers in production
              const isProduction = process.env.NODE_ENV === 'production';
              const isElectron = typeof window !== 'undefined' && (window as any).electronAPI;
              if (isProduction && isElectron && printer.simulated) {
                return false;
              }
              return true;
            }).map((printer) => {
              const selected = printer.enabled || false;
              return (
                <div key={printer.id} className="space-y-2">
                  <div
                    className={cn(
                      "flex items-center gap-2 px-2 py-1 text-xs transition-colors",
                      selected ? "bg-primary/10" : ""
                    )}
                  >
                    <input
                      type="checkbox"
                      checked={printer.enabled || false}
                      onChange={() => handlePrinterSelect(printer.id)}
                      className="accent-primary h-4 w-4"
                      name="printer-select"
                    />
                    <Printer className="h-4 w-4" />
                    <span className="font-medium truncate flex-1">
                      {printer.name}
                      {printer.isReceiptPrinter && (
                        <Badge variant="default" className="ml-1 px-1 py-0 text-[10px] bg-green-600">Receipt</Badge>
                      )}
                    </span>
                    {printer.ipAddress && (
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <Wifi className="h-3 w-3" />
                        <span className="text-xs">{printer.ipAddress}</span>
                      </div>
                    )}
                    <div className="flex items-center gap-1">
                      <div className={cn(
                        "w-2 h-2 rounded-full",
                        printer.status === 'online' ? "bg-green-500" :
                          printer.status === 'offline' ? "bg-red-500" : "bg-gray-500"
                      )} />
                      <span className={cn(
                        "text-xs",
                        printer.status === 'online' ? "text-green-700" :
                          printer.status === 'offline' ? "text-red-700" : "text-gray-700"
                      )}>
                        {printer.status === 'online' ? 'Online' :
                          printer.status === 'offline' ? 'Offline' : 'Unknown'}
                      </span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-6 px-2 text-xs mr-1"
                      onClick={() => testPrinter(printer.name)}
                      disabled={!selected}
                    >
                      🖨️ Test
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-6 px-2 text-xs"
                      onClick={() => toggleReceiptPrinter(printer.id)}
                      disabled={!selected}
                    >
                      {printer.isReceiptPrinter ? 'Remove Receipt' : 'Set as Receipt'}
                    </Button>
                  </div>

                  {/* Category assignment */}
                  {selected && categories && categories.length > 0 && (
                    <div className="px-2 pb-2">
                      <div className="text-xs text-muted-foreground mb-1">Assign categories to this printer:</div>
                      <div className="flex flex-wrap gap-1">
                        {categories.filter(cat => cat && cat.id && cat.name).map((category) => {
                          const isAssigned = printer.assignedCategories.includes(category.name);
                          return (
                            <Button
                              key={category.id}
                              variant={isAssigned ? "default" : "outline"}
                              size="sm"
                              className={cn(
                                "h-6 px-2 text-xs",
                                isAssigned && "bg-primary text-primary-foreground"
                              )}
                              onClick={() => assignCategoryToPrinter(printer.id, category.name)}
                            >
                              <span className="mr-1">{category.emoji || '🍽️'}</span>
                              {category.name}
                            </Button>
                          );
                        })}
                      </div>
                    </div>
                  )}
                  {selected && (!categories || categories.length === 0) && (
                    <div className="px-2 pb-2 text-xs text-muted-foreground">
                      No menu categories found. Add categories in Menu Management first.
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 gap-2 text-center border rounded-md bg-muted/30">
            <Printer className="h-8 w-8 text-muted-foreground mb-1" />
            <div className="text-sm text-muted-foreground font-medium">No printers found</div>
            <div className="text-xs text-muted-foreground max-w-sm">
              📡 Click "Refresh" to discover network printers or install a thermal printer driver and restart the app
            </div>
            <div className="flex gap-2 mt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefreshPrinters}
                disabled={isDiscoveringPrinters}
                className="flex items-center gap-2"
              >
                <Search className="h-4 w-4" />
                {isDiscoveringPrinters ? 'Scanning...' : 'Scan for Printers'}
              </Button>


            </div>
          </div>
        )}



        {printingFeatures.barcodeEnabled && (
          scanners.length > 0 ? (
            <div className="border rounded-md divide-y bg-muted/30 mt-1">
              {scanners.map((scanner) => (
                <div key={scanner.id} className="flex items-center gap-2 px-2 py-1 text-xs">
                  <input
                    type="checkbox"
                    checked={scanner.enabled || false}
                    onChange={() => handleScannerSelect(scanner.id)}
                    className="accent-primary h-4 w-4"
                    name="scanner-select"
                  />
                  <Scan className="h-4 w-4 mr-1" />
                  <span className="font-medium truncate max-w-[120px]">{scanner.name}</span>
                  <span className="ml-auto text-muted-foreground">{scanner.status}</span>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-4 gap-1 text-center border rounded-md bg-muted/30 mt-1">
              <Scan className="h-6 w-6 text-muted-foreground" />
              <div className="text-xs text-muted-foreground font-medium">No USB barcode scanners found</div>
              <div className="text-xs text-muted-foreground">Connect a scanner to get started.</div>
            </div>
          )
        )}
        {(printers.length > 0 || (printingFeatures.barcodeEnabled && scanners.length > 0)) && (
          <div className="flex gap-4 mt-1 text-xs justify-center">
            <span className="flex items-center gap-1 text-green-700">
              <Printer className="h-4 w-4" />
              {printers.filter(p => p.status === 'online').length}/{printers.length} online
            </span>
            {printers.filter(p => p.status === 'offline').length > 0 && (
              <span className="flex items-center gap-1 text-red-700">
                🔴 {printers.filter(p => p.status === 'offline').length} offline
              </span>
            )}
            {printers.length > 0 && (
              <span className="flex items-center gap-1 text-blue-700">
                <Users className="h-4 w-4" />
                {printers.reduce((acc, p) => acc + p.assignedCategories.length, 0)} assignments
              </span>
            )}
          </div>
        )}
        

        {/* 🧪 Printer Testing Component */}
        <PrinterTestingComponent printers={printers} />
      </div>
    </TooltipProvider>
  );
}

// 🧪 Printer Testing Component
interface PrinterTestingComponentProps {
  printers: PrinterDevice[];
}

function PrinterTestingComponent({ printers }: PrinterTestingComponentProps) {
  const { toast } = useToast();
  const [selectedPrinter, setSelectedPrinter] = useState<string>('');
  const [testResults, setTestResults] = useState<Record<string, any>>({});
  const [isTestingThermal, setIsTestingThermal] = useState(false);
  // Removed webContents test - now using universal printing for everything
  const [isTestingHTML, setIsTestingHTML] = useState(false);

  // Test 1: Universal printer approach (works with ANY printer)
  const testThermalPrinter = async () => {
    if (!selectedPrinter) {
      toast({
        title: "No Printer Selected",
        description: "Please select a printer to test",
        variant: "destructive"
      });
      return;
    }

    setIsTestingThermal(true);
    try {
      console.log('🧪 Testing simple HTML printer approach...');

      // Use simple HTML print service
      const result = await simpleHTMLPrintService.print({
        id: simpleHTMLPrintService.generateJobId(),
        title: `Printer Test - ${selectedPrinter}`,
        type: 'kitchen',
        content: `
=======================
PRINTER TEST
=======================

Printer: ${selectedPrinter}
Test Time: ${new Date().toLocaleString()}
Method: Simple HTML Print

This test opens a print window
where you can choose your printer
and see exactly what will print.

✅ Universal compatibility
✅ Real user feedback  
✅ Works with all printers

If you can read this clearly,
your printer setup is working!

=======================
        `
      });

      setTestResults(prev => ({
        ...prev,
        thermal: result
      }));

      if (result.success) {
        toast({
          title: "✅ Print Test Window Opened",
          description: `Click the Print button in the new window to test ${selectedPrinter}`,
        });
      } else {
        toast({
          title: "❌ Print Test Failed",
          description: result.error || "Could not open print window - please allow pop-ups",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('HTML print test error:', error);
      toast({
        title: "❌ Print Test Error",
        description: error instanceof Error ? error.message : "Could not open print test - please allow pop-ups",
        variant: "destructive"
      });
    } finally {
      setIsTestingThermal(false);
    }
  };

  // Note: webContents test removed - now using universal printing for everything

  // Test 3: HTML-based printing (create hidden iframe and print)
  const testHTMLPrint = async () => {
    if (!selectedPrinter) {
      toast({
        title: "No Printer Selected",
        description: "Please select a printer to test",
        variant: "destructive"
      });
      return;
    }

    setIsTestingHTML(true);
    try {
      console.log('🧪 Testing HTML iframe print approach...');

      // Create a hidden iframe with print content
      const iframe = document.createElement('iframe');
      iframe.style.position = 'absolute';
      iframe.style.left = '-9999px';
      iframe.style.width = '1px';
      iframe.style.height = '1px';

      document.body.appendChild(iframe);

      const printContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Test Print</title>
          <style>
            body {
              font-family: monospace;
              font-size: 12px;
              margin: 0;
              padding: 10px;
              width: 58mm; /* Thermal printer width */
            }
            .center { text-align: center; }
            .bold { font-weight: bold; }
            .line { border-bottom: 1px dashed #000; margin: 5px 0; }
          </style>
        </head>
        <body>
          <div class="center bold">TEST PRINT</div>
          <div class="line"></div>
          <div>Time: ${new Date().toLocaleTimeString()}</div>
          <div>Date: ${new Date().toLocaleDateString()}</div>
          <div class="line"></div>
          <div>This is a test print using HTML iframe method</div>
          <div>Printer: ${selectedPrinter}</div>
          <div>Method: HTML + window.print()</div>
          <div class="line"></div>
          <div class="center">Thank you!</div>
        </body>
        </html>
      `;

      iframe.contentDocument?.open();
      iframe.contentDocument?.write(printContent);
      iframe.contentDocument?.close();

      // Wait for content to load then print
      iframe.onload = () => {
        try {
          iframe.contentWindow?.print();

          setTestResults(prev => ({
            ...prev,
            html: { success: true, method: 'HTML iframe + window.print()' }
          }));

          toast({
            title: "✅ HTML Print Test Initiated",
            description: "Print dialog should have opened. Check if it printed correctly.",
          });

          // Clean up
          setTimeout(() => {
            document.body.removeChild(iframe);
          }, 1000);
        } catch (error) {
          console.error('HTML print error:', error);
          toast({
            title: "❌ HTML Print Test Failed",
            description: error instanceof Error ? error.message : "Unknown error",
            variant: "destructive"
          });
          document.body.removeChild(iframe);
        }
      };

    } catch (error) {
      console.error('HTML print test error:', error);
      toast({
        title: "❌ HTML Print Test Error",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive"
      });
    } finally {
      setIsTestingHTML(false);
    }
  };

  if (printers.length === 0) {
    return null;
  }

  return (
    <div className="mt-6 p-4 border rounded-lg bg-yellow-50 border-yellow-200">
      <div className="flex items-center gap-2 mb-4">
        <TestTube className="h-5 w-5 text-yellow-600" />
        <h3 className="font-semibold text-yellow-800">🧪 Printer Testing Lab</h3>
      </div>

      <div className="space-y-4">
        {/* Printer Selection */}
        <div>
          <label className="block text-sm font-medium mb-2">Select Printer for Testing:</label>
          <select
            value={selectedPrinter}
            onChange={(e) => setSelectedPrinter(e.target.value)}
            className="w-full p-2 border rounded-md text-sm"
          >
            <option value="">Choose a printer...</option>
            {printers.map(printer => (
              <option key={printer.id} value={printer.name}>
                {printer.name} ({printer.status})
              </option>
            ))}
          </select>
        </div>

        {/* Test Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <Button
            onClick={testThermalPrinter}
            disabled={!selectedPrinter || isTestingThermal}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Zap className="h-4 w-4" />
            {isTestingThermal ? 'Testing...' : 'Test Universal (80mm)'}
          </Button>

          <Button
            onClick={testHTMLPrint}
            disabled={!selectedPrinter || isTestingHTML}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <FileText className="h-4 w-4" />
            {isTestingHTML ? 'Testing...' : 'Test HTML Print'}
          </Button>
        </div>

        {/* Test Results */}
        {Object.keys(testResults).length > 0 && (
          <div className="mt-4 p-3 bg-white border rounded-md">
            <h4 className="font-medium mb-2">Test Results:</h4>
            <div className="space-y-2 text-sm">
              {testResults.thermal && (
                <div className="p-2 bg-gray-50 rounded">
                  <strong>Universal (80mm):</strong>
                  <span className={testResults.thermal.success ? 'text-green-600 ml-2' : 'text-red-600 ml-2'}>
                    {testResults.thermal.success ? '✅ Success' : '❌ Failed'}
                  </span>
                  {testResults.thermal.error && (
                    <div className="text-red-600 text-xs mt-1">{testResults.thermal.error}</div>
                  )}
                </div>
              )}



              {testResults.html && (
                <div className="p-2 bg-gray-50 rounded">
                  <strong>HTML Print:</strong>
                  <span className={testResults.html.success ? 'text-green-600 ml-2' : 'text-red-600 ml-2'}>
                    {testResults.html.success ? '✅ Initiated' : '❌ Failed'}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}