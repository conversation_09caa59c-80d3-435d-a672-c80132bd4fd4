
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { deleteAllOrders } from '@/lib/db/v4/operations/order-ops';
import { Trash2 } from 'lucide-react';

export function ClearOrdersSettings() {
  const [password, setPassword] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleClearOrders = async () => {
    if (password !== 'dada1234') {
      toast({
        title: 'Error',
        description: 'Incorrect password.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    try {
      const deletedCount = await deleteAllOrders();
      toast({
        title: 'Success',
        description: `Successfully cleared ${deletedCount} orders.`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to clear orders.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setIsOpen(false);
      setPassword('');
    }
  };

  return (
    <Card className="border-red-500">
      <CardHeader>
        <CardTitle className="text-red-600 flex items-center gap-2">
          <Trash2 className="h-5 w-5" />
          Danger Zone
        </CardTitle>
        <CardDescription>
          This action is irreversible. Please proceed with caution.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button variant="destructive">Clear All Orders</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Are you absolutely sure?</DialogTitle>
              <DialogDescription>
                This action cannot be undone. This will permanently delete all orders.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsOpen(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleClearOrders} disabled={isLoading}>
                {isLoading ? 'Clearing...' : 'Clear Orders'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
