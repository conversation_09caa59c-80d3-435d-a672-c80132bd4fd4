import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Upload, Store, Phone, MapPin, Image, MessageSquare, Check, AlertCircle, Printer } from 'lucide-react';
import { toast } from 'sonner';
// print-service removed; no-op for header/footer generation
import { getSettings, updateSettings } from '@/lib/db/v4/operations/settings-ops';
import { ReceiptPreview } from '@/components/print/ReceiptPreview';
import { cn } from '@/lib/utils';

export function RestaurantInfoSettings() {
  const [restaurantName, setRestaurantName] = useState('');
  const [restaurantPhone, setRestaurantPhone] = useState('');
  const [restaurantAddress, setRestaurantAddress] = useState('');
  const [restaurantSecondaryPhone, setRestaurantSecondaryPhone] = useState('');
  const [restaurantLogoUrl, setRestaurantLogoUrl] = useState('');
  const [restaurantFooter, setRestaurantFooter] = useState('Merci de votre visite!');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState('');

  // Load settings on component mount
  useEffect(() => {
    async function loadSettings() {
      setLoading(true);
      try {
        const dbSettings = await getSettings();
        setRestaurantName(dbSettings.restaurantName || '');
        setRestaurantPhone(dbSettings.restaurantPhone || '');
        setRestaurantAddress(dbSettings.restaurantAddress || '');
        setRestaurantSecondaryPhone(dbSettings.restaurantSecondaryPhone || '');
        setRestaurantLogoUrl(dbSettings.restaurantLogoUrl || '');
        setRestaurantFooter(dbSettings.restaurantFooter || 'Merci de votre visite!');
        
        // Set logo preview if URL exists
        if (dbSettings.restaurantLogoUrl) {
          setLogoPreview(dbSettings.restaurantLogoUrl);
        }
      } catch (error) {
        console.error('Failed to load restaurant settings:', error);
        toast.error('Failed to load restaurant settings');
      } finally {
        setLoading(false);
      }
    }
    loadSettings();
  }, []);

  // Handle logo file selection
  const handleLogoFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select an image file');
        return;
      }
      
      // Validate file size (max 2MB)
      if (file.size > 2 * 1024 * 1024) {
        toast.error('Image file must be less than 2MB');
        return;
      }
      
      setLogoFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setLogoPreview(result);
        setRestaurantLogoUrl(result); // Set as data URL for now
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle save
  const handleSave = async () => {
    setSaving(true);
    try {
      // Update database
      await updateSettings({
        restaurantName,
        restaurantPhone,
        restaurantAddress,
        restaurantSecondaryPhone,
        restaurantLogoUrl,
        restaurantFooter
      });
      
      // Persist restaurant info for local preview/cache
      try {
        localStorage.setItem('restaurant_info', JSON.stringify({
          restaurantName,
          restaurantPhone,
          restaurantAddress,
          restaurantSecondaryPhone,
          restaurantLogoUrl,
          restaurantFooter
        }));
      } catch (e) {
        // ignore localStorage failures in strict environments
      }
      
      toast.success('Restaurant information saved successfully! 🏪');
    } catch (error) {
      console.error('Failed to save restaurant settings:', error);
      toast.error('Failed to save restaurant settings');
    } finally {
      setSaving(false);
    }
  };

  // Check if any required info is missing (secondary phone is optional)
  const hasBasicInfo = restaurantName || restaurantPhone || restaurantAddress;
  const isComplete = restaurantName && restaurantPhone && restaurantAddress;

  if (loading) {
    return (
      <div className="flex flex-col md:flex-row gap-6 p-6 border rounded-lg animate-pulse">
        <div className="md:w-1/2 space-y-4">
          <div className="h-6 bg-muted-foreground/20 rounded w-3/4"></div>
          <div className="h-4 bg-muted-foreground/20 rounded w-1/2"></div>
          {[...Array(6)].map((_, i) => (
            <div key={i} className="h-10 bg-muted-foreground/20 rounded"></div>
          ))}
          <div className="h-10 bg-muted-foreground/20 rounded w-1/4 self-end"></div>
        </div>
        <div className="md:w-1/2 flex items-center justify-center bg-muted/50 rounded-lg p-4">
          <div className="h-64 w-full bg-muted-foreground/20 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col md:flex-row gap-6">
      {/* Restaurant Information Form */}
      <div className="md:w-1/2 space-y-4">
        <div className="flex items-center justify-between pb-4">
          <div>
            <div className="flex items-center gap-2 text-lg font-semibold">
              <Store className="h-5 w-5" />
              Restaurant Information
            </div>
            <div className="text-sm text-muted-foreground mt-1">
              Configure your restaurant details for receipts and prints
            </div>
          </div>
          <div className="flex items-center gap-2">
            {hasBasicInfo ? (
              isComplete ? (
                <Badge variant="default" className="bg-green-100 text-green-800 border-green-300">
                  <Check className="h-3 w-3 mr-1" />
                  Complete
                </Badge>
              ) : (
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-300">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Partial
                </Badge>
              )
            ) : (
              <Badge variant="outline" className="text-muted-foreground">
                Not configured
              </Badge>
            )}
          </div>
        </div>
        <div className="space-y-3">
          {/* Restaurant Name */}
          <div className="space-y-1">
            <Label htmlFor="restaurant-name" className="flex items-center gap-2 text-sm">
              <Store className="h-4 w-4" />
              Restaurant Name
            </Label>
            <Input
              id="restaurant-name"
              value={restaurantName}
              onChange={(e) => setRestaurantName(e.target.value)}
              placeholder="Enter your restaurant name"
              className="font-medium"
            />
          </div>

          {/* Primary Phone Number */}
          <div className="space-y-1">
            <Label htmlFor="restaurant-phone" className="flex items-center gap-2 text-sm">
              <Phone className="h-4 w-4" />
              Primary Phone Number
            </Label>
            <Input
              id="restaurant-phone"
              value={restaurantPhone}
              onChange={(e) => setRestaurantPhone(e.target.value)}
              placeholder="Enter your primary phone number"
              className="font-medium"
              type="tel"
            />
          </div>

          {/* Address */}
          <div className="space-y-1">
            <Label htmlFor="restaurant-address" className="flex items-center gap-2 text-sm">
              <MapPin className="h-4 w-4" />
              Address
            </Label>
            <Input
              id="restaurant-address"
              value={restaurantAddress}
              onChange={(e) => setRestaurantAddress(e.target.value)}
              placeholder="Enter your restaurant address"
            />
          </div>

          {/* Secondary Phone */}
          <div className="space-y-1">
            <Label htmlFor="restaurant-secondary-phone" className="flex items-center gap-2 text-sm">
              <Phone className="h-4 w-4" />
              Secondary Phone Number
            </Label>
            <Input
              id="restaurant-secondary-phone"
              value={restaurantSecondaryPhone}
              onChange={(e) => setRestaurantSecondaryPhone(e.target.value)}
              placeholder="Enter your secondary phone number (optional)"
              type="tel"
            />
          </div>

          <Separator className="my-4"/>

          {/* Logo Upload */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2 text-sm">
              <Image className="h-4 w-4" />
              Restaurant Logo
            </Label>
            
            <div className="flex items-start gap-3">
              {/* Logo Preview */}
              <div className="flex-shrink-0">
                {logoPreview ? (
                  <div className="relative">
                    <img
                      src={logoPreview}
                      alt="Restaurant Logo Preview"
                      className="w-20 h-20 object-contain border rounded-lg bg-muted"
                    />
                    <Button
                      variant="destructive"
                      size="sm"
                      className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                      onClick={() => {
                        setLogoPreview('');
                        setRestaurantLogoUrl('');
                        setLogoFile(null);
                      }}
                    >
                      ×
                    </Button>
                  </div>
                ) : (
                  <div className="w-20 h-20 border-2 border-dashed border-muted-foreground/25 rounded-lg flex items-center justify-center bg-muted/50">
                    <Image className="h-7 w-7 text-muted-foreground/50" />
                  </div>
                )}
              </div>

              {/* Upload Controls */}
              <div className="flex-1 space-y-1">
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => document.getElementById('logo-upload')?.click()}
                    className="flex items-center gap-2 text-xs py-1 px-2 h-auto"
                  >
                    <Upload className="h-3 w-3" />
                    Upload Logo
                  </Button>
                  <input
                    id="logo-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleLogoFileChange}
                    className="hidden"
                  />
                </div>
                
                <div className="text-xs text-muted-foreground leading-tight">
                  <p>• Recommended: 200x60px ratio</p>
                  <p>• Max file size: 2MB (JPG, PNG, GIF, WebP)</p>
                </div>

                {/* Manual URL Input */}
                <div className="space-y-1 mt-2">
                  <Label htmlFor="logo-url" className="text-xs">Or enter logo URL:</Label>
                  <Input
                    id="logo-url"
                    value={restaurantLogoUrl}
                    onChange={(e) => {
                      setRestaurantLogoUrl(e.target.value);
                      setLogoPreview(e.target.value);
                      setLogoFile(null);
                    }}
                    placeholder="https://example.com/logo.png"
                    className="text-xs h-8"
                  />
                </div>
              </div>
            </div>
          </div>

          <Separator className="my-4"/>

          {/* Footer Message */}
          <div className="space-y-1">
            <Label htmlFor="restaurant-footer" className="flex items-center gap-2 text-sm">
              <MessageSquare className="h-4 w-4" />
              Receipt Footer Message
            </Label>
            <Input
              id="restaurant-footer"
              value={restaurantFooter}
              onChange={(e) => setRestaurantFooter(e.target.value)}
              placeholder="Thank you message for receipts"
              className="text-sm"
            />
            <p className="text-xs text-muted-foreground leading-tight">
              This message will appear at the bottom of customer receipts
            </p>
          </div>

          {/* Save Button */}
          <div className="flex justify-end pt-3">
            <Button 
              onClick={handleSave} 
              disabled={saving}
              className="min-w-[100px] text-sm h-9"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Save Settings
                </>
              )}
            </Button>
          </div>

          {/* Preview Note */}
          {hasBasicInfo && (
            <div className="p-3 text-sm text-muted-foreground border border-dashed border-muted-foreground/25">
              💡 <strong>Preview:</strong> Your restaurant information will appear on customer receipts. 
              Only filled fields will be displayed - empty fields are automatically hidden.
            </div>
          )}
        </div>
      </div>

      {/* Receipt Preview */}
      <div className="md:w-1/2 space-y-4">
        <div className="flex items-center justify-between pb-4">
          <div className="flex items-center gap-2 text-lg font-semibold">
            <Printer className="h-5 w-5" />
            Receipt Preview
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Trigger a re-render of ReceiptPreview by updating its key or props
              // For now, no explicit refresh needed as it reacts to prop changes
            }}
            className="flex items-center gap-2 text-xs py-1 px-2 h-auto"
          >
            <Check className="h-3 w-3 mr-1" />
            Refresh Preview
          </Button>
        </div>
        <div className="w-full flex justify-center py-4 bg-muted rounded-lg border border-muted-foreground/20">
          <ReceiptPreview
            restaurantName={restaurantName}
            restaurantPhone={restaurantPhone}
            restaurantAddress={restaurantAddress}
            restaurantSecondaryPhone={restaurantSecondaryPhone}
            restaurantLogoUrl={restaurantLogoUrl}
            restaurantFooter={restaurantFooter}
          />
        </div>
        <div className="text-sm text-muted-foreground space-y-2 mt-4 w-full">
          <p>• Only configured restaurant info will appear</p>
          <p>• Empty fields are automatically hidden</p>
          <p>• Logo, name, address, and phone in compact layout</p>
          <p>• Voided items show as "REMBOURSÉ" with 0.00 DA</p>
          <p>• Total reflects actual amount (excluding voids)</p>
          <p>• Footer message appears at the bottom</p>
        </div>
      </div>
    </div>
  );
}