import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Clock, Database, AlertTriangle, CheckCircle2, Trash2 } from 'lucide-react';
import { clientBackupService, BackupMetadata, BackupStatus } from '@/lib/services/client-backup-service';


interface CloudBackupFile {
  id: string;
  name: string;
  createdTime: string;
  size: number;
}

export default function BackupSettings({ restaurantId }: { restaurantId: string }) {
  const [status, setStatus] = useState<BackupStatus>({ isRunning: false, googleDriveConnected: false });
  const [localBackups, setLocalBackups] = useState<BackupMetadata[]>([]);
  const [cloudBackups, setCloudBackups] = useState<CloudBackupFile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [autoCloudBackup, setAutoCloudBackup] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info' | 'warning'; text: string } | null>(null);
  const [restoreDialogOpen, setRestoreDialogOpen] = useState(false);
  const [selectedBackup, setSelectedBackup] = useState<BackupMetadata | null>(null);
  const [isElectron, setIsElectron] = useState(false);

  useEffect(() => {
    // Check if we're in Electron environment
    setIsElectron(typeof window !== 'undefined' && !!(window as any).electronAPI);
    loadData();
  }, [restaurantId]);

  const loadData = async () => {
    if (!isElectron) {
      setMessage({ 
        type: 'warning', 
        text: 'Backup functionality is only available in the desktop application.' 
      });
      return;
    }

    try {
      setIsLoading(true);
      
      // Load backup status using client service
      const statusData = await clientBackupService.getStatus();
      setStatus(statusData);
      setAutoCloudBackup(statusData.googleDriveConnected);

      // Load local backups using client service
      const localBackupsData = await clientBackupService.listLocalBackups();
      setLocalBackups(localBackupsData);

      // Cloud backups functionality removed (focusing on local backups only)
      setCloudBackups([]);

    } catch (error) {
      console.error('Failed to load backup data:', error);
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Failed to load backup data' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const connectGoogleDrive = async () => {
    setMessage({ type: 'info', text: 'Google Drive connection not available in offline mode. Use local backups instead.' });
  };

  const createBackup = async () => {
    if (!isElectron) {
      setMessage({ type: 'warning', text: 'Backup functionality is only available in the desktop application.' });
      return;
    }

    try {
      setIsLoading(true);
      setMessage({ type: 'info', text: 'Creating filesystem backup...' });
      
      const result = await clientBackupService.createBackup(restaurantId);
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Filesystem backup created successfully!' });
        loadData();
      } else {
        setMessage({ type: 'error', text: result.error || 'Backup failed' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to create backup' });
    } finally {
      setIsLoading(false);
    }
  };

  const restoreBackup = async (timestamp: number, isCloud = false, fileId?: string) => {
    try {
      setIsLoading(true);
      setMessage({ type: 'info', text: 'Restoring backup...' });
      
      // Only support local backups for now
      if (isCloud) {
        setMessage({ type: 'error', text: 'Cloud backup restore not supported in offline mode' });
        return;
      }
      
      const result = await clientBackupService.restoreFromBackup(timestamp, restaurantId);
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Database restored successfully! Changes will be visible immediately.' });
        loadData(); // Refresh the backup list
      } else {
        setMessage({ type: 'error', text: result.error || 'Restore failed' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to restore backup' });
    } finally {
      setIsLoading(false);
      setRestoreDialogOpen(false);
      setSelectedBackup(null);
    }
  };

  const handleRestoreClick = (backup: BackupMetadata) => {
    setSelectedBackup(backup);
    setRestoreDialogOpen(true);
  };

  const confirmRestore = () => {
    if (selectedBackup) {
      restoreBackup(selectedBackup.timestamp);
    }
  };

  const toggleAutoCloudBackup = async (enabled: boolean) => {
    setMessage({ type: 'info', text: 'Cloud backup functionality not available in offline mode' });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - timestamp;
    
    // Format: "Aug 17, 2:45 PM (3 min ago)"
    const timeStr = date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
    
    // Add relative time
    const minutes = Math.floor(diff / (60 * 1000));
    const hours = Math.floor(diff / (60 * 60 * 1000));
    const days = Math.floor(diff / (24 * 60 * 60 * 1000));
    
    let relativeTime;
    if (days > 0) relativeTime = `${days}d ago`;
    else if (hours > 0) relativeTime = `${hours}h ago`;
    else if (minutes > 0) relativeTime = `${minutes}m ago`;
    else relativeTime = 'Just now';
    
    return `${timeStr} (${relativeTime})`;
  };

  return (
    <div className="space-y-6">
      {message && (
        <Alert className={
          message.type === 'error' ? 'border-red-500 bg-red-50 text-red-900' : 
          message.type === 'success' ? 'border-green-500 bg-green-50 text-green-900' : 
          message.type === 'warning' ? 'border-orange-500 bg-orange-50 text-orange-900' :
          'border-blue-500 bg-blue-50 text-blue-900'
        }>
          <div className="flex items-center gap-2">
            {message.type === 'success' && <CheckCircle2 className="h-4 w-4" />}
            {message.type === 'error' && <AlertTriangle className="h-4 w-4" />}
            {message.type === 'warning' && <AlertTriangle className="h-4 w-4" />}
            {message.type === 'info' && <Database className="h-4 w-4" />}
            <AlertDescription className="font-medium">{message.text}</AlertDescription>
          </div>
        </Alert>
      )}

      {/* Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            {isElectron ? 'Filesystem Backup System' : 'Backup System'}
            {isElectron ? (
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                Desktop Ready
              </Badge>
            ) : (
              <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                Desktop Only
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            {isElectron 
              ? 'Robust backups stored on your device filesystem using CouchDB' 
              : 'Backup functionality requires the desktop application'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!isElectron ? (
            <div className="text-center py-8">
              <AlertTriangle className="h-12 w-12 text-orange-500 mx-auto mb-4" />
              <p className="text-muted-foreground font-medium mb-2">Desktop Application Required</p>
              <p className="text-sm text-muted-foreground">
                Download and use the desktop version for robust filesystem backups
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {status.lastBackup && (
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Last backup:</span>
                  <span className="text-muted-foreground">
                    {formatTimestamp(status.lastBackup)}
                  </span>
                </div>
              )}
              
              <div className="grid grid-cols-2 gap-4 pt-2">
                <div className="space-y-1">
                  <div className="text-sm font-medium">Storage Type</div>
                  <div className="text-sm text-muted-foreground">Device Filesystem</div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm font-medium">Total Backups</div>
                  <div className="text-sm text-muted-foreground">{localBackups.length} stored</div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Actions */}
      {isElectron && (
        <Card>
          <CardHeader>
            <CardTitle>Create Backup</CardTitle>
            <CardDescription>
              Manually create a complete backup of your restaurant data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  <strong>Secure Storage:</strong> Backups are stored on your device filesystem. 
                  They survive app updates and system reboots.
                </AlertDescription>
              </Alert>
              
              <Button 
                onClick={createBackup} 
                disabled={isLoading}
                className="w-full"
                size="lg"
              >
                {isLoading ? 'Creating Filesystem Backup...' : 'Create Backup Now'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Local Backups */}
      {isElectron && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Available Backups</span>
              {localBackups.length > 0 && (
                <Badge variant="secondary">
                  {localBackups.length} backup{localBackups.length !== 1 ? 's' : ''}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Restore your restaurant data from any of these filesystem backups
            </CardDescription>
          </CardHeader>
          <CardContent>
            {localBackups.length === 0 ? (
              <div className="text-center py-12">
                <Database className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground font-medium">No backups found</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Create your first backup to get started
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {localBackups.slice(0, 10).map((backup) => (
                  <div key={backup.timestamp} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <div className="font-medium">{backup.date}</div>
                        <Badge variant="outline" className="text-xs">
                          {backup.docCount} docs
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {formatFileSize(backup.size)} • {formatTimestamp(backup.timestamp)}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleRestoreClick(backup)}
                        disabled={isLoading}
                        className="text-red-600 border-red-200 hover:bg-red-50"
                      >
                        Restore
                      </Button>
                    </div>
                  </div>
                ))}
                
                {localBackups.length > 10 && (
                  <div className="text-center pt-2">
                    <p className="text-sm text-muted-foreground">
                      Showing 10 most recent backups of {localBackups.length} total
                    </p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Restore Confirmation Dialog */}
      <AlertDialog open={restoreDialogOpen} onOpenChange={setRestoreDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Confirm Database Restore
            </AlertDialogTitle>
            <AlertDialogDescription>
              This will <strong>completely replace</strong> your current restaurant data with the backup from{' '}
              <strong>{selectedBackup?.date}</strong> containing{' '}
              <strong>{selectedBackup?.docCount} documents</strong>.
              
              <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded text-red-800 text-sm">
                <strong>⚠️ WARNING:</strong> This action cannot be undone. Your current data will be lost.
                A safety backup will be created automatically before restoring.
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmRestore}
              className="bg-red-600 hover:bg-red-700"
            >
              Yes, Restore Backup
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

    </div>
  );
}