"use client";

import { GraduationCap, X } from "lucide-react";
import { useTrainingMode } from "@/lib/context/training-mode-provider";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export function TrainingModeBanner() {
  const { isTrainingMode, toggleTrainingMode } = useTrainingMode();

  if (!isTrainingMode) {
    return null;
  }

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50">
      <div className={cn(
        "bg-orange-500 text-white shadow-lg rounded-lg border border-orange-600",
        "px-3 py-1.5 max-w-fit",
        "flex items-center gap-2",
        "animate-in slide-in-from-top-2 duration-300"
      )}>
        <GraduationCap className="h-4 w-4 flex-shrink-0" />
        <span className="text-sm font-medium whitespace-nowrap">
          Training Mode
        </span>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleTrainingMode}
          className={cn(
            "h-5 w-5 p-0 flex-shrink-0 ml-1",
            "hover:bg-white/20 text-white hover:text-white",
            "rounded-md transition-colors"
          )}
        >
          <X className="h-3 w-3" />
          <span className="sr-only">Exit training mode</span>
        </Button>
      </div>
    </div>
  );
}