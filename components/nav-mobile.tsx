"use client"

import React from "react"
import { type LucideIcon } from "lucide-react"
import Link from "next/link"
import { useStaticNavigation, isStaticMode } from "@/lib/utils/navigation"
import { cn } from "@/lib/utils"
import { SheetClose } from "@/components/ui/sheet"

export type NavItem = {
  title: string
  url: string
  icon?: LucideIcon
  isActive?: boolean
};

export function NavMobile({
  items,
}: {
  items: NavItem[];
}) {
  const { navigate } = useStaticNavigation()

  const handleNavClick = (href: string, e: React.MouseEvent) => {
    if (isStaticMode()) {
      e.preventDefault()
      const cleanPath = href.replace(/^\//, '')
      navigate(cleanPath)
    }
  }

  return (
    <nav className="flex flex-col gap-1 px-2">
      {items.map((item) => (
        <SheetClose key={item.title} asChild>
          <Link
            href={item.url}
            onClick={(e) => handleNavClick(item.url, e)}
            className={cn(
              "flex items-center gap-2 px-2 py-2 text-xs font-medium rounded-md transition-colors",
              item.isActive 
                ? 'bg-primary text-primary-foreground' 
                : 'text-foreground hover:bg-accent hover:text-accent-foreground'
            )}
          >
            {item.icon && <item.icon className="h-4 w-4 flex-shrink-0" />}
            <span className="truncate">{item.title}</span>
          </Link>
        </SheetClose>
      ))}
    </nav>
  );
}