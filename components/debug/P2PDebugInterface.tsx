'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Server,
  CheckCircle,
  XCircle,
  Search,
  Database,
  Network,
  Activity,
  AlertCircle,
  RefreshCw,
  WifiOff,
  Smartphone,
  Monitor,
  Wifi,
  Play,
  Pause
} from 'lucide-react';
import { isMobileApp, isElectronApp, getPlatformName } from '@/lib/utils/environment';
import { useSync } from '@/lib/hooks/useSync';
import { mainDbInstance } from '@/lib/db/v4/core/db-main-instance';

interface P2PDebugInterfaceProps {
  className?: string;
}

export function P2PDebugInterface({ className }: P2PDebugInterfaceProps) {
  const [platform, setPlatform] = useState('unknown');
  const [isMobile, setIsMobile] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);
  const [selectedTab, setSelectedTab] = useState('overview');

  // Database status
  const [dbStatus, setDbStatus] = useState<{
    isReady: boolean;
    restaurantId: string | null;
    hasDatabase: boolean;
    dbName: string | null;
  }>({ isReady: false, restaurantId: null, hasDatabase: false, dbName: null });

  // Use the working sync hook
  const sync = useSync({
    autoStart: true,
    config: {
      localDiscoveryInterval: 30000,
      localReconnectInterval: 60000,
      internetDiscoveryInterval: 120000,
      internetReconnectInterval: 180000,
      maxReconnectAttempts: 5,
      preferLocalSync: true,
      vpsBaseUrl: 'https://bistro.icu',
      authToken: '',
      deviceId: `debug-${Date.now()}`,
      deviceType: 'mobile'
    }
  });

  // Initialize platform detection
  useEffect(() => {
    const mobile = isMobileApp();
    const desktop = isElectronApp();
    setIsMobile(mobile);
    setIsDesktop(desktop);
    setPlatform(getPlatformName());
  }, []);

  // Check database status
  useEffect(() => {
    const checkDatabase = async () => {
      try {
        const isReady = mainDbInstance.isInitialized;
        const restaurantId = mainDbInstance.getCurrentRestaurantId();
        const db = mainDbInstance.getDatabase();
        const hasDatabase = Boolean(db);

        let dbName = null;
        if (restaurantId) {
          const { getRestaurantDbName } = await import('@/lib/db/db-utils');
          dbName = getRestaurantDbName(restaurantId);
        }

        setDbStatus({
          isReady,
          restaurantId,
          hasDatabase,
          dbName
        });
      } catch (error) {
        console.error('Database status check failed:', error);
        setDbStatus({
          isReady: false,
          restaurantId: null,
          hasDatabase: false,
          dbName: null
        });
      }
    };

    checkDatabase();
    const interval = setInterval(checkDatabase, 5000);
    return () => clearInterval(interval);
  }, []);


  const getStatusIcon = (isConnected: boolean, isSyncing: boolean, hasError: boolean) => {
    if (hasError) return <XCircle className="h-4 w-4 text-red-500" />;
    if (isSyncing) return <Activity className="h-4 w-4 text-blue-500 animate-pulse" />;
    if (isConnected) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <WifiOff className="h-4 w-4 text-gray-500" />;
  };

  const getStatusColor = (isConnected: boolean, isSyncing: boolean, hasError: boolean) => {
    if (hasError) return 'bg-red-100 text-red-800';
    if (isSyncing) return 'bg-blue-100 text-blue-800';
    if (isConnected) return 'bg-green-100 text-green-800';
    return 'bg-gray-100 text-gray-800';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">P2P Sync Debug Interface</h2>
          <p className="text-muted-foreground">
            Native PouchDB ↔ CouchDB synchronization
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {isMobile && <Smartphone className="h-5 w-5 text-blue-500" />}
          {isDesktop && <Monitor className="h-5 w-5 text-purple-500" />}
          <Badge variant="outline">{platform}</Badge>
        </div>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="autonomous">Autonomous</TabsTrigger>
          <TabsTrigger value="discovery">Discovery</TabsTrigger>
          <TabsTrigger value="sync">Sync Status</TabsTrigger>
          <TabsTrigger value="auth">Auth Debug</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          {/* Autonomous Sync Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Autonomous Sync Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className={`text-2xl font-bold ${sync.isRunning ? 'text-green-600' : 'text-gray-400'}`}>
                    {sync.isRunning ? 'ON' : 'OFF'}
                  </div>
                  <div className="text-sm text-gray-600">Sync Mode</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {sync.discoveredServers || 0}
                  </div>
                  <div className="text-sm text-gray-600">Discovered</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {sync.connected ? 1 : 0}
                  </div>
                  <div className="text-sm text-gray-600">Connected</div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-gray-600">Last Sync</div>
                  <div className="text-sm font-medium">
                    {sync.status?.lastSync
                      ? new Date(sync.status.lastSync).toLocaleTimeString()
                      : 'Never'
                    }
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Platform Info */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Platform</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  {isMobile ? <Smartphone className="h-4 w-4" /> : <Monitor className="h-4 w-4" />}
                  <span className="font-medium">{platform}</span>
                </div>
              </CardContent>
            </Card>

            {/* Database Status */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Database</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  {dbStatus.isReady ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <span className="font-medium">
                    {dbStatus.isReady ? 'Ready' : 'Not Ready'}
                  </span>
                </div>
                {dbStatus.dbName && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {dbStatus.dbName}
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Sync Status */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Sync Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  {getStatusIcon(sync.connected, sync.syncing, Boolean(sync.error))}
                  <span className="font-medium">
                    {sync.error ? 'Error' : sync.syncing ? 'Syncing' : sync.connected ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
                {sync.currentServer && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {sync.currentServer.url || sync.currentServer.ip}
                  </p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Sync Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {sync.status?.docsReceived || 0}
                  </div>
                  <div className="text-sm text-gray-600">Docs Received</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {sync.status?.docsSent || 0}
                  </div>
                  <div className="text-sm text-gray-600">Docs Sent</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {sync.servers?.length || 0}
                  </div>
                  <div className="text-sm text-gray-600">Servers Found</div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-gray-600">Last Sync</div>
                  <div className="text-sm font-medium">
                    {sync.status?.lastSync
                      ? new Date(sync.status.lastSync).toLocaleTimeString()
                      : 'Never'
                    }
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Error Display */}
          {sync.error && (
            <Card className="border-red-200">
              <CardHeader>
                <CardTitle className="text-red-600 flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  Sync Error
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-red-600 font-medium">{sync.error}</p>

                  {/* Enhanced error troubleshooting */}
                  <div className="bg-red-50 p-3 rounded-lg">
                    <h4 className="font-medium text-red-800 mb-2">🔧 Troubleshooting Steps:</h4>
                    <div className="text-xs text-red-700 space-y-1">
                      {sync.error.toLowerCase().includes('unauthorized') || sync.error.toLowerCase().includes('authentication') ? (
                        <>
                          <p>• <strong>Authentication Issue:</strong> CouchDB credentials are incorrect</p>
                          <p>• <strong>Expected:</strong> Desktop CouchDB should use admin:admin credentials</p>
                          <p>• <strong>Check:</strong> Desktop CouchDB configuration in electron/src/p2p-sync.ts</p>
                          <p>• <strong>Fix:</strong> Restart desktop app to regenerate CouchDB config</p>
                        </>
                      ) : sync.error.toLowerCase().includes('timeout') ? (
                        <>
                          <p>• <strong>Network Issue:</strong> Cannot reach CouchDB server</p>
                          <p>• <strong>Check:</strong> Desktop app is running and CouchDB is started</p>
                          <p>• <strong>Check:</strong> Both devices are on the same network</p>
                          <p>• <strong>Check:</strong> Firewall is not blocking port 5984</p>
                        </>
                      ) : sync.error.toLowerCase().includes('not found') ? (
                        <>
                          <p>• <strong>Database Issue:</strong> Restaurant database not found</p>
                          <p>• <strong>Check:</strong> Same restaurant ID on both devices</p>
                          <p>• <strong>Check:</strong> Database was created on desktop first</p>
                        </>
                      ) : (
                        <>
                          <p>• Check browser console for detailed error information</p>
                          <p>• Verify network connectivity between devices</p>
                          <p>• Ensure CouchDB authentication is properly configured</p>
                          <p>• Check database permissions and restaurant ID matching</p>
                        </>
                      )}
                    </div>
                  </div>

                  {/* Current sync configuration */}
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <h4 className="font-medium text-gray-800 mb-2">🔍 Current Configuration:</h4>
                    <div className="text-xs text-gray-600 space-y-1">
                      <p>• <strong>Platform:</strong> {platform}</p>
                      <p>• <strong>Restaurant ID:</strong> {dbStatus.restaurantId || 'Not set'}</p>
                      <p>• <strong>Database Name:</strong> {dbStatus.dbName || 'Not available'}</p>
                      <p>• <strong>Expected Auth:</strong> admin:admin</p>
                      {sync.currentServer && (
                        <p>• <strong>Target Server:</strong> {sync.currentServer.ip}:{sync.currentServer.port}</p>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Autonomous Tab */}
        <TabsContent value="autonomous" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Autonomous Sync System
              </CardTitle>
              <CardDescription>
                Fully automated background sync that runs continuously
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Status Overview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 border rounded-lg text-center">
                  <div className={`text-3xl font-bold mb-2 ${sync.isRunning ? 'text-green-600' : 'text-gray-400'}`}>
                    {sync.isRunning ? 'ACTIVE' : 'STOPPED'}
                  </div>
                  <div className="text-sm text-gray-600">System Status</div>
                </div>
                <div className="p-4 border rounded-lg text-center">
                  <div className="text-3xl font-bold mb-2 text-blue-600">
                    {sync.discoveredServers || 0}
                  </div>
                  <div className="text-sm text-gray-600">Servers Discovered</div>
                </div>
                <div className="p-4 border rounded-lg text-center">
                  <div className="text-3xl font-bold mb-2 text-green-600">
                    {sync.connected ? 1 : 0}
                  </div>
                  <div className="text-sm text-gray-600">Active Connections</div>
                </div>
              </div>

              {/* Control Panel */}
              <div className="flex gap-2">
                {sync.isRunning ? (
                  <Button onClick={sync.stop} variant="outline">
                    <Pause className="h-4 w-4 mr-2" />
                    Stop Sync
                  </Button>
                ) : (
                  <Button onClick={sync.start}>
                    <Play className="h-4 w-4 mr-2" />
                    Start Sync
                  </Button>
                )}
                <Button onClick={sync.discover} variant="outline">
                  <Search className="h-4 w-4 mr-2" />
                  Manual Discovery
                </Button>
              </div>

              {/* Sync Features */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-3">👑 Leader/Follower Features</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-blue-700">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>Leader-only connections</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>Split-brain prevention</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>Automatic leader election</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>LAN + Internet sync</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>Live continuous sync</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>Background operation</span>
                  </div>
                </div>
              </div>

              {/* Error Display */}
              {sync.error && (
                <div className="bg-red-50 p-4 rounded-lg">
                  <h4 className="font-medium text-red-800 mb-2">❌ Sync Error</h4>
                  <p className="text-red-700 text-sm">{sync.error}</p>
                </div>
              )}

              {/* Last Discovery */}
              {sync.status?.lastSync && (
                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="text-sm font-medium text-gray-800">Last Sync</div>
                  <div className="text-sm text-gray-600">
                    {new Date(sync.status.lastSync).toLocaleString()}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Discovery Tab */}
        <TabsContent value="discovery" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Server Discovery
              </CardTitle>
              <CardDescription>
                Scan for CouchDB servers on the local network
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Button
                  onClick={sync.discover}
                  disabled={sync.discovering}
                  className="flex items-center gap-2"
                >
                  {sync.discovering ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                  {sync.discovering ? 'Scanning...' : 'Scan Network'}
                </Button>
              </div>

              {/* Discovered Servers */}
              {sync.servers && sync.servers.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium">Discovered Servers ({sync.servers.length})</h4>
                  <div className="space-y-2">
                    {sync.servers.map((server, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Server className="h-4 w-4 text-blue-500" />
                          <div>
                            <div className="font-medium">{server.ip}:{server.port}</div>
                            <div className="text-sm text-gray-600">{server.url || `http://${server.ip}:${server.port}`}</div>
                            {server.isLeader && <Badge className="bg-yellow-100 text-yellow-800">Leader</Badge>}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {sync.currentServer?.ip === server.ip ? (
                            <Badge className="bg-green-100 text-green-800">Connected</Badge>
                          ) : (
                            <Button
                              size="sm"
                              onClick={() => sync.connect(server)}
                              disabled={sync.connected}
                            >
                              Connect
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              const testUrl = `http://${server.ip}:${server.port}`;
                              window.open(testUrl, '_blank');
                            }}
                            className="flex items-center gap-1"
                          >
                            <AlertCircle className="h-3 w-3" />
                            Test
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {(!sync.servers || sync.servers.length === 0) && !sync.discovering && (
                <div className="text-center py-8 text-gray-500">
                  <WifiOff className="h-8 w-8 mx-auto mb-2" />
                  <p>No servers discovered</p>
                  <p className="text-sm">Click "Scan Network" to search for CouchDB servers</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Sync Status Tab */}
        <TabsContent value="sync" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Sync Status
              </CardTitle>
              <CardDescription>
                Real-time synchronization status and controls
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Connection Status */}
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  {getStatusIcon(sync.connected, sync.syncing, Boolean(sync.error))}
                  <div>
                    <div className="font-medium">
                      {sync.error ? 'Error' : sync.syncing ? 'Syncing' : sync.connected ? 'Connected' : 'Disconnected'}
                    </div>
                    {sync.currentServer && (
                      <div className="text-sm text-gray-600">{sync.currentServer.url || `${sync.currentServer.ip}:${sync.currentServer.port}`}</div>
                    )}
                  </div>
                </div>
                <div className="flex gap-2">
                  {sync.connected && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={sync.disconnect}
                    >
                      Disconnect
                    </Button>
                  )}
                  <Badge className={getStatusColor(sync.connected, sync.syncing, Boolean(sync.error))}>
                    {sync.error ? 'Error' : sync.syncing ? 'Syncing' : sync.connected ? 'Connected' : 'Disconnected'}
                  </Badge>
                </div>
              </div>

              {/* Sync Statistics */}
              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-600">{sync.status?.docsReceived || 0}</div>
                  <div className="text-sm text-gray-600">Documents Received</div>
                </div>
                <div className="p-4 border rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-600">{sync.status?.docsSent || 0}</div>
                  <div className="text-sm text-gray-600">Documents Sent</div>
                </div>
              </div>

              {/* Last Sync Time */}
              {sync.status?.lastSync && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="text-sm font-medium text-blue-800">Last Sync</div>
                  <div className="text-sm text-blue-600">
                    {new Date(sync.status.lastSync).toLocaleString()}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Auth Debug Tab */}
        <TabsContent value="auth" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Authentication Debug
              </CardTitle>
              <CardDescription>
                CouchDB authentication and connection diagnostics
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Authentication Status */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-3">🔐 Authentication Configuration</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="font-medium text-blue-700">Expected Credentials:</p>
                    <p className="text-blue-600">Username: admin</p>
                    <p className="text-blue-600">Password: admin</p>
                  </div>
                  <div>
                    <p className="font-medium text-blue-700">Connection Format:</p>
                    <p className="text-blue-600 font-mono text-xs">*********************:PORT/DB</p>
                  </div>
                </div>
              </div>

              {/* Current Connection Details */}
              {sync.currentServer && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-800 mb-3">🔗 Current Connection</h4>
                  <div className="space-y-2 text-sm">
                    <p><strong>Server IP:</strong> {sync.currentServer.ip}</p>
                    <p><strong>Server Port:</strong> {sync.currentServer.port}</p>
                    <p><strong>Database:</strong> {dbStatus.dbName || 'Not available'}</p>
                    <p><strong>Full URL:</strong> <code className="bg-gray-200 px-1 rounded">http://admin:***@{sync.currentServer.ip}:{sync.currentServer.port}/{dbStatus.dbName || 'resto-{id}'}</code></p>
                  </div>
                </div>
              )}

              {/* Authentication Test */}
              <div className="bg-yellow-50 p-4 rounded-lg">
                <h4 className="font-medium text-yellow-800 mb-3">🧪 Authentication Test</h4>
                <div className="space-y-2 text-sm text-yellow-700">
                  <p>To test authentication manually:</p>
                  <ol className="list-decimal list-inside space-y-1 ml-2">
                    <li>Open browser on mobile device</li>
                    <li>Navigate to: <code className="bg-yellow-200 px-1 rounded">http://{sync.currentServer?.ip || 'DESKTOP_IP'}:{sync.currentServer?.port || '5984'}</code></li>
                    <li>Enter credentials: admin / admin</li>
                    <li>You should see CouchDB welcome page</li>
                  </ol>
                </div>
              </div>

              {/* Error Analysis */}
              {sync.error && (
                <div className="bg-red-50 p-4 rounded-lg">
                  <h4 className="font-medium text-red-800 mb-3">❌ Error Analysis</h4>
                  <div className="space-y-2 text-sm text-red-700">
                    <p><strong>Error:</strong> {sync.error}</p>
                    {sync.error.toLowerCase().includes('unauthorized') && (
                      <div className="bg-red-100 p-2 rounded">
                        <p className="font-medium">🔐 Authentication Failure Detected</p>
                        <p>This error indicates the CouchDB server is rejecting the admin:admin credentials.</p>
                        <p><strong>Solution:</strong> Restart the desktop app to regenerate CouchDB configuration.</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Database Tab */}
        <TabsContent value="database" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Database Status
              </CardTitle>
              <CardDescription>
                Local PouchDB database information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Status</span>
                    <Badge variant={dbStatus.isReady ? "default" : "destructive"}>
                      {dbStatus.isReady ? 'Ready' : 'Not Ready'}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Restaurant ID</span>
                    <span className="text-sm text-gray-600">
                      {dbStatus.restaurantId || 'Not set'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Database Name</span>
                    <span className="text-sm text-gray-600">
                      {dbStatus.dbName || 'Not available'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Has Database</span>
                    <Badge variant={dbStatus.hasDatabase ? "default" : "secondary"}>
                      {dbStatus.hasDatabase ? 'Yes' : 'No'}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}