"use client";

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Printer, TestTube, FileText, Receipt } from "lucide-react";
import { simpleHTMLPrintService } from '@/lib/services/simple-html-print';
import { useToast } from "@/components/ui/use-toast";

export function SimpleHTMLPrintTest() {
  const { toast } = useToast();
  const [isTesting, setIsTesting] = useState(false);

  const handleTestReceipt = async () => {
    setIsTesting(true);
    try {
      const result = await simpleHTMLPrintService.print({
        id: simpleHTMLPrintService.generateJobId(),
        title: 'Test Receipt',
        type: 'receipt',
        content: `
==================
TEST RESTAURANT
==================
Receipt #123
Table 5
${new Date().toLocaleString()}

1x Burger         $12.99
1x Fries          $4.99  
1x Soda           $2.99
------------------
Subtotal:        $20.97
Tax:              $2.10
------------------
TOTAL:           $23.07

Payment: Card
Thank you!
==================
        `
      });

      if (result.success) {
        toast({
          title: "Receipt Test Sent",
          description: "Print window should have opened",
        });
      } else {
        toast({
          title: "Test Failed", 
          description: result.error,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive", 
      });
    } finally {
      setIsTesting(false);
    }
  };

  const handleTestKitchen = async () => {
    setIsTesting(true);
    try {
      const result = await simpleHTMLPrintService.print({
        id: simpleHTMLPrintService.generateJobId(),
        title: 'Test Kitchen Order',
        type: 'kitchen',
        content: `
========================
KITCHEN ORDER #123
========================
Table: 5
Time: ${new Date().toLocaleString()}

[ MAINS ]
1x Burger (Medium)
   - No pickles
   - Extra cheese

1x Chicken Wings (12pc)
   - BBQ sauce

[ SIDES ]  
2x French Fries
1x Onion Rings

[ DRINKS ]
1x Coca Cola
1x Orange Juice

========================
TOTAL ITEMS: 5
========================
        `
      });

      if (result.success) {
        toast({
          title: "Kitchen Order Test Sent",
          description: "Print window should have opened",
        });
      } else {
        toast({
          title: "Test Failed",
          description: result.error,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
    } finally {
      setIsTesting(false);
    }
  };

  const handleGeneralTest = async () => {
    setIsTesting(true);
    try {
      const result = await simpleHTMLPrintService.testPrint();
      
      if (result.success) {
        toast({
          title: "General Test Sent",
          description: "Print window should have opened",
        });
      } else {
        toast({
          title: "Test Failed",
          description: result.error,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error", 
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
    } finally {
      setIsTesting(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Printer className="h-5 w-5" />
          Simple HTML Print Testing
        </CardTitle>
        <CardDescription>
          Test the dead simple HTML print system - just opens print dialogs that actually work!
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button
            onClick={handleGeneralTest}
            disabled={isTesting}
            variant="outline"
            className="h-20 flex flex-col items-center gap-2"
          >
            <TestTube className="h-6 w-6" />
            <span>General Test</span>
          </Button>

          <Button
            onClick={handleTestReceipt}
            disabled={isTesting}
            variant="outline"
            className="h-20 flex flex-col items-center gap-2"
          >
            <Receipt className="h-6 w-6" />
            <span>Receipt Test</span>
          </Button>

          <Button
            onClick={handleTestKitchen}
            disabled={isTesting}
            variant="outline"
            className="h-20 flex flex-col items-center gap-2"
          >
            <FileText className="h-6 w-6" />
            <span>Kitchen Test</span>
          </Button>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-semibold text-blue-900 mb-2">How it works:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Opens a new window with your print content</li>
            <li>• You see exactly what will print (WYSIWYG)</li>
            <li>• Click "Print" button to open your system's print dialog</li>
            <li>• Choose any printer - thermal, inkjet, laser, PDF - all work!</li>
            <li>• Real error feedback if something goes wrong</li>
          </ul>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="font-semibold text-green-900 mb-2">Why this works:</h4>
          <ul className="text-sm text-green-800 space-y-1">
            <li>✅ No silent failures</li>
            <li>✅ Works in Electron AND web browsers</li>
            <li>✅ Universal printer compatibility</li>
            <li>✅ Real user feedback</li>
            <li>✅ Simple, reliable, maintainable</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}