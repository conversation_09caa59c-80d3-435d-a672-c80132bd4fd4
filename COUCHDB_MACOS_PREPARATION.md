# 📋 CouchDB macOS Bundle Preparation Guide

This guide explains how to manually prepare CouchDB bundle files for macOS embedded deployment in the Electron application.

## 🎯 Overview

The embedded CouchDB service requires a complete CouchDB installation bundle to be placed in the `electron/resources/couchdb-macos/` directory. This bundle contains all necessary Erlang/OTP runtime, CouchDB libraries, and configuration files.

## 📂 Required Bundle Structure

Your `electron/resources/couchdb-macos/` directory should contain:

```
couchdb-macos/
├── bin/                    # CouchDB executables
│   ├── couchdb            # Main CouchDB script
│   ├── remsh              # Remote shell
│   └── weatherreport      # Diagnostic tool
├── erts-X.Y.Z/            # Erlang Runtime System (CRITICAL)
│   ├── bin/
│   │   ├── erl            # ⚠️ MAIN ERLANG EXECUTABLE
│   │   ├── erlexec
│   │   ├── beam.smp
│   │   └── ...
│   ├── lib/
│   └── include/
├── lib/                   # CouchDB & Erlang libraries
│   ├── couch-3.X.X/
│   ├── kernel-X.Y.Z/
│   ├── stdlib-X.Y.Z/
│   └── ... (many more)
├── etc/                   # Configuration files
│   ├── default.ini        # ⚠️ REQUIRED
│   ├── local.ini          # Optional
│   └── vm.args
├── releases/              # Erlang release files
└── share/                 # Documentation & web files
```

## 🔧 Method 1: Extract from Homebrew Installation

### Step 1: Install CouchDB via Homebrew
```bash
# Install CouchDB using Homebrew
brew install couchdb

# Find installation path
brew --prefix couchdb
# Usually: /opt/homebrew/lib/couchdb or /usr/local/lib/couchdb
```

### Step 2: Copy Installation Bundle
```bash
# Navigate to your electron resources directory
cd /path/to/your/project/electron/resources/

# Remove any existing bundle
rm -rf couchdb-macos

# Copy Homebrew CouchDB installation
cp -R $(brew --prefix couchdb)/lib/couchdb couchdb-macos

# Alternative paths if above doesn't work:
# cp -R /opt/homebrew/lib/couchdb couchdb-macos
# cp -R /usr/local/lib/couchdb couchdb-macos
```

### Step 3: Verify Bundle Structure
```bash
# Check for critical files
ls -la couchdb-macos/bin/couchdb          # Should exist
ls -la couchdb-macos/erts-*/bin/erl       # Should exist
ls -la couchdb-macos/etc/default.ini      # Should exist
ls -la couchdb-macos/lib/                 # Should contain many directories
```

## 🔧 Method 2: Download Official CouchDB Binary

### Step 1: Download CouchDB Binary
```bash
# Download the official CouchDB binary for macOS
# Visit: https://couchdb.apache.org/downloads.html
# Or use direct download (adjust version as needed):
curl -O https://archive.apache.org/dist/couchdb/binary/mac/3.5.0/apache-couchdb-3.5.0.tar.gz
```

### Step 2: Extract and Prepare
```bash
# Extract the downloaded tar.gz
tar -xzf apache-couchdb-3.5.0.tar.gz

# Copy to resources directory
cd /path/to/your/project/electron/resources/
cp -R /path/to/extracted/couchdb couchdb-macos
```

## 🔧 Method 3: Build from Source (Advanced)

### Prerequisites
```bash
# Install build dependencies
brew install erlang
brew install icu4c
brew install openssl
brew install node
```

### Build Process
```bash
# Clone CouchDB repository
git clone https://github.com/apache/couchdb.git
cd couchdb

# Configure build
./configure

# Build CouchDB
make release

# Copy built release
cd /path/to/your/project/electron/resources/
cp -R /path/to/couchdb/rel/couchdb couchdb-macos
```

## ✅ Bundle Validation

After preparing your bundle, validate it:

```bash
# Navigate to your bundle
cd electron/resources/couchdb-macos

# Check ERTS directory exists
ls -la | grep erts-
# Should show: erts-X.Y.Z/

# Check erl executable
ls -la erts-*/bin/erl
# Should show executable file

# Check required directories
ls -la bin/ lib/ etc/
# All should exist and contain files

# Test basic functionality (optional)
./bin/couchdb -h
# Should show CouchDB help message
```

## 📋 Common Issues & Solutions

### Issue: "ERTS directory not found"
**Solution:** Ensure the `erts-X.Y.Z` directory exists and contains the `bin/erl` executable.

### Issue: "Permission denied" errors
**Solution:** Set proper execute permissions:
```bash
chmod +x couchdb-macos/bin/couchdb
chmod +x couchdb-macos/erts-*/bin/erl
chmod +x couchdb-macos/erts-*/bin/*
```

### Issue: Missing libraries
**Solution:** Ensure the `lib/` directory contains all Erlang/CouchDB modules:
```bash
# Should contain 30+ directories like:
ls couchdb-macos/lib/
# couch-3.X.X/ kernel-X.Y.Z/ stdlib-X.Y.Z/ etc.
```

### Issue: Configuration files missing
**Solution:** Ensure `etc/default.ini` exists:
```bash
ls -la couchdb-macos/etc/default.ini
# Should be a readable file
```

## 🎯 Final Bundle Size

A complete CouchDB macOS bundle should be approximately:
- **Size:** 100-200 MB
- **Files:** 2000+ files
- **Directories:** 100+ directories

## 🔒 Security Considerations

1. **Bundle Integrity:** Verify all files are present and not corrupted
2. **Permissions:** Ensure execute permissions are set correctly
3. **Source Trust:** Only use bundles from trusted sources (Homebrew, official Apache)

## 🚀 Testing Your Bundle

Once prepared, test with the embedded service:

```bash
# Build and run Electron app
cd electron/
npm run build
npm run electron:start

# Check logs for successful CouchDB startup
# Look for: "✅ Embedded CouchDB server started successfully"
```

## 📝 Bundle Maintenance

- **Version Updates:** When updating CouchDB, replace entire bundle
- **Backup:** Keep a working bundle backup
- **Size Optimization:** Remove unnecessary files like docs (optional)

## 🏁 Success Checklist

✅ Bundle exists at `electron/resources/couchdb-macos/`  
✅ `erts-X.Y.Z/bin/erl` executable present  
✅ `bin/couchdb` script present  
✅ `etc/default.ini` configuration present  
✅ `lib/` directory with 30+ Erlang modules  
✅ Execute permissions set correctly  
✅ Total bundle size 100-200MB  
✅ Electron app starts CouchDB successfully  

Your embedded CouchDB is ready for production deployment! 🎉