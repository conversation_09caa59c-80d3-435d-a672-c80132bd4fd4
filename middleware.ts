import { NextRequest, NextResponse } from 'next/server';
import { jwtDecode } from 'jwt-decode';
import { enforceRestrictions, shouldRedirectToSubscription } from '@/lib/auth/restriction-middleware';

interface JWTPayload {
  sub: string;
  name: string;
  email?: string;
  role: string;
  restaurantId: string;
  permissions?: any;
  restricted?: boolean;
  exp: number;
}

// 🚨 CRITICAL: Skip middleware entirely for static/electron builds to allow static export
// Static builds handle auth client-side and reach out to remote server when needed
export function middleware(request: NextRequest) {
  // Skip middleware entirely for static/electron builds to allow offline operation
  // These builds handle restrictions client-side and through sync blocking
  if (process.env.BUILD_TARGET === 'electron' || process.env.BUILD_TARGET === 'static') {
    return NextResponse.next();
  }

  // For development mode, still allow some restriction checking on API routes
  const isDevelopment = process.env.NODE_ENV === 'development';
  if (isDevelopment && request.nextUrl.pathname.startsWith('/api/')) {
    // Check for restrictions on API routes even in development
    const restrictionResponse = enforceRestrictions(request);
    if (restrictionResponse) {
      return restrictionResponse;
    }
  }

  // Handle CORS for API routes
  if (request.nextUrl.pathname.startsWith('/api/')) {
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return new NextResponse(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
          'Access-Control-Allow-Credentials': 'false',
          'Access-Control-Max-Age': '86400',
        },
      });
    }

    // Check for restrictions on API routes
    const restrictionResponse = enforceRestrictions(request);
    if (restrictionResponse) {
      return restrictionResponse;
    }

    // Add CORS headers to all API responses
    const response = NextResponse.next();
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    response.headers.set('Access-Control-Allow-Credentials', 'false');
    return response;
  }
  
  // Get path and check if it's a protected route
  const path = request.nextUrl.pathname;

  // Define public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/auth',
    '/landing',
    '/api/auth/login',
    '/api/auth/register',
    '/api/auth/refresh',
    '/api/auth/demo',
    '/api/health',
    '/api/updates/check',
    '/api/updates/download',
    '/api/releases/latest'
  ];

  // Get token from Authorization header or cookies first
  const authHeader = request.headers.get('authorization');
  const token = authHeader?.replace('Bearer ', '') || 
                request.cookies.get('auth_token')?.value ||
                request.cookies.get('auth-token')?.value ||
                request.cookies.get('token')?.value;

  // Determine if this is a web build early to use in logic below
  const isWebBuild = process.env.BUILD_TARGET === 'web' || 
                     process.env.NEXT_PUBLIC_BUILD_TARGET === 'web' ||
                     (!process.env.BUILD_TARGET && !process.env.NEXT_PUBLIC_BUILD_TARGET && process.env.NODE_ENV === 'production');
  
  // For web builds, handle mobile vs desktop differently
  if (isWebBuild) {
    // In development, allow desktop access for testing
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    // Check if user is on mobile device or tablet
    const userAgent = request.headers.get('user-agent') || '';
    const isMobile = /mobile|android|iphone|ipad|tablet|phone|kindle|silk|blackberry|opera mini|windows phone/i.test(userAgent);
    
    const webAllowedRoutes = [
      '/',
      '/landing',
      '/auth',
      '/api/auth/login',
      '/api/auth/register',
      '/api/health'
    ];
    
    // For mobile users, also allow protected routes if they have a valid token
    if (isMobile && token) {
      try {
        const decoded = jwtDecode<JWTPayload>(token);
        const currentTime = Math.floor(Date.now() / 1000);
        
        if (decoded.exp && decoded.exp >= currentTime) {
          // Mobile user with valid token can access protected routes
          console.log('📱 [Middleware] Allowing mobile user access to protected route:', path);
          // Allow the request to continue - skip desktop route checking
          return NextResponse.next();
        } else {
          // Token expired, redirect to auth
          const authUrl = new URL('/auth', request.url);
          authUrl.searchParams.set('redirect', path);
          return NextResponse.redirect(authUrl);
        }
      } catch (error) {
        // Invalid token, redirect to auth
        const authUrl = new URL('/auth', request.url);
        authUrl.searchParams.set('redirect', path);
        return NextResponse.redirect(authUrl);
      }
    } else {
      // Desktop users or unauthenticated mobile users - check allowed routes  
      const isWebAllowed = webAllowedRoutes.some(route => path === route || path.startsWith(route + '/'));
      
      if (!isWebAllowed && !path.startsWith('/_next') && !path.startsWith('/public') && !path.match(/\.(ico|png|jpg|jpeg|svg|css|js|woff|woff2|ttf|otf)$/)) {
        // In development, allow desktop access to all routes for testing
        if (isDevelopment && !isMobile) {
          console.log('🔧 [Dev] Allowing desktop access to protected route:', path);
          // Allow desktop access in development
        } else if (isMobile) {
          // For mobile users without cookie token, allow initial page load
          // Client-side auth will handle redirect if needed
          console.log('📱 [Middleware] Allowing mobile initial page load for client-side auth check:', path);
          return NextResponse.next();
        } else {
          // For web builds in production, redirect to landing page instead of protected routes
          return NextResponse.redirect(new URL('/landing', request.url));
        }
      }
    }
  }

  // API routes that don't require authentication
  const authApiRoutes = [
    '/api/auth/login',
    '/api/auth/register',
    '/api/auth/refresh',
    '/api/auth/demo',
    '/api/health'
  ];

  // Token was already retrieved above

  // Check if the path is a public route
  if (publicRoutes.includes(path) || path.startsWith('/_next') || path.startsWith('/public')) {
    return NextResponse.next();
  }

  // Public files like images, etc.
  if (path.match(/\.(ico|png|jpg|jpeg|svg|css|js|woff|woff2|ttf|otf)$/)) {
    return NextResponse.next();
  }

  // If no token, check if this is a static app that might have sessions in localStorage
  if (!token) {
    // Allow API auth routes without token
    if (authApiRoutes.includes(path)) {
      return NextResponse.next();
    }

    // If this is an API route, return 401 error (but allow static apps to handle offline)
    if (path.startsWith('/api/')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // For static/electron/mobile apps, allow initial page load without token
    // The client-side auth will handle session restoration and redirect if needed
    const isStaticApp = request.headers.get('user-agent')?.includes('Electron') || 
                       request.headers.get('user-agent')?.includes('CapacitoriOS') ||
                       request.headers.get('user-agent')?.includes('CapacitorAndroid') ||
                       request.headers.get('x-app-type') === 'static' ||
                       (process.env.NODE_ENV === 'development' && !isWebBuild);
    
    if (isStaticApp && !path.startsWith('/api/')) {
      console.log('🖥️ [Middleware] Allowing static/mobile app to load without token for client-side auth check');
      return NextResponse.next();
    }

    // For web apps, redirect to login
    const loginUrl = new URL('/auth', request.url);
    loginUrl.searchParams.set('redirect', path);
    return NextResponse.redirect(loginUrl);
  }

  // If we have a token, verify it's valid
  if (token) {
    try {
      const decoded = jwtDecode<JWTPayload>(token);
      
      // Check if token is expired
      const currentTime = Math.floor(Date.now() / 1000);
      if (decoded.exp && decoded.exp < currentTime) {
        throw new Error('Token expired');
      }
      
      // Check for restriction redirect (for non-API routes)
      const redirectResponse = shouldRedirectToSubscription(request);
      if (redirectResponse) {
        return redirectResponse;
      }
      
      // Token is valid, allow the request
      return NextResponse.next();
    } catch (error) {
      console.error('Token verification failed:', error);
      // Invalid token, continue to redirect to auth
    }
  }

  // Invalid token, redirect to auth
  const authUrl = new URL('/auth', request.url);
  authUrl.searchParams.set('redirect', path);
  return NextResponse.redirect(authUrl);
}

export const config = {
  // Only run middleware for specific paths, exclude static assets and API routes that don't need auth
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * Skip middleware entirely for static builds
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};