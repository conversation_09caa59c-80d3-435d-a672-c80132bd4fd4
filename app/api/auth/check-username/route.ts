import { NextRequest, NextResponse } from "next/server";
import { verifyJwtAuth } from "@/lib/api-middleware";
import { getMongoUserByUsername } from '@/lib/auth/mongo-auth-ops';

// Static export configuration
export { dynamic, revalidate } from '@/lib/api-config';


export async function GET(request: NextRequest) {
  console.log('[API] MONGODB_URI (masked):', process.env.MONGODB_URI ? process.env.MONGODB_URI.replace(/\/\/.*:.*@/, '//USER:PASSWORD@') : 'NOT SET');
  try {
    // Verify the user is authenticated
    const authResult = await verifyJwtAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    // Get the username from the query params
    const url = new URL(request.url);
    const username = url.searchParams.get('username');
    
    if (!username) {
      return NextResponse.json({ error: "Username parameter is required" }, { status: 400 });
    }

    // Query MongoDB to see if the username already exists
    const user = await getMongoUserByUsername(username);
    const available = !user;

    return NextResponse.json({ 
      available,
      message: available ? 
        "Username is available" : 
        "Username is already taken"
    });
  } catch (error) {
    console.error("Error checking username availability:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An unknown error occurred" },
      { status: 500 }
    );
  }
} 