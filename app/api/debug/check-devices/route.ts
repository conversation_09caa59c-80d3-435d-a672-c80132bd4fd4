import { NextRequest, NextResponse } from 'next/server';
import clientPromise from '@/lib/mongodb';

// Dynamic route - needs database access
export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  try {
    // Connect to MongoDB
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    const devicesCollection = db.collection('device_registry');

    // Get all devices
    const allDevices = await devicesCollection.find({}).toArray();

    // Get collection count
    const count = await devicesCollection.countDocuments();

    return NextResponse.json({
      success: true,
      totalDevices: allDevices.length,
      documentCount: count,
      devices: allDevices,
      query: 'db.device_registry.find({})'
    });
  } catch (error) {
    console.error('[Debug Check Devices] Error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Unknown error',
      success: false
    }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { action } = body;

    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    const devicesCollection = db.collection('device_registry');

    if (action === 'clear') {
      // Clear all devices for testing
      const result = await devicesCollection.deleteMany({});
      return NextResponse.json({
        success: true,
        message: `Cleared ${result.deletedCount} devices`,
        deletedCount: result.deletedCount
      });
    }

    if (action === 'create_test') {
      // Create test device
      const testDevice = {
        deviceId: `test-device-${Date.now()}`,
        deviceType: 'desktop',
        restaurantId: 'restaurant:test',
        publicIP: '************',
        couchdbPort: 5984,
        status: 'active',
        registeredAt: new Date(),
        lastHeartbeat: new Date(),
        capabilities: ['couchdb_server', 'pos_system', 'internet_sync'],
        metadata: {
          userAgent: 'Debug Test',
          registrationSource: 'api',
          detectedFrom: 'debug'
        }
      };

      await devicesCollection.insertOne(testDevice);
      return NextResponse.json({
        success: true,
        message: 'Test device created',
        device: testDevice
      });
    }

    return NextResponse.json({ error: 'Unknown action' }, { status: 400 });
  } catch (error) {
    console.error('[Debug Check Devices] POST Error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Unknown error',
      success: false
    }, { status: 500 });
  }
}