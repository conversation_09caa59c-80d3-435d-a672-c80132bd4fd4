import { NextRequest, NextResponse } from "next/server";
import { verifyJwtAuth } from "@/lib/api-middleware";
import { serverGetStaff } from "@/lib/db/server-db";
import { cleanRestaurantId } from "@/lib/db/db-utils";

// Static export configuration
export { dynamic, revalidate } from '@/lib/api-config';


/**
 * PouchDB operations for staff permissions
 */
class StaffPermissionsService {
  constructor() {
    // No configuration needed for PouchDB
  }

  /**
   * Get a staff member by ID
   */
  async getStaff(staffId: string, restaurantId: string) {
    try {
      console.log(`StaffPermissionsService: Getting staff with ID ${staffId} from restaurant ${restaurantId}`);
      
      // Get all staff members from PouchDB
      const staffDoc = await serverGetStaff(restaurantId);
      
      if (staffDoc && staffDoc.members && Array.isArray(staffDoc.members)) {
        // Find the staff member by ID
        const staffMember = staffDoc.members.find((m: any) => m.id === staffId);

        if (staffMember) {
          console.log(`StaffPermissionsService: Found staff member ${staffId} in staff collection`);
          return staffMember;
        }
      }

      console.error(`StaffPermissionsService: Staff member ${staffId} not found in staff collection`);
      return null;
    } catch (error) {
      console.error(`StaffPermissionsService: Error getting staff ${staffId}:`, error);
      throw error;
    }
  }
}

// Create an instance of the service
const staffPermissionsService = new StaffPermissionsService();

/**
 * GET handler for staff permissions
 * Requires proper authentication and authorization
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { staffId: string } }
) {
  console.log(`API: /api/staff/permissions/${params.staffId} - GET request received`);

  try {
    // 1. Verify JWT authentication
    const authResult = await verifyJwtAuth(request);

    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: authResult.error || "Unauthorized" },
        { status: 401 }
      );
    }

    // 2. Check user permissions for staff management
    const { user } = authResult;
    
    // Only admin, manager, or owner can access staff permissions
    if (!user.permissions?.staff?.canView && !['admin', 'manager', 'owner'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view staff permissions' },
        { status: 403 }
      );
    }
    
    // 3. Extract the staff ID from the URL params
    const { staffId } = params;

    if (!staffId) {
      return NextResponse.json(
        { error: "Staff ID is required" },
        { status: 400 }
      );
    }

    // 4. Get the restaurant ID from the authenticated user
    const restaurantId = user.restaurantId;
    if (!restaurantId) {
      return NextResponse.json(
        { error: "Missing restaurant ID in user profile" },
        { status: 400 }
      );
    }

    // 5. Clean the restaurant ID
    const cleanedRestaurantId = cleanRestaurantId(restaurantId);

    try {
      // 6. Get the staff member
      const staffMember = await staffPermissionsService.getStaff(staffId, cleanedRestaurantId);

      if (!staffMember) {
        return NextResponse.json(
          { error: `Staff member with ID ${staffId} not found` },
          { status: 404 }
        );
      }

      // 7. Return the staff permissions
      return NextResponse.json({
        success: true,
        staffId: staffMember.id,
        name: staffMember.name,
        permissions: staffMember.permissions || { pages: {} }
      });
    } catch (error) {
      console.error(`Error getting staff permissions for ${staffId}:`, error);
      return NextResponse.json(
        {
          error: "Error getting staff permissions",
          details: error instanceof Error ? error.message : String(error)
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error in staff permissions API handler:", error);
    return NextResponse.json(
      {
        error: "Server error",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}