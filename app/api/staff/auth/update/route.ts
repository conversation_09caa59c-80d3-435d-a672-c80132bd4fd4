import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { updateMongoUserCredentials } from "@/lib/auth/mongo-auth-ops";

// Static export configuration
export { dynamic, revalidate } from '@/lib/api-config';


// Validation schema for updating staff auth
const updateAuthSchema = z.object({
  staffId: z.string().min(1, "Staff ID is required").uuid("Staff ID must be a valid UUID"),
  username: z.string()
    .min(3, "Username must be at least 3 characters")
    .max(50, "Username must be at most 50 characters")
    .regex(/^[a-zA-Z0-9_]+$/, "Username can only contain letters, numbers, and underscores"),
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .max(100, "Password must be at most 100 characters")
    .optional(),
});

/**
 * Updates authentication for a staff member
 *
 * This endpoint updates a MongoDB auth user linked to an existing staff member.
 */
export async function PUT(request: NextRequest) {
  try {
    console.log("API: /api/staff/auth/update - Request received");

    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        { error: "Invalid request body" },
        { status: 400 }
      );
    }

    // Log the request body with sensitive data redacted
    console.log("API: Request body:", {
      ...body,
      password: body.password ? '[REDACTED]' : undefined
    });

    // Validate the request body
    console.log("API: Validating request body");
    const validation = updateAuthSchema.safeParse(body);
    if (!validation.success) {
      console.error("API: Validation failed:", validation.error.formErrors.fieldErrors);
      return NextResponse.json(
        {
          error: "Validation failed",
          details: validation.error.formErrors.fieldErrors
        },
        { status: 400 }
      );
    }
    console.log("API: Validation successful");

    // Extract validated data
    const { staffId, username, password } = validation.data;

    console.log(`API: Updating MongoDB user for staff ${staffId} with username ${username}`);

    // Check MongoDB connection before attempting to update user
    try {
      // Import the isMongoDBReachable function
      const { isMongoDBReachable } = await import('@/lib/mongodb');
      const mongoStatus = await isMongoDBReachable();

      if (!mongoStatus.reachable) {
        console.error(`API: MongoDB is not reachable: ${mongoStatus.error}`);
        return NextResponse.json(
          {
            error: "Database connection error",
            details: "The authentication database is currently unavailable. Please try again later.",
            offlineError: true
          },
          { status: 503 } // Service Unavailable
        );
      }

      // Update the MongoDB user credentials
      console.log(`API: Updating MongoDB user credentials for staff ${staffId}`);
      const updateData: { username: string; plaintextPassword?: string } = { username };
      
      if (password) {
        updateData.plaintextPassword = password;
      }

      const mongoUserResult = await updateMongoUserCredentials(staffId, updateData);

      if (!mongoUserResult.success) {
        console.error("API: MongoDB user update failed:", mongoUserResult.error);
        return NextResponse.json(
          {
            error: mongoUserResult.error || "Failed to update auth user",
            details: "The authentication database could not update the user. Please check the provided information and try again."
          },
          { status: 500 }
        );
      }

      console.log(`API: MongoDB user updated successfully for staff ${staffId}`);
      return NextResponse.json({
        success: true,
        message: "Auth user updated successfully"
      });

    } catch (dbError) {
      console.error("API: Error checking MongoDB or updating user:", dbError);
      return NextResponse.json(
        {
          error: "Database operation failed",
          details: "There was an error connecting to the authentication database.",
          offlineError: true
        },
        { status: 503 } // Service Unavailable
      );
    }
  } catch (error) {
    console.error("API: Error updating staff auth:", error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Internal server error"
      },
      { status: 500 }
    );
  }
} 