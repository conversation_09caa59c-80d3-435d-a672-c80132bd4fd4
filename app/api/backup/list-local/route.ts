import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    if (typeof window !== 'undefined') {
      return NextResponse.json({ error: 'This endpoint is only available on desktop' }, { status: 400 });
    }

    // Import backup service only on server side
    const { BackupService } = await import('@/lib/services/backup-service');
    const backupService = new BackupService();

    const backups = await backupService.listLocalBackups();

    return NextResponse.json({
      success: true,
      backups
    });

  } catch (error) {
    console.error('Failed to list local backups:', error);
    return NextResponse.json(
      { error: 'Failed to list local backups' },
      { status: 500 }
    );
  }
}