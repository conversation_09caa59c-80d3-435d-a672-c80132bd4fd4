import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    if (typeof window !== 'undefined') {
      return NextResponse.json({ error: 'This endpoint is only available on desktop' }, { status: 400 });
    }

    const { restaurantId } = await request.json();

    if (!restaurantId) {
      return NextResponse.json({ error: 'Restaurant ID is required' }, { status: 400 });
    }

    // Import backup service only on server side
    const { BackupService } = await import('@/lib/services/backup-service');
    const backupService = new BackupService();

    const result = await backupService.createBackup(restaurantId);

    if (result.success) {
      return NextResponse.json({
        success: true,
        metadata: result.metadata
      });
    } else {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Failed to create backup:', error);
    return NextResponse.json(
      { error: 'Failed to create backup' },
      { status: 500 }
    );
  }
}