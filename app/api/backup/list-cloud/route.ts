import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    if (typeof window !== 'undefined') {
      return NextResponse.json({ error: 'This endpoint is only available on desktop' }, { status: 400 });
    }

    // For now, return empty array since Google Drive connection needs to be implemented
    return NextResponse.json({
      success: true,
      files: []
    });

  } catch (error) {
    console.error('Failed to list cloud backups:', error);
    return NextResponse.json(
      { error: 'Failed to list cloud backups' },
      { status: 500 }
    );
  }
}