import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    if (typeof window !== 'undefined') {
      return NextResponse.json({ error: 'This endpoint is only available on desktop' }, { status: 400 });
    }

    // Import Electron modules only on server side
    const { ipcMain, app } = await import('electron');
    
    // Get backup scheduler status via IPC
    const status = await new Promise((resolve) => {
      ipcMain.handle('backup:get-status', async () => {
        try {
          const { BackupScheduler } = await import('@/lib/services/backup-scheduler');
          // In a real implementation, you'd get the actual scheduler instance
          // For now, return mock data
          return {
            isRunning: true,
            lastBackup: Date.now() - (2 * 60 * 60 * 1000), // 2 hours ago
            googleDriveConnected: false
          };
        } catch (error) {
          console.error('Failed to get backup status:', error);
          return {
            isRunning: false,
            googleDriveConnected: false
          };
        }
      });
      
      // Trigger the handler
      resolve({
        isRunning: true,
        lastBackup: Date.now() - (2 * 60 * 60 * 1000),
        googleDriveConnected: false
      });
    });

    return NextResponse.json(status);

  } catch (error) {
    console.error('Failed to get backup status:', error);
    return NextResponse.json(
      { error: 'Failed to get backup status' },
      { status: 500 }
    );
  }
}