import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    if (typeof window !== 'undefined') {
      return NextResponse.json({ error: 'This endpoint is only available on desktop' }, { status: 400 });
    }

    const { enabled } = await request.json();

    // For now, just return success
    // In a full implementation, this would update the backup scheduler configuration
    console.log(`Auto cloud backup ${enabled ? 'enabled' : 'disabled'}`);

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Failed to toggle cloud backup:', error);
    return NextResponse.json(
      { error: 'Failed to toggle cloud backup setting' },
      { status: 500 }
    );
  }
}