import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    if (typeof window !== 'undefined') {
      return NextResponse.json({ error: 'This endpoint is only available on desktop' }, { status: 400 });
    }

    const { restaurantId, timestamp } = await request.json();

    if (!restaurantId || !timestamp) {
      return NextResponse.json({ error: 'Restaurant ID and timestamp are required' }, { status: 400 });
    }

    // Import backup service only on server side
    const { BackupService } = await import('@/lib/services/backup-service');
    const backupService = new BackupService();

    const result = await backupService.restoreFromBackup(timestamp, restaurantId);

    if (result.success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Failed to restore backup:', error);
    return NextResponse.json(
      { error: 'Failed to restore backup' },
      { status: 500 }
    );
  }
}