import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth/new-auth-service';
import clientPromise from '@/lib/mongodb';

// Dynamic route - needs database access
export const dynamic = 'force-dynamic';


export async function POST(req: NextRequest) {
  try {
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization token required' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const decoded = verifyToken(token);
    
    if (!decoded || !decoded.restaurantId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const body = await req.json();
    const { deviceId } = body;

    if (!deviceId) {
      return NextResponse.json({ error: 'deviceId is required' }, { status: 400 });
    }

    // Connect to MongoDB for device registry (VPS orchestrator database)
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    const devicesCollection = db.collection('device_registry');
    
    try {
      // Find the device in MongoDB
      const existing = await devicesCollection.findOne({
        deviceId,
        restaurantId: decoded.restaurantId
      });
      
      if (!existing) {
        return NextResponse.json({ 
          error: 'Device not found or not registered' 
        }, { status: 404 });
      }

      // Update heartbeat timestamp and ensure active status
      await devicesCollection.updateOne(
        { deviceId, restaurantId: decoded.restaurantId },
        { 
          $set: {
            lastHeartbeat: new Date(),
            status: 'active' // Refresh status on heartbeat
          }
        }
      );
    } catch (error) {
      console.error('Heartbeat update failed:', error);
      return NextResponse.json({ 
        error: 'Failed to update heartbeat' 
      }, { status: 500 });
    }

    return NextResponse.json({
      message: 'Heartbeat received',
      deviceId,
      timestamp: new Date()
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });

  } catch (error) {
    console.error('[API Sync Heartbeat] Error:', error);
    return NextResponse.json({ 
      error: 'Heartbeat failed' 
    }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}