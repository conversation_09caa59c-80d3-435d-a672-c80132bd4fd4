import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth/new-auth-service';
import clientPromise from '@/lib/mongodb';

// Dynamic route - needs database access
export const dynamic = 'force-dynamic';

/**
 * 👑 LEADER REGISTRATION ENDPOINT
 * 
 * This endpoint allows a device to register as the official leader for their restaurant.
 * Only one leader can exist per restaurant - this enforces single-leader authority.
 */
export async function POST(req: NextRequest) {
  try {
    // 🔐 Authentication check
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization token required' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const decoded = verifyToken(token);
    
    if (!decoded || !decoded.restaurantId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // 📋 Parse request body
    const body = await req.json();
    const { deviceId, restaurantId, leaderEpoch, couchdbPort } = body;

    if (!deviceId || !restaurantId || !leaderEpoch) {
      return NextResponse.json({ 
        error: 'deviceId, restaurantId, and leaderEpoch are required' 
      }, { status: 400 });
    }

    if (restaurantId !== decoded.restaurantId) {
      return NextResponse.json({ 
        error: 'Restaurant ID mismatch' 
      }, { status: 403 });
    }

    // 🌐 AUTO-DETECT PUBLIC IP
    let clientIP = req.headers.get('x-forwarded-for')?.split(',')[0]?.trim() || 
                   req.headers.get('x-real-ip') || 
                   req.headers.get('cf-connecting-ip') || 
                   'unknown';

    // Development environment handling
    if (clientIP === 'unknown' || clientIP === '::1' || clientIP === '127.0.0.1') {
      if (process.env.NODE_ENV === 'development' || req.headers.get('host')?.includes('localhost')) {
        clientIP = '************'; // Simulate public IP for development
        console.log('🧪 [DEBUG] Development mode - using simulated public IP:', clientIP);
      }
    }

    // Connect to MongoDB
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    const devicesCollection = db.collection('device_registry');

    const now = new Date();

    // 👑 CRITICAL: Atomic leader election
    // This prevents race conditions where two devices try to become leader simultaneously
    try {
      // Step 1: Check if there's already a leader for this restaurant
      const existingLeader = await devicesCollection.findOne({
        restaurantId: decoded.restaurantId,
        isLeader: true
      });

      if (existingLeader && existingLeader.deviceId !== deviceId) {
        // Check if existing leader has higher epoch (more recent election)
        if (existingLeader.leaderEpoch >= leaderEpoch) {
          return NextResponse.json({
            error: 'Another device is already the leader with equal or higher epoch',
            currentLeader: {
              deviceId: existingLeader.deviceId,
              leaderEpoch: existingLeader.leaderEpoch,
              electedAt: existingLeader.electedAt
            }
          }, { status: 409 }); // Conflict
        }

        // Our epoch is higher - we can take leadership
        console.log(`👑 [LeaderElection] ${deviceId} taking leadership from ${existingLeader.deviceId} (epoch ${leaderEpoch} > ${existingLeader.leaderEpoch})`);
      }

      // Step 2: Atomic operation - demote all others and promote this device
      const session = mongoClient.startSession();
      
      try {
        await session.withTransaction(async () => {
          // Demote any existing leaders in this restaurant
          await devicesCollection.updateMany(
            { 
              restaurantId: decoded.restaurantId,
              isLeader: true,
              deviceId: { $ne: deviceId } 
            },
            { 
              $set: { 
                isLeader: false, 
                demotedAt: now,
                demotedReason: `New leader elected: ${deviceId}` 
              } 
            },
            { session }
          );

          // Register/update this device as the leader
          const leaderRecord = {
            deviceId,
            deviceType: 'desktop', // Leaders are always desktop devices
            restaurantId: decoded.restaurantId,
            publicIP: clientIP,
            couchdbPort: couchdbPort || 5984,
            status: 'active',
            isLeader: true,
            leaderEpoch,
            electedAt: now,
            registeredAt: now,
            lastHeartbeat: now,
            capabilities: ['couchdb_server', 'pos_system', 'internet_sync', 'leader'],
            metadata: {
              userAgent: req.headers.get('user-agent') || 'unknown',
              registrationSource: 'leader_election',
              detectedFrom: req.headers.get('x-forwarded-for') ? 'proxy' : 'direct'
            }
          };

          await devicesCollection.replaceOne(
            { deviceId, restaurantId: decoded.restaurantId },
            leaderRecord,
            { upsert: true, session }
          );
        });
      } finally {
        await session.endSession();
      }

      console.log(`✅ [LeaderElection] ${deviceId} is now the leader for restaurant ${restaurantId} (epoch: ${leaderEpoch})`);

      return NextResponse.json({
        message: 'Device registered as leader successfully',
        deviceId,
        restaurantId: decoded.restaurantId,
        leaderEpoch,
        electedAt: now
      }, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      });

    } catch (error) {
      console.error('👑 [LeaderElection] Failed:', error);
      return NextResponse.json({ 
        error: 'Leader election failed - database error' 
      }, { status: 500 });
    }

  } catch (error) {
    console.error('[API Sync Register Leader] Error:', error);
    return NextResponse.json({ 
      error: 'Leader registration failed' 
    }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}