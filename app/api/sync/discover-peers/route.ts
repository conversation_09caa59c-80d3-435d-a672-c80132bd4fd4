import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth/new-auth-service';
import clientPromise from '@/lib/mongodb';

// Dynamic route - needs database access
export const dynamic = 'force-dynamic';


// Simple rate limiting (in production, use Redis or external service)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const RATE_LIMIT_MAX = 30; // 30 requests per minute

function checkRateLimit(identifier: string): boolean {
  const now = Date.now();
  const current = rateLimitMap.get(identifier);
  
  if (!current || now > current.resetTime) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }
  
  if (current.count >= RATE_LIMIT_MAX) {
    return false;
  }
  
  current.count++;
  return true;
}

export async function GET(req: NextRequest) {
  try {
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization token required' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const decoded = verifyToken(token);
    
    if (!decoded || !decoded.restaurantId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Rate limiting per restaurant
    if (!checkRateLimit(decoded.restaurantId)) {
      return NextResponse.json({ 
        error: 'Rate limit exceeded - too many discovery requests' 
      }, { status: 429 });
    }

    // Connect to MongoDB for device registry (VPS orchestrator database)
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    const devicesCollection = db.collection('device_registry');

    // Increase timeout to 15 minutes to handle network hiccups better
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);

    try {
      // 👑 CRITICAL: Find the current leader for this restaurant
      const leader = await devicesCollection.findOne({
        restaurantId: decoded.restaurantId,
        isLeader: true,
        deviceType: 'desktop',
        status: 'active',
        lastHeartbeat: { $gte: fifteenMinutesAgo },
        publicIP: { $exists: true }
      });

      if (!leader) {
        console.warn(`⚠️ [PeerDiscovery] No active leader found for restaurant ${decoded.restaurantId}`);
        return NextResponse.json({
          error: 'No leader available',
          message: 'Restaurant has no active leader device. A desktop device must be elected as leader first.',
          restaurantId: decoded.restaurantId,
          leader: null,
          peers: []
        }, {
          status: 404,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          }
        });
      }

      // 👑 FORMAT LEADER INFO - Only return the leader, not all devices
      const formattedLeader = {
        id: leader.deviceId,
        deviceType: 'desktop',
        ipAddress: leader.publicIP,
        couchdbPort: leader.couchdbPort || 5984,
        hostname: `leader-${leader.deviceId?.substring(0, 8) || 'unknown'}`,
        platform: 'desktop',
        lastSeen: leader.lastHeartbeat || leader.registeredAt,
        registeredAt: leader.registeredAt,
        isLeader: true,
        leaderEpoch: leader.leaderEpoch,
        electedAt: leader.electedAt
      };

      console.log(`✅ [PeerDiscovery] Returning leader info for restaurant ${decoded.restaurantId}: ${leader.deviceId}`);

      return NextResponse.json({
        leader: formattedLeader,
        peers: [], // No other peers - only leader matters
        restaurantId: decoded.restaurantId,
        count: 1
      }, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      });

    } catch (error) {
      console.error('[API Sync Discover Peers] Error:', error);
      return NextResponse.json({ 
        error: 'Peer discovery failed' 
      }, { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      });
    }
  } catch (error) {
    console.error('[API Sync Discover Peers] Outer error:', error);
    return NextResponse.json({ 
      error: 'Server error' 
    }, { status: 500 });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}