import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth/new-auth-service';
import clientPromise from '@/lib/mongodb';
import { validateSyncOperation } from '@/lib/sync/restaurant-validation';

// Dynamic route - needs database access
export const dynamic = 'force-dynamic';


interface RouteParams {
  params: {
    deviceId: string;
    path: string[];
  };
}

async function getTargetDevice(deviceId: string, restaurantId: string) {
  try {
    // Connect to MongoDB for device registry (VPS orchestrator database)
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    const devicesCollection = db.collection('device_registry');
    
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
    
    // 👑 CRITICAL: Find the current leader for this restaurant
    const leader = await devicesCollection.findOne({
      restaurantId,
      isLeader: true,
      deviceType: 'desktop',
      status: 'active',
      lastHeartbeat: { $gte: fifteenMinutesAgo },
      publicIP: { $exists: true }
    });

    // 🚨 LEADER ENFORCEMENT: Only allow connections to verified leader
    if (!leader) {
      console.error(`❌ [ProxyLeaderCheck] No active leader found for restaurant ${restaurantId}`);
      return null;
    }

    // Check if requested device is the leader
    if (leader.deviceId !== deviceId) {
      console.error(`❌ [ProxyLeaderCheck] Rejecting connection to non-leader device`, {
        requestedDevice: deviceId,
        currentLeader: leader.deviceId,
        restaurantId
      });
      return null;
    }

    console.log(`✅ [ProxyLeaderCheck] Connecting to verified leader: ${leader.deviceId}`);
    return leader;
    
  } catch (error) {
    console.error('Failed to find target device:', error);
    return null;
  }
}

async function proxyRequest(req: NextRequest, targetUrl: string, restaurantId: string) {
  const headers = new Headers();
  
  // Copy relevant headers from the original request
  req.headers.forEach((value, key) => {
    if (key.toLowerCase() !== 'host' && 
        key.toLowerCase() !== 'authorization' &&
        !key.toLowerCase().startsWith('x-')) {
      headers.set(key, value);
    }
  });
  
  // Use restaurant credentials for CouchDB access (admin:restaurantId)
  const username = 'admin';
  const password = restaurantId;
  
  headers.set('Authorization', 'Basic ' + Buffer.from(`${username}:${password}`).toString('base64'));
  
  const requestInit: RequestInit = {
    method: req.method,
    headers,
    // Add connection optimization
    keepalive: true,
  };
  
  // Add body for POST, PUT, PATCH requests
  if (req.method !== 'GET' && req.method !== 'HEAD') {
    try {
      const body = await req.text();
      if (body) {
        requestInit.body = body;
      }
    } catch (error) {
      console.error('[Proxy] Error reading request body:', error);
    }
  }
  
  try {
    const response = await fetch(targetUrl, requestInit);
    
    const responseHeaders = new Headers();
    response.headers.forEach((value, key) => {
      if (key.toLowerCase() !== 'transfer-encoding' &&
          key.toLowerCase() !== 'connection' &&
          key.toLowerCase() !== 'keep-alive') {
        responseHeaders.set(key, value);
      }
    });
    
    // Add CORS headers
    responseHeaders.set('Access-Control-Allow-Origin', '*');
    responseHeaders.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    responseHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    const responseBody = await response.text();
    
    return new NextResponse(responseBody, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
    });
    
  } catch (error) {
    console.error('[Proxy] Error proxying request:', {
      targetUrl,
      method: req.method,
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined
    });
    
    // Provide specific error messages based on error type
    let errorMessage = 'Proxy request failed';
    let statusCode = 502;
    
    if (error instanceof Error) {
      if (error.message.includes('ECONNREFUSED')) {
        errorMessage = 'Desktop device is offline or CouchDB not running';
        statusCode = 503;
      } else if (error.message.includes('ENOTFOUND')) {
        errorMessage = 'Cannot reach desktop device - check network';
        statusCode = 503;
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Request timeout - desktop may be slow to respond';
        statusCode = 504;
      }
    }
    
    return NextResponse.json({ 
      error: errorMessage,
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { 
      status: statusCode,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  }
}

export async function GET(req: NextRequest, { params }: RouteParams) {
  return handleProxyRequest(req, params);
}

export async function POST(req: NextRequest, { params }: RouteParams) {
  return handleProxyRequest(req, params);
}

export async function PUT(req: NextRequest, { params }: RouteParams) {
  return handleProxyRequest(req, params);
}

export async function DELETE(req: NextRequest, { params }: RouteParams) {
  return handleProxyRequest(req, params);
}

export async function PATCH(req: NextRequest, { params }: RouteParams) {
  return handleProxyRequest(req, params);
}

async function handleProxyRequest(req: NextRequest, params: { deviceId: string; path: string[] }) {
  try {
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization token required' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const decoded = verifyToken(token);
    
    if (!decoded || !decoded.restaurantId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // CRITICAL: Validate sync operation to prevent cross-restaurant access
    const dbName = params.path[0]; // First path segment is the database name
    const syncValidation = validateSyncOperation({
      authToken: token,
      targetDeviceId: params.deviceId,
      requestedDbName: dbName,
      userRestaurantId: decoded.restaurantId
    });
    
    if (!syncValidation.isValid) {
      console.error('[Proxy] CRITICAL: Cross-restaurant sync attempt blocked!', {
        deviceId: params.deviceId,
        dbName,
        userRestaurantId: decoded.restaurantId,
        error: syncValidation.error,
        ip: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
      });
      
      return NextResponse.json({ 
        error: 'Access denied - security validation failed'
      }, { status: 403 });
    }

    const { deviceId, path } = params;
    
    // Find the target desktop device
    const targetDevice = await getTargetDevice(deviceId, decoded.restaurantId);
    
    if (!targetDevice) {
      // 👑 Provide specific error message for leader enforcement
      return NextResponse.json({ 
        error: 'Cannot connect - only the restaurant leader accepts connections',
        deviceId,
        details: 'The requested device is not the registered leader or is offline. Only the leader device can accept sync connections.'
      }, { status: 404 });
    }
    
    // Use validated restaurant ID from security check
    const restaurantId = syncValidation.restaurantId!;
    const pathString = path.join('/');
    
    // Construct the target URL using public IP for internet access
    const targetUrl = `http://${targetDevice.publicIP}:${targetDevice.couchdbPort}/${pathString}`;
    
    // Get query parameters from the original request
    const url = new URL(req.url);
    const queryString = url.search;
    const finalTargetUrl = targetUrl + queryString;
    
    console.log(`[Proxy] ${req.method} ${finalTargetUrl} (restaurant: ${restaurantId})`);
    
    return await proxyRequest(req, finalTargetUrl, restaurantId);
    
  } catch (error) {
    console.error('[Proxy] Error handling request:', error);
    return NextResponse.json({ 
      error: 'Proxy request failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  }
}

export async function OPTIONS(req: NextRequest, { params }: RouteParams) {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS, PATCH',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}