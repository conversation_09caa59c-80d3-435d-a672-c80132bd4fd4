import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

export { dynamic, revalidate } from '@/lib/api-config';

/**
 * POST /api/admin/login
 * Body: { email: string, password: string }
 * Only allows configured admin email and password via env.
 * Issues a short-lived admin JWT (admin: true) for guarding admin APIs.
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json().catch(() => ({}));
    const email = body?.email;
    const password = body?.password;

    const ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';
    const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD;
    const JWT_SECRET = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET;

    if (!JWT_SECRET) {
      return NextResponse.json({ error: 'Server misconfiguration' }, { status: 500 });
    }
    if (!ADMIN_PASSWORD) {
      return NextResponse.json({ error: 'Admin password not configured' }, { status: 500 });
    }
    if (typeof email !== 'string' || typeof password !== 'string') {
      return NextResponse.json({ error: 'Invalid body' }, { status: 400 });
    }

    if (email !== ADMIN_EMAIL || password !== ADMIN_PASSWORD) {
      return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 });
    }

    const token = jwt.sign(
      {
        sub: 'admin',
        name: 'Platform Admin',
        email: ADMIN_EMAIL,
        role: 'admin',
        restaurantId: 'platform',
        admin: true,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 60 * 60, // 1 hour
      },
      JWT_SECRET
    );

    return NextResponse.json({ token });
  } catch {
    return NextResponse.json({ error: 'Login failed' }, { status: 500 });
  }
}

// Handle CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}