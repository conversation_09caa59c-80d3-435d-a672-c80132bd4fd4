import { NextRequest, NextResponse } from 'next/server';

// Static export configuration
export { dynamic, revalidate } from '@/lib/api-config';

// import { getConnectionStatus, ConnectionType } from '@/lib/network/connectivity';

/**
 * API endpoint for LAN health check
 * This endpoint is used to check if the device is available on the LAN
 */

export async function GET(request: NextRequest) {
  // Get the current connection status
  // const connectionStatus = getConnectionStatus();
  
  return NextResponse.json({
    status: 'ok',
    timestamp: Date.now(),
    connectionType: 'LAN',
    isLanMode: true,
  });
}

export async function HEAD(request: NextRequest) {
  // Simple HEAD response for quick connectivity checks
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
    },
  });
}

export async function OPTIONS() {
  // Handle CORS preflight requests
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
