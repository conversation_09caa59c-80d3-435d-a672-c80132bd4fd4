import { NextRequest, NextResponse } from 'next/server';
import { corsJsonResponse, corsOptionsResponse } from '@/lib/utils/cors';
import { isMongoDBReachable } from '@/lib/mongodb';

// Dynamic route for health checks
export const dynamic = 'force-dynamic';

/**
 * API endpoint for server health check
 * Used by web builds to verify server connectivity before authentication
 */

export async function GET(request: NextRequest) {
  try {
    console.log('[API Health] Performing health check...');
    
    // Check MongoDB connection
    const mongoStatus = await isMongoDBReachable();
    console.log('[API Health] MongoDB status:', mongoStatus);
    
    const healthData = {
      status: mongoStatus.reachable ? 'ok' : 'degraded',
      timestamp: Date.now(),
      server: 'bistro-api',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        mongodb: {
          status: mongoStatus.reachable ? 'connected' : 'disconnected',
          error: mongoStatus.error || null,
        },
        auth: {
          status: 'available',
          endpoint: '/api/auth/login',
        }
      },
      corsEnabled: true,
    };
    
    return corsJsonResponse(healthData, {
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      }
    });
  } catch (error) {
    console.error('[API Health] Error:', error);
    return corsJsonResponse({
      status: 'error',
      timestamp: Date.now(),
      error: 'Health check failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function HEAD(request: NextRequest) {
  // Simple HEAD response for quick connectivity checks
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
    },
  });
}

export async function OPTIONS() {
  return corsOptionsResponse();
}