"use client"

import React, { useState, Suspense, Component, ReactNode } from 'react';
import { formatCurrency } from '@/lib/utils/currency';
import { 
  LayoutDashboardIcon, 
  ShoppingCartIcon, 
  DollarSignIcon, 
  ClipboardListIcon,
  UsersIcon, 
  BarChart4Icon, 
  RefreshCwIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  Loader2Icon
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { DateRange } from 'react-day-picker';

// Analytics Tab components imports
import SalesTab from '@/components/analytics/tabs/SalesTab';
import CogsTab from '@/components/analytics/tabs/CogsTab';
import ExpensesV4Tab from '@/components/analytics/tabs/ExpensesV4Tab';
import TabFallback from '@/components/analytics/tabs/TabFallback';

// Loading component for tabs
const TabLoadingFallback = ({ tabName }: { tabName: string }) => (
  <div className="flex items-center justify-center h-64">
    <div className="flex flex-col items-center gap-3">
      <Loader2Icon className="h-8 w-8 animate-spin text-primary" />
      <p className="text-sm text-muted-foreground">Chargement {tabName}...</p>
    </div>
  </div>
);

// Error boundary component for tabs
const TabErrorFallback = ({ tabName, error }: { tabName: string; error?: string }) => (
  <div className="flex items-center justify-center h-64">
    <div className="flex flex-col items-center gap-3 text-center">
      <div className="p-3 bg-red-50 rounded-full">
        <BarChart4Icon className="h-8 w-8 text-red-500" />
      </div>
      <div>
        <p className="text-sm font-medium text-red-700">Erreur de chargement {tabName}</p>
        {error && <p className="text-xs text-red-600 mt-1">{error}</p>}
        <p className="text-xs text-muted-foreground mt-2">Veuillez actualiser la page</p>
      </div>
    </div>
  </div>
);

// Error Boundary Class Component
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Analytics tab error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }

    return this.props.children;
  }
}

export default function AnalyticsPage() {
  const [activeTab, setActiveTab] = useState('sales');
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);

  const handleRefresh = () => {
    setIsLoading(true);
    // Here you would fetch fresh data from your APIs/databases
    setTimeout(() => setIsLoading(false), 1000); // Simulating data fetch
  };

  const clearDateRange = () => {
    setDateRange(undefined);
  };

  return (
    <div className="container max-w-full px-2 py-3 space-y-3 sm:px-4 sm:py-4 sm:space-y-4">
      {/* Header with date range picker - Mobile optimized */}
      <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div className="flex items-center gap-2 sm:gap-3">
          <div className="p-1.5 sm:p-2 bg-primary/10 rounded-full">
            <BarChart4Icon className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
          </div>
          <div>
            <h1 className="text-lg sm:text-xl md:text-2xl font-bold">Analytique</h1>
            <p className="text-xs text-muted-foreground hidden sm:block">Aperçu et métriques de performance</p>
          </div>
        </div>
        
        <div className="flex gap-1.5 sm:gap-2">
          <DateRangePicker 
            value={dateRange}
            onChange={setDateRange}
            className="h-8 text-xs sm:h-9 sm:text-sm"
          />
          {dateRange && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearDateRange}
              className="h-8 px-2 text-xs sm:h-9 sm:px-4"
            >
              <span className="hidden xs:inline">Effacer</span>
              <span className="xs:hidden">✕</span>
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
            className="h-8 px-2 text-xs sm:h-9 sm:px-3"
          >
            <RefreshCwIcon className={`h-3 w-3 sm:h-4 sm:w-4 ${isLoading ? "animate-spin" : ""}`} />
            <span className="ml-1 hidden xs:inline">
              {isLoading ? "Actualisation..." : "Actualiser"}
            </span>
          </Button>
        </div>
      </div>

      {/* Main Tabs - Mobile optimized */}
      <Tabs defaultValue="sales" value={activeTab} onValueChange={setActiveTab} className="space-y-3 sm:space-y-4">
        <TabsList className="grid grid-cols-3 w-full h-10 p-1 bg-muted rounded-lg">
          <TabsTrigger value="sales" className="flex items-center justify-center gap-1.5 px-1 text-xs sm:px-2 sm:text-sm">
            <ShoppingCartIcon className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
            <span className="hidden xs:inline">Ventes</span>
          </TabsTrigger>
          <TabsTrigger value="cogs" className="flex items-center justify-center gap-1.5 px-1 text-xs sm:px-2 sm:text-sm">
            <DollarSignIcon className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
            <span className="hidden xs:inline">CMV</span>
          </TabsTrigger>
          <TabsTrigger value="expenses" className="flex items-center justify-center gap-1.5 px-1 text-xs sm:px-2 sm:text-sm">
            <ClipboardListIcon className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
            <span className="hidden xs:inline">Dépenses</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="sales" className="space-y-4">
          <Suspense fallback={<TabLoadingFallback tabName="Ventes" />}>
            <ErrorBoundary fallback={<TabErrorFallback tabName="Ventes" />}>
              <SalesTab dateRange={dateRange} />
            </ErrorBoundary>
          </Suspense>
        </TabsContent>
        
        <TabsContent value="cogs" className="space-y-4">
          {process.env.NODE_ENV === 'development' ? (
            <Suspense fallback={<TabLoadingFallback tabName="CMV (Coût des Marchandises Vendues)" />}>
              <ErrorBoundary fallback={<TabErrorFallback tabName="CMV (Coût des Marchandises Vendues)" />}>
                <CogsTab dateRange={dateRange} />
              </ErrorBoundary>
            </Suspense>
          ) : (
            <TabFallback tabName="CMV (Coût des Marchandises Vendues)" isComingSoon={true} />
          )}
        </TabsContent>
        
        <TabsContent value="expenses" className="space-y-4">
          {process.env.NODE_ENV === 'development' ? (
            <Suspense fallback={<TabLoadingFallback tabName="Gestion des Dépenses" />}>
              <ErrorBoundary fallback={<TabErrorFallback tabName="Gestion des Dépenses" />}>
                <ExpensesV4Tab dateRange={dateRange} />
              </ErrorBoundary>
            </Suspense>
          ) : (
            <TabFallback tabName="Gestion des Dépenses" isComingSoon={true} />
          )}
        </TabsContent>
      </Tabs>


    </div>
  );
}