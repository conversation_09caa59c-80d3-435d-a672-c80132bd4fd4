'use client';

import { AppSidebar, MobileSidebar } from "@/components/app-sidebar";
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { SidebarInset, SidebarTrigger, SidebarProvider } from "@/components/ui/sidebar";
import { Menu } from "lucide-react";
import FloatingDebugButton from "@/components/debug/FloatingDebugButton";
import { useMobileLayout } from '@/hooks/use-mobile-layout';
import { cn } from '@/lib/utils';

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isMobile, safeAreaInsets } = useMobileLayout();

  if (isMobile) {
    // Mobile layout with Sheet overlay
    return (
      <div className="flex flex-col h-screen w-full">
        {/* Ultra-compact Mobile Header */}
        <header 
          className={cn(
            "sticky top-0 z-30 flex items-center gap-3 border-b bg-background/95 backdrop-blur-sm",
            "h-12 px-3 safe-top"
          )}
          style={{
            paddingTop: `max(${safeAreaInsets.top}px, 4px)`
          }}
        >
          <MobileSidebar />
          
          <div className="flex-1 text-center">
            <h1 className="font-semibold text-foreground text-sm">
              Restaurant Manager
            </h1>
          </div>
          
          <div className="w-8" /> {/* Balance space */}
        </header>

        {/* Main Content */}
        <main 
          className="flex-1 flex flex-col min-h-0 bg-background overflow-auto"
          style={{
            paddingBottom: `max(${safeAreaInsets.bottom}px, 12px)`
          }}
        >
          <div className="flex-1 flex flex-col min-h-0 p-3 gap-3">
            <ProtectedRoute>
              {children}
            </ProtectedRoute>
          </div>
        </main>
        
        <FloatingDebugButton />
      </div>
    )
  }

  // Desktop layout with traditional sidebar
  return (
    <SidebarProvider>
      <div className="flex h-screen w-full">
        <AppSidebar />
        <SidebarInset className="flex-1 flex flex-col min-h-0">
          {/* Desktop Header */}
          <header 
            className="sticky top-0 z-30 flex items-center gap-4 border-b bg-background/95 backdrop-blur-sm h-14 px-6 lg:hidden"
          >
            <SidebarTrigger className="lg:hidden">
              <Menu className="h-6 w-6" />
              <span className="sr-only">Toggle Menu</span>
            </SidebarTrigger>
            
            <div className="flex-1 text-center">
              <h1 className="font-semibold text-foreground text-lg">
                Restaurant Manager
              </h1>
            </div>
          </header>

          {/* Main Content */}
          <main className="flex-1 flex flex-col min-h-0 bg-background">
            <div className="flex-1 flex flex-col min-h-0 p-4 sm:p-6 gap-4">
              <ProtectedRoute>
                {children}
              </ProtectedRoute>
            </div>
          </main>
          
          <FloatingDebugButton />
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}