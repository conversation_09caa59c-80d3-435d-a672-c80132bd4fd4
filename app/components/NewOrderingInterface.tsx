import React, { useEffect, useReducer, useState, useMemo, useRef, useCallback } from "react";
import { AlertCircle, ArrowLeft, Check, ChevronsUpDown, ChevronLeft, ChevronRight, ChevronDown, Clock, Copy, Edit2, FileDown, Filter, Info, Loader2, Minus, MinusCircle, Plus, PlusCircle, Receipt, Search, Send, ShoppingCart, Trash2, Users, UserRound, MoreVertical, ChefHat, CheckSquare, XCircle, CheckCircle, RefreshCw, X, Beaker, Pizza, Utensils, Package, Truck, Play, AlertTriangle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { useMenuV4 } from "@/lib/hooks/use-menu-v4";
import { useSupplements } from "@/lib/hooks/useSupplements";
import { useTableDB } from "@/lib/hooks/useTableDB";
import { useOrderV4 } from "@/lib/hooks/use-order-v4";
import { useStaffMenuV4 } from "@/lib/hooks/useStaffMenuV4";
import { MenuItem } from "@/lib/db/v4/schemas/menu-schema";
import { OrderItem, PizzaQuarter, DeliveryPerson } from "@/lib/db/v4/schemas/order-schema";
import { OrderAddon } from "@/lib/db/v4/schemas/order-schema";
import PizzaQuarterPanel from "./PizzaQuarterPanel";

import { kitchenPrintService } from '@/lib/services/kitchen-print-service';
import type { Order } from "@/lib/db/v4/schemas/order-schema";
import { useEditOrder } from '@/components/providers/EditOrderContext';
import { PRESET_COLORS } from "@/lib/constants";


import { OrderType, getOrderTypeLabel, requiresTable, requiresCustomerInfo, requiresCustomerPhone, requiresCustomerAddress } from '@/lib/types/order-types';
import { format } from 'date-fns';
import { staffService } from '@/lib/services/staff-service';
import StaffMenuManager from '@/components/staff/StaffMenuManager';
import PaymentForm, { WasteProcessingData, DiscountData } from './PaymentForm';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useSimplifiedOrderFinance } from '@/lib/services/simplified-order-finance';
import { useOrderHold } from '@/lib/hooks/useOrderHold';
import { Pause, List } from 'lucide-react';


// Color generation utility no longer needed - removed

// Function to get color for menu items based strictly on category color from DB
const getItemColor = (categoryId: string, itemId: string, categoriesArray?: any[]): string => {
  if (categoriesArray && categoriesArray.length) {
    const category = categoriesArray.find(cat => cat.id === categoryId);
    if (category && category.items) {
      const item = category.items.find((item: any) => item.id === itemId);
      if (item && item.color) {
        return item.color; // Use the individual item's color from DB
      }
    }
  }
  // If no color in DB, use a neutral fallback (no palette, no generation)
  return '#e0e0e0';
};

// Add a function to get a lighter version of the same color for backgrounds
const getLightColor = (color: string): string => {
  return `${color}15`; // 15% opacity version of the same color
};

type NewOrder = Omit<Order, '_id' | 'type' | 'schemaVersion' | 'createdAt' | 'updatedAt' | 'id'>;

// Define our reducer state type
interface OrderState {
  _id: string;
  id: string;
  type: string;
  orderType: OrderType;
  status: string;
  tableId: string;
  items: OrderItem[];
  total: number;
  createdAt: string;
  updatedAt: string;
  schemaVersion: string;
  notes: string;
  customer?: {
    name: string;
    phone: string;
    address?: string;
  };
  deliveryPerson?: DeliveryPerson;
  paymentStatus?: string;
}

// Define our UI state type (for temporary selections during item customization)
interface UiState {
  selectedCategory: string;
  selectedItemForSupplements: string | null; // Format: "itemId-size" for size-specific selection
  selectedItemSizes: { [key: string]: string };
  itemNotes: { [key: string]: string };
  selectedSupplements: { [key: string]: Set<string> };
  lastAddedItem: string | null;
}

// Define possible actions for our reducer
type OrderAction =
  | { type: 'INITIALIZE_ORDER' }
  | { type: 'LOAD_ORDER', payload: any }
  | { type: 'ADD_ITEM', payload: { item: MenuItem, size: string, addons: OrderAddon[], notes: string, categoryId: string, categoryName: string } }
  | { type: 'ADD_CUSTOM_PIZZA', payload: { quarters: PizzaQuarter[], size: string, notes: string, categoryId: string, pricingMethod: 'max' | 'average' | 'fixed', fixedPrice?: number } }
  | { type: 'UPDATE_ITEM', payload: { itemId: string, updates: Partial<OrderItem> } }
  | { type: 'INCREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'DECREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'REMOVE_ITEM', payload: { itemId: string } }
  | { type: 'SET_ORDER_TYPE', payload: { orderType: OrderType } }
  | { type: 'SET_TABLE', payload: { tableId: string } }
  | { type: 'SET_NOTES', payload: { notes: string } }
  | { type: 'SET_CUSTOMER_INFO', payload: { name: string, phone: string, address?: string } }
  | { type: 'SET_DELIVERY_INFO', payload: DeliveryPerson }
  | { type: 'RESET_ORDER' }
  | { type: 'RESUME_ORDER', payload: any };

// Helper functions for the reducer
const getItemSignature = (item: OrderItem): string => {
  const baseSignature = [
    item.menuItemId || '',
    item.size || '',
    ((item.addons || []).map((a: OrderAddon) => a?.id || '').filter(Boolean).sort().join(',')),
    ((item.notes || '').trim()),
    (item as any).categoryName || '' // 🎯 Include categoryName for consistency
  ];

  // For custom pizzas, include the quarters composition in the signature
  if (item.compositeType === 'pizza_quarters' && item.quarters && item.quarters.length > 0) {
    const quartersSignature = item.quarters
      .map(q => `${q.menuItemId}:${q.size}:${q.price}`)
      .sort()
      .join(',');
    baseSignature.push(quartersSignature);
  }

  return baseSignature.join('|');
};

const calculateTotal = (items: OrderItem[]): number => {
  const combinedItemsMap: { [key: string]: OrderItem } = {};
  items.forEach(item => {
    const itemSignature = getItemSignature(item);
    if (combinedItemsMap[itemSignature]) combinedItemsMap[itemSignature].quantity += item.quantity;
    else combinedItemsMap[itemSignature] = { ...item };
  });
  return Object.values(combinedItemsMap).reduce((total, item) => {
    const itemTotal = item.price * item.quantity;
    const addonTotal = (item.addons || []).reduce((sum, addon) => sum + (addon.price || 0), 0) * item.quantity;
    return total + itemTotal + addonTotal;
  }, 0);
};

// Initialize order reducer state
const initialOrderState: OrderState = {
  _id: "",
  id: "",
  type: "order_document",
  orderType: "dine-in" as OrderType,
  status: "pending",
  tableId: "",
  items: [],
  total: 0,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  schemaVersion: "v4.0",
  notes: ""
};

// Initialize UI state
const initialUiState: UiState = {
  selectedCategory: "",
  selectedItemForSupplements: null,
  selectedItemSizes: {},
  itemNotes: {},
  selectedSupplements: {},
  lastAddedItem: null
};

// Reducer function
const orderReducer = (state: OrderState, action: OrderAction): OrderState => {
  switch (action.type) {
    case 'INITIALIZE_ORDER':
      return { ...initialOrderState };

    case 'LOAD_ORDER':
      return { ...action.payload };

    case 'ADD_ITEM': {
      const { item, size, addons, notes, categoryId, categoryName } = action.payload;
      const price = item.prices[size] || Object.values(item.prices)[0] || 0;
      const uniqueId = `order_item_${Date.now()}_${Math.floor(Math.random() * 10000)}`;

      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: item.id,
        name: item.name,
        size: size,
        price: price,
        quantity: 1,
        addons: addons,
        notes: notes,
        categoryId: categoryId, // 🎯 Keep categoryId for compatibility
        categoryName: categoryName // 🎯 Add categoryName for printing
      };

      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }

    case 'ADD_CUSTOM_PIZZA': {
      const { quarters, size, notes, categoryId, pricingMethod, fixedPrice } = action.payload;
      const uniqueId = `custom_pizza_${Date.now()}_${Math.floor(Math.random() * 10000)}`;

      // Calculate price based on pricing method
      let price = 0;
      if (pricingMethod === 'max') {
        price = Math.max(...quarters.map(q => q.price));
      } else if (pricingMethod === 'average') {
        price = quarters.reduce((sum, q) => sum + q.price, 0) / quarters.length;
      } else if (pricingMethod === 'fixed') {
        price = fixedPrice || 0;
      }

      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: 'custom_pizza',
        name: `Pizza Personnalisée (${quarters.map(q => q.name).join(', ')})`.length > 50
          ? `Pizza Personnalisée (${quarters.length} types)`
          : `Pizza Personnalisée (${quarters.map(q => q.name).join(', ')})`,
        size: size,
        price: price,
        quantity: 1,
        addons: [],
        notes: notes,
        categoryId: categoryId,
        compositeType: 'pizza_quarters',
        quarters: quarters
      };

      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }

    case 'UPDATE_ITEM': {
      const { itemId, updates } = action.payload;
      const updatedItems = state.items.map(item =>
        item.id === itemId ? { ...item, ...updates } : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }

    case 'INCREMENT_QUANTITY': {
      const { itemId } = action.payload;
      const updatedItems = state.items.map(item =>
        item.id === itemId ? { ...item, quantity: item.quantity + 1 } : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }

    case 'DECREMENT_QUANTITY': {
      const { itemId } = action.payload;
      const targetItem = state.items.find(item => item.id === itemId);

      if (!targetItem || targetItem.quantity <= 1) {
        const updatedItems = state.items.filter(item => item.id !== itemId);
        return {
          ...state,
          items: updatedItems,
          total: calculateTotal(updatedItems)
        };
      }

      const updatedItems = state.items.map(item =>
        item.id === itemId ? { ...item, quantity: item.quantity - 1 } : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }

    case 'REMOVE_ITEM': {
      const { itemId } = action.payload;
      const updatedItems = state.items.filter(item => item.id !== itemId);
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }

    case 'SET_ORDER_TYPE': {
      return {
        ...state,
        orderType: action.payload.orderType
      };
    }

    case 'SET_TABLE': {
      return {
        ...state,
        tableId: action.payload.tableId
      };
    }

    case 'SET_NOTES': {
      return {
        ...state,
        notes: action.payload.notes
      };
    }

    case 'SET_CUSTOMER_INFO': {
      return {
        ...state,
        customer: {
          name: action.payload.name,
          phone: action.payload.phone,
          address: action.payload.address
        }
      };
    }

    case 'SET_DELIVERY_INFO': {
      return {
        ...state,
        deliveryPerson: action.payload
      };
    }


    case 'RESET_ORDER': {
      return { ...initialOrderState };
    }

    case 'RESUME_ORDER': {
      return { ...action.payload };
    }

    default:
      return state;
  }
};

// Small components that we'll use in our main interface

// CategorySelector Component
interface CategorySelectorProps {
  categories: any[];
  selectedCategory: string;
  onSelectCategory: (categoryId: string) => void;
}

const CategorySelector: React.FC<CategorySelectorProps> = React.memo(({
  categories,
  selectedCategory,
  onSelectCategory
}) => {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-1">
      {categories.map((category) => {
        const isActive = selectedCategory === category.id;
        return (
          <div
            key={category.id}
            className={`rounded-md p-1 cursor-pointer transition-all duration-200 border flex flex-col items-center justify-center gap-0.5 hover:border-primary/70 hover:bg-primary/5 min-h-[52px] ${isActive ? 'border-primary bg-primary/10 shadow-sm' : 'border-muted bg-background'}`}
            onClick={() => onSelectCategory(category.id || '')}
          >
            <div className={`text-xl ${isActive ? 'text-primary' : 'text-muted-foreground'}`}>
              {category.emoji || '🍽️'}
            </div>
            <div className={`font-medium text-center text-[11px] leading-tight truncate w-full ${isActive ? 'text-primary' : 'text-foreground'}`}>
              {category.name}
            </div>
          </div>
        );
      })}
    </div>
  );
});

// MenuItemCard Component 
interface MenuItemCardProps {
  item: MenuItem;
  categoryId: string;
  categories: any[];
  isSelected: boolean;
  selectedSize: string | undefined;
  lastAddedItem: string | null;
  onSelect: (itemId: string) => void;
  onAddItem: (item: MenuItem, size: string) => void | Promise<void>;
}

const MenuItemCard: React.FC<MenuItemCardProps> = React.memo(({
  item,
  categoryId,
  categories,
  isSelected,
  selectedSize,
  lastAddedItem,
  onSelect,
  onAddItem
}) => {
  // Handler function to properly handle size button clicks
  const handleSizeButtonClick = useCallback((e: React.MouseEvent, size: string) => {
    console.log('🔘 [MenuItemCard] Button clicked:', { itemId: item.id, itemName: item.name, size });
    e.stopPropagation(); // Stop event from bubbling up to parent
    onAddItem(item, size);
  }, [item, onAddItem]);

  // Filter out sizes that have a price of 0, with an exception for custom pizza
  const visibleSizes = Object.entries(item.prices).filter(([sz, pr]) => {
    // 🍕 Exception for custom pizza: always show the button, regardless of price
    if (item.id === 'custom_pizza') {
      return true;
    }
    const num = Number(pr || 0);
    return !Number.isNaN(num) && num > 0;
  });

  const hasMultipleSizes = visibleSizes.length > 1;
  // Calculate if this is the only visible size
  const isOnlySize = visibleSizes.length === 1;

  // Generate color based on item and category
  const itemColor = getItemColor(categoryId, item.id, categories); // Pass categories

  return (
    <div
      className={cn(
        "p-2 transition-colors border rounded-lg flex flex-col justify-between h-auto",
        isSelected ? "ring-2 ring-primary/40" : "border-muted"
      )}
      style={{
        backgroundColor: itemColor
      }}
    // 🎯 Card click removed - only size buttons should be clickable
    >
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <div className="font-semibold text-sm truncate">{item.name}</div>
        </div>
      </div>
      <div className="mt-2 flex flex-wrap gap-1.5">
        {visibleSizes.map(([size, price]) => {
          const isItemSizeSelected = isSelected && selectedSize === size;
          const isJustAdded = lastAddedItem === `${item.id}-${size}`;

          return (
            <Button
              key={`${item.id}-${size}`}
              variant={isItemSizeSelected ? "default" : "outline"}
              size="sm"
              className={`h-8 py-1 text-xs rounded-md transition-all duration-300 flex-grow ${isOnlySize ? 'w-full' : 'min-w-[calc(50%-0.5rem)]'} ${isItemSizeSelected ? "bg-gray-900 text-white border-gray-900" : "bg-white text-gray-700 hover:bg-gray-100 border-gray-200"} ${isJustAdded ? 'shadow-sm border-green-500 border-2 bg-green-50 text-green-700' : ''}`}
              onClick={(e) => handleSizeButtonClick(e, size)}
              type="button"
            >
              <div className={`flex ${item.id === 'custom_pizza' ? 'justify-center' : 'justify-between'} w-full items-center`}>
                <span>{size === "default" ? "Classique" : size === "Personnalisé" ? "Personnalisé" : size}</span>
                {/* Show price for all menu items except custom pizza */}
                {item.id !== 'custom_pizza' && (
                  <span className={`font-medium tabular-nums ${isItemSizeSelected ? "text-white" : isJustAdded ? "text-green-700" : "text-primary"}`}>
                    {price as number} DA
                  </span>
                )}
              </div>
            </Button>
          );
        })}
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function to optimize rerenders
  return (
    prevProps.item.id === nextProps.item.id &&
    prevProps.isSelected === nextProps.isSelected &&
    prevProps.selectedSize === nextProps.selectedSize &&
    prevProps.lastAddedItem === nextProps.lastAddedItem
  );
});

// Set display name for better debugging
MenuItemCard.displayName = 'MenuItemCard';

// More subcomponents we'll use in our UI

// ItemCustomizationPanel Component
interface ItemCustomizationPanelProps {
  category: any;
  item: MenuItem;
  selectedSize: string;
  selectedSupplements: { [key: string]: Set<string> };
  itemNotes: { [key: string]: string };
  getSupplementKey: (itemId: string, size: string) => string;
  getItemNoteKey: (itemId: string, size: string) => string;
  isSupplementSelected: (itemId: string, size: string, supplementId: string) => boolean;
  toggleSupplementSelection: (itemId: string, size: string, supplementId: string) => void;
  updateItemNote: (itemId: string, size: string, note: string) => void;
  finalizeItem: () => void;
  categories: any[];
  // 🍕 Custom Pizza Props
  onCustomPizzaConfirm?: (quarters: PizzaQuarter[], size: string, notes: string, categoryId: string, pricingMethod: 'max' | 'average' | 'fixed', fixedPrice?: number) => void;
  customPizzaEditData?: {
    originalItemId: string;
    quarters: PizzaQuarter[];
    size: string;
    notes: string;
  } | null;
  // ❌ Close function
  onClose?: () => void;
  
}

const ItemCustomizationPanel: React.FC<ItemCustomizationPanelProps> = React.memo(({
  category,
  item,
  selectedSize,
  selectedSupplements,
  itemNotes,
  getSupplementKey,
  getItemNoteKey,
  isSupplementSelected,
  toggleSupplementSelection,
  updateItemNote,
  finalizeItem,
  categories,
  onCustomPizzaConfirm,
  customPizzaEditData,
  onClose
}) => {
  // Get the color for this item from its category
  const itemColor = item.color || category.color || '#e0e0e0';
  const lightColor = getLightColor(itemColor);

  // Get supplements from the category-specific supplements hook
  const { supplements } = useSupplements(category.id);
  // Filter to get only active supplements for this category
  const activeSupplements = supplements.filter(supplement => supplement.isActive !== false);

  // Use supplements directly without conversion
  const allSupplements = activeSupplements;

  // Get pizza items from the current category for selection
  const pizzaItems = category.items || [];

  // 🍕 Custom Pizza State for quarter selection
  const [pizzaPartition, setPizzaPartition] = useState<'half' | 'quarter' | null>(null);
  const [pizzaQuarters, setPizzaQuarters] = useState<(PizzaQuarter | null)[]>([null, null, null, null]);

  // Initialize pizza state when editing existing custom pizza
  useEffect(() => {
    if (customPizzaEditData && item.id === 'custom_pizza_editor') {
      const { quarters } = customPizzaEditData;
      if (quarters.length > 0) {
        // Set up the pizza quarters for editing
        const quarterArray: (PizzaQuarter | null)[] = [null, null, null, null];
        quarters.forEach((quarter, index) => {
          if (index < 4) {
            quarterArray[index] = quarter;
          }
        });
        setPizzaQuarters(quarterArray);

        // Determine partition type based on quarters
        const uniquePizzas = [...new Set(quarters.map(q => q.menuItemId))];
        if (uniquePizzas.length <= 2 && quarters.length === 4) {
          // Check if it's a half pizza (same pizza for quarters 0,1 and same for 2,3)
          const firstHalf = quarters[0]?.menuItemId === quarters[1]?.menuItemId;
          const secondHalf = quarters[2]?.menuItemId === quarters[3]?.menuItemId;
          if (firstHalf && secondHalf) {
            setPizzaPartition('half');
          } else {
            setPizzaPartition('quarter');
          }
        } else {
          setPizzaPartition('quarter');
        }
      }
    }
  }, [customPizzaEditData, item.id]);

  const handleToggleSupplement = (supplementId: string) => {
    toggleSupplementSelection(item.id, selectedSize, supplementId);
  };

  // 🍕 Pizza quarter selection handlers
  const handlePizzaQuarterSelect = useCallback((quarterIndex: number, value: string) => {
    if (value === 'none') {
      setPizzaQuarters(prev => {
        const newQuarters = [...prev];
        newQuarters[quarterIndex] = null;
        return newQuarters;
      });
    } else {
      const pizza = pizzaItems.find(p => p.id === value);
      if (pizza) {
        setPizzaQuarters(prev => {
          const newQuarters = [...prev];
          newQuarters[quarterIndex] = {
            menuItemId: pizza.id,
            name: pizza.name,
            price: pizza.prices[selectedSize] || Object.values(pizza.prices)[0] || 0,
            size: selectedSize
          };
          return newQuarters;
        });
      }
    }
  }, [selectedSize, pizzaItems]);

  const handlePizzaConfirm = useCallback(() => {
    if (onCustomPizzaConfirm && pizzaPartition) {
      const validQuarters = pizzaQuarters.filter(q => q !== null) as PizzaQuarter[];
      const requiredSections = pizzaPartition === 'half' ? 2 : 4;

      if (validQuarters.length !== requiredSections) {
        return;
      }

      if (validQuarters.length > 0) {
        onCustomPizzaConfirm(
          validQuarters,
          selectedSize,
          itemNotes[getItemNoteKey(item.id, selectedSize)] || '',
          category.id,
          category.quarterPricingMethod || 'max',
          category.fixedPricing?.[selectedSize]
        );
        // Reset quarters
        setPizzaQuarters([null, null, null, null]);
        setPizzaPartition(null); // Reset partition selection
        if (onClose) onClose();
      }
    }
  if (onClose) onClose();
  }, [pizzaQuarters, selectedSize, itemNotes, getItemNoteKey, item.id, category.id, category.quarterPricingMethod, onCustomPizzaConfirm, onClose, pizzaPartition]);

  const handlePizzaCancel = useCallback(() => {
    setPizzaPartition(null);
    setPizzaQuarters([null, null, null, null]);
    if (onClose) onClose();
  }, [onClose]);

  const handleItemNote = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateItemNote(item.id, selectedSize, e.target.value);
  };

  // Handle panel close - should NOT remove the last added item for custom pizza
  const handleClose = () => {
    // Reset pizza state when closing
    setPizzaPartition(null);
    setPizzaQuarters([null, null, null, null]);

    if (onClose) {
      onClose();
    } else {
      // For custom pizza, don't call finalizeItem as it shouldn't add another item
      if (item.id === 'custom_pizza' || item.id === 'custom_pizza_editor') {
        // Just close the panel without adding anything
        return;
      }
      finalizeItem();
    }
  };



  return (
    <div className="bg-background border border-border rounded-lg shadow-sm flex flex-col h-full">
      {/* 🎯 Compact Header */}
      <div className="px-2 py-1 border-b border-border bg-muted/20">
        <div className="flex items-center justify-between gap-1.5">
          <div className="flex items-center gap-1.5 flex-1 min-w-0">
            <h3 className="font-medium text-xs text-foreground truncate">{item.name}</h3>
            {selectedSize && selectedSize !== 'default' && (
              <Badge variant="secondary" className="text-xs flex-shrink-0 px-1 py-0">
                {selectedSize}
              </Badge>
            )}
            <Input
              placeholder="Notes..."
              value={itemNotes[getItemNoteKey(item.id, selectedSize)] || ''}
              onChange={handleItemNote}
              className="h-6 text-xs flex-1 max-w-32 bg-background border-muted"
            />
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            className="h-6 w-6 p-0 hover:bg-destructive hover:text-destructive-foreground"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-2">
          {/* 🔄 Compact Layout */}
          <div className="space-y-2.5">

            {/* 🍕 Custom Pizza Section */}
            {(item.id === 'custom_pizza' || item.id === 'custom_pizza_editor') && (
              <div>
                <div className="flex items-center gap-1.5 mb-3">
                  <Pizza className="h-3.5 w-3.5 text-orange-600" />
                  <h4 className="text-xs font-medium text-orange-800">Pizza Personnalisée</h4>
                </div>

                {/* Step 1: Sectioning Choice */}
                {!pizzaPartition && (
                  <div className="space-y-2">
                    <p className="text-xs text-gray-600 mb-2">Choisir le type de partition :</p>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        onClick={() => setPizzaPartition('half')}
                        variant="outline"
                        size="sm"
                        className="h-10 text-xs border-orange-200 hover:border-orange-400 hover:bg-orange-50"
                      >
                        <div className="flex flex-col items-center">
                          <span className="font-medium">Moitié</span>
                          <span className="text-xs text-gray-500">1/2 + 1/2</span>
                        </div>
                      </Button>
                      <Button
                        onClick={() => setPizzaPartition('quarter')}
                        variant="outline"
                        size="sm"
                        className="h-10 text-xs border-orange-200 hover:border-orange-400 hover:bg-orange-50"
                      >
                        <div className="flex flex-col items-center">
                          <span className="font-medium">Quart</span>
                          <span className="text-xs text-gray-500">1/4 x 4</span>
                        </div>
                      </Button>
                    </div>
                  </div>
                )}

                {/* Step 2: Pizza Selection Based on Partition */}
                {pizzaPartition && (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <p className="text-xs text-gray-600">
                        {pizzaPartition === 'half' ? 'Sélectionner les moitiés :' : 'Sélectionner les quarts :'}
                      </p>
                      <Button
                        onClick={() => {
                          setPizzaPartition(null);
                          setPizzaQuarters([null, null, null, null]);
                        }}
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700"
                      >
                        <ArrowLeft className="h-3 w-3 mr-1" />
                        Retour
                      </Button>
                    </div>

                    {/* Half Pizza Selection */}
                    {pizzaPartition === 'half' && (
                      <div className="grid grid-cols-2 gap-2">
                        <div className="space-y-1">
                          <Label className="text-xs font-medium text-orange-700">Moitié Gauche</Label>
                          <Select
                            value={pizzaQuarters[0]?.menuItemId || 'none'}
                            onValueChange={(value) => {
                              if (value === 'none') {
                                setPizzaQuarters(prev => [null, null, prev[2], prev[3]]);
                              } else {
                                const pizza = pizzaItems.find(p => p.id === value);
                                if (pizza) {
                                  const quarter = {
                                    menuItemId: pizza.id,
                                    name: pizza.name,
                                    price: pizza.prices[selectedSize] || Object.values(pizza.prices)[0] || 0,
                                    size: selectedSize
                                  };
                                  // For half pizza, only fill position 0 (left half)
                                  setPizzaQuarters(prev => [quarter, null, prev[2], prev[3]]);
                                }
                              }
                            }}
                          >
                            <SelectTrigger className="h-8 text-xs">
                              <SelectValue placeholder="Choisir pizza" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">Aucune</SelectItem>
                              {pizzaItems.map((pizza: MenuItem) => (
                                <SelectItem key={pizza.id} value={pizza.id}>
                                  {pizza.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-1">
                          <Label className="text-xs font-medium text-orange-700">Moitié Droite</Label>
                          <Select
                            value={pizzaQuarters[2]?.menuItemId || 'none'}
                            onValueChange={(value) => {
                              if (value === 'none') {
                                setPizzaQuarters(prev => [prev[0], prev[1], null, prev[3]]);
                              } else {
                                const pizza = pizzaItems.find(p => p.id === value);
                                if (pizza) {
                                  const quarter = {
                                    menuItemId: pizza.id,
                                    name: pizza.name,
                                    price: pizza.prices[selectedSize] || Object.values(pizza.prices)[0] || 0,
                                    size: selectedSize
                                  };
                                  // For half pizza, only fill position 2 (right half)
                                  setPizzaQuarters(prev => [prev[0], prev[1], quarter, null]);
                                }
                              }
                            }}
                          >
                            <SelectTrigger className="h-8 text-xs">
                              <SelectValue placeholder="Choisir pizza" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">Aucune</SelectItem>
                              {pizzaItems.map((pizza: MenuItem) => (
                                <SelectItem key={pizza.id} value={pizza.id}>
                                  {pizza.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    )}

                    {/* Quarter Pizza Selection */}
                    {pizzaPartition === 'quarter' && (
                      <div className="grid grid-cols-2 gap-2">
                        <div className="space-y-1">
                          <Label className="text-xs font-medium text-orange-700">Quart 1</Label>
                          <Select
                            value={pizzaQuarters[0]?.menuItemId || 'none'}
                            onValueChange={(value) => handlePizzaQuarterSelect(0, value)}
                          >
                            <SelectTrigger className="h-8 text-xs">
                              <SelectValue placeholder="Choisir" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">Aucune</SelectItem>
                              {pizzaItems.map((pizza: MenuItem) => (
                                <SelectItem key={pizza.id} value={pizza.id}>
                                  {pizza.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-1">
                          <Label className="text-xs font-medium text-orange-700">Quart 2</Label>
                          <Select
                            value={pizzaQuarters[1]?.menuItemId || 'none'}
                            onValueChange={(value) => handlePizzaQuarterSelect(1, value)}
                          >
                            <SelectTrigger className="h-8 text-xs">
                              <SelectValue placeholder="Choisir" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">Aucune</SelectItem>
                              {pizzaItems.map((pizza: MenuItem) => (
                                <SelectItem key={pizza.id} value={pizza.id}>
                                  {pizza.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-1">
                          <Label className="text-xs font-medium text-orange-700">Quart 3</Label>
                          <Select
                            value={pizzaQuarters[2]?.menuItemId || 'none'}
                            onValueChange={(value) => handlePizzaQuarterSelect(2, value)}
                          >
                            <SelectTrigger className="h-8 text-xs">
                              <SelectValue placeholder="Choisir" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">Aucune</SelectItem>
                              {pizzaItems.map((pizza: MenuItem) => (
                                <SelectItem key={pizza.id} value={pizza.id}>
                                  {pizza.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-1">
                          <Label className="text-xs font-medium text-orange-700">Quart 4</Label>
                          <Select
                            value={pizzaQuarters[3]?.menuItemId || 'none'}
                            onValueChange={(value) => handlePizzaQuarterSelect(3, value)}
                          >
                            <SelectTrigger className="h-8 text-xs">
                              <SelectValue placeholder="Choisir" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">Aucune</SelectItem>
                              {pizzaItems.map((pizza: MenuItem) => (
                                <SelectItem key={pizza.id} value={pizza.id}>
                                  {pizza.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    )}

                    {/* Summary and Actions */}
                    {pizzaQuarters.some(q => q !== null) && (
                      <div className="space-y-2 p-2 bg-orange-50 rounded border">
                        <div className="text-xs">
                          <span className="font-medium text-orange-800">Composition: </span>
                          <span className="text-orange-600">
                            {pizzaQuarters.filter(q => q !== null).map(q => q!.name).join(', ')}
                          </span>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            onClick={() => {
                              setPizzaPartition(null);
                              setPizzaQuarters([null, null, null, null]);
                            }}
                            variant="outline"
                            size="sm"
                            className="flex-1 h-7 text-xs"
                          >
                            Reset
                          </Button>
                          <Button
                            onClick={handlePizzaConfirm}
                            size="sm"
                            className="flex-1 h-7 text-xs bg-orange-600 hover:bg-orange-700"
                          >
                            Ajouter
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* 🧹 Supplements Section */}
            {allSupplements.length > 0 && item.id !== 'custom_pizza' && item.id !== 'custom_pizza_editor' && (
              <div>
                <div className="flex items-center gap-1.5 mb-2">
                  <Beaker className="h-3.5 w-3.5 text-muted-foreground" />
                  <h4 className="text-xs font-medium text-foreground">Suppléments</h4>
                </div>

                <div className="grid grid-cols-2 gap-1.5">
                  {allSupplements.map((supplement) => {
                    const isSelected = isSupplementSelected(item.id, selectedSize, supplement.id);
                    return (
                      <Button
                        key={`${item.id}-supplement-${supplement.id}`}
                        variant={isSelected ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleToggleSupplement(supplement.id)}
                        className={`h-9 justify-start text-left p-2 transition-all duration-200 ${isSelected
                          ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                          : 'bg-background hover:bg-muted/50 border-border'
                          }`}
                      >
                        <div className="flex items-center gap-1.5 w-full">
                          <div className={`w-3.5 h-3.5 rounded-sm flex items-center justify-center transition-colors border flex-shrink-0 ${isSelected
                            ? 'bg-primary-foreground text-primary border-primary-foreground'
                            : 'bg-background border-border'
                            }`}>
                            {isSelected && <Check className="h-2.5 w-2.5" />}
                          </div>
                          <span className="text-xs font-medium truncate">{supplement.name}</span>
                        </div>
                      </Button>
                    );
                  })}
                </div>
              </div>
            )}

            {/* No supplements message */}
            {allSupplements.length === 0 && (
              <div className="flex items-center justify-center text-muted-foreground text-xs py-3">
                Aucun supplément disponible
              </div>
            )}

          </div>
        </div>
      </ScrollArea>
    </div>
  );
}, (prevProps: ItemCustomizationPanelProps, nextProps: ItemCustomizationPanelProps) => {
  // Only re-render if these specific props change
  return (
    prevProps.item.id === nextProps.item.id &&
    prevProps.selectedSize === nextProps.selectedSize &&
    prevProps.itemNotes[prevProps.getItemNoteKey(prevProps.item.id, prevProps.selectedSize)] ===
    nextProps.itemNotes[nextProps.getItemNoteKey(nextProps.item.id, nextProps.selectedSize)] &&
    JSON.stringify(Array.from(prevProps.selectedSupplements[prevProps.getSupplementKey(prevProps.item.id, prevProps.selectedSize)] || new Set())) ===
    JSON.stringify(Array.from(nextProps.selectedSupplements[nextProps.getSupplementKey(nextProps.item.id, nextProps.selectedSize)] || new Set()))
  );
});

// Custom hook for panel state management
const usePanelState = (selectedItemForSupplements: string | null, displayCategories: any[]) => {
  const [error, setError] = useState<string | null>(null);

  const parsedData = useMemo(() => {
    if (!selectedItemForSupplements) return null;

    try {
      // Parse item ID from selectedItemForSupplements
      // Format is "itemId-size" where itemId can contain dashes
      // We need to extract the size (last part) and reconstruct the itemId
      let itemId: string;
      const parts = selectedItemForSupplements.split('-');

      if (selectedItemForSupplements.startsWith('staff-')) {
        // For staff items like "staff-abc123-def456-medium", extract "staff-abc123-def456"
        // The size is always the last part
        itemId = parts.slice(0, -1).join('-');
      } else {
        // For regular items like "item_16044b6e-beab-4df9-8e2a-65946dbe2671-Normale"
        // The size is always the last part, itemId is everything before that
        itemId = parts.slice(0, -1).join('-');
      }

      console.log('🔧 [usePanelState] Parsing:', {
        selectedItemForSupplements,
        extractedItemId: itemId
      });

      // Handle special case for custom pizza (synthetic menu item)
      if (itemId === 'custom_pizza') {
        // Find a pizza category or use the first category
        const pizzaCategory = displayCategories.find(cat =>
          cat.name?.toLowerCase().includes('pizza') || cat.id?.toLowerCase().includes('pizza')
        ) || displayCategories[0];
        if (pizzaCategory) {
          // Get the actual sizes from the category but set prices to 0 (won't be displayed)
          const firstItem = pizzaCategory.items?.[0];
          const actualSizes = firstItem?.prices || { 'Standard': 0 };
          const customPrices = Object.keys(actualSizes).reduce((acc, size) => ({ ...acc, [size]: 0 }), {});

          const fakeMenuItem: MenuItem = {
            id: 'custom_pizza',
            name: 'Personnalisé',
            description: 'Créez votre pizza personnalisée',
            prices: customPrices, // Use actual sizes but with 0 prices (won't be displayed)
            color: '#f97316' // Orange color for custom pizza
          };
          setError(null);
          return { itemId, category: pizzaCategory, item: fakeMenuItem };
        }
      }

      // Handle special case for custom pizza editor
      if (itemId === 'custom_pizza_editor') {
        // Find a pizza category or use the first category
        const pizzaCategory = displayCategories.find(cat =>
          cat.name?.toLowerCase().includes('pizza') || cat.id?.toLowerCase().includes('pizza')
        ) || displayCategories[0];
        if (pizzaCategory) {
          const fakeMenuItem: MenuItem = {
            id: 'custom_pizza_editor',
            name: 'Pizza Personnalisée',
            prices: { 'Standard': 0, 'Grande': 0, 'Géante': 0 },
            color: pizzaCategory.color || '#e0e0e0'
          };
          setError(null);
          return { itemId, category: pizzaCategory, item: fakeMenuItem };
        }
      }

      // Find category and item for regular items
      const category = displayCategories.find((c: any) =>
        c.items.some((item: MenuItem) => item.id === itemId)
      );
      const item = category?.items.find((item: MenuItem) =>
        item.id === itemId
      );

      if (!item || !category) {
        console.log('🔧 [usePanelState] Available items:', displayCategories.map(c => ({
          categoryId: c.id,
          categoryName: c.name,
          items: (c.items || []).map((i: MenuItem) => ({ id: i.id, name: i.name }))
        })));
        setError(`Item or category not found for: ${selectedItemForSupplements} (parsed itemId: ${itemId})`);
        return null;
      }

      setError(null);
      return { itemId, category, item };
    } catch (err) {
      setError(`Error parsing panel data: ${err}`);
      return null;
    }
  }, [selectedItemForSupplements, displayCategories]);

  return { parsedData, error };
};

// CustomizationModal component removed - now using inline rendering

// Add extended OrderItem type to include the combined item IDs
interface CombinedOrderItem extends OrderItem {
  originalId?: string;
  allIds?: string[];
}

// OrderSummary Component
interface OrderSummaryProps {
  items: OrderItem[];
  total: number;
  onIncrement: (itemId: string) => void;
  onDecrement: (itemId: string) => void;
  onRemove: (itemId: string) => void;
  selectedItemForSupplements: string | null;
  selectedItemSizes: { [key: string]: string };
  setSelectedItemForSupplements: (itemId: string | null) => void;
  setSelectedItemSizes: (updater: (prev: { [key: string]: string }) => { [key: string]: string }) => void;
  setSelectedSupplements: (updater: (prev: { [key: string]: Set<string> }) => { [key: string]: Set<string> }) => void;
  setItemNotes: (updater: (prev: { [key: string]: string }) => { [key: string]: string }) => void;
  getSupplementKey: (itemId: string, size: string) => string;
  getItemNoteKey: (itemId: string, size: string) => string;
  displayCategories: any[];
  onCustomPizzaClick: (item: any) => void;
}

const OrderSummary: React.FC<OrderSummaryProps> = React.memo(({
  items,
  total,
  onIncrement,
  onDecrement,
  onRemove,
  selectedItemForSupplements,
  selectedItemSizes,
  setSelectedItemForSupplements,
  setSelectedItemSizes,
  setSelectedSupplements,
  setItemNotes,
  getSupplementKey,
  getItemNoteKey,
  displayCategories,
  onCustomPizzaClick
}) => {
  // Track which groups are collapsed - store in state that persists across renders
  const [collapsedGroups, setCollapsedGroups] = useState<{ [key: string]: boolean }>({});

  // Toggle collapsed state for a group
  const toggleGroupCollapse = useCallback((groupId: string) => {
    setCollapsedGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  }, []);

  // Function to get category ID for menu items - needed for grouping
  const getCategoryIdForItem = useCallback((itemId: string, orderItem?: any): string => {
    // First priority: use the categoryId stored in the order item (most reliable)
    if (orderItem?.categoryId) {
      return orderItem.categoryId;
    }

    // Handle custom pizzas - they should use their stored categoryId
    if (itemId === 'custom_pizza') {
      return orderItem?.categoryId || 'unknown';
    }

    // Fallback: Find the category that contains this item
    const category = displayCategories.find(cat =>
      cat.items && cat.items.some((item: MenuItem) => item.id === itemId)
    );
    return category?.id || 'unknown';
  }, [displayCategories]);

  // Create a map to combine identical items (same item, size, addons, notes)
  const groupedItems = useMemo(() => {
    // First combine items with identical properties
    const combinedItemsMap: { [key: string]: OrderItem } = {};
    const combinedItemIds: { [key: string]: string[] } = {}; // Track all IDs for items with same signature

    items.forEach(item => {
      // Debug: Log item details to see if addons and notes are present
      console.log('🔍 [OrderSummary] Processing item:', {
        name: item.name,
        addons: item.addons,
        notes: item.notes,
        hasAddons: item.addons && item.addons.length > 0,
        hasNotes: !!item.notes
      });

      const itemSignature = getItemSignature(item);

      if (combinedItemsMap[itemSignature]) {
        // Add quantity to existing item
        combinedItemsMap[itemSignature].quantity += item.quantity;
        // Track the ID for later removal/increment operations
        combinedItemIds[itemSignature].push(item.id);
      } else {
        // Create new entry
        combinedItemsMap[itemSignature] = { ...item };
        combinedItemIds[itemSignature] = [item.id];
      }
    });

    // Create properly combined items with preserved first ID
    const combinedItems = Object.entries(combinedItemsMap).map(([signature, item]) => {
      return {
        ...item,
        // We'll keep using the first ID for UI operations
        originalId: item.id,
        allIds: combinedItemIds[signature]
      } as CombinedOrderItem;
    });

    // 🎯 Group by category instead of item name
    const categoryGroups: { [key: string]: CombinedOrderItem[] } = {};
    combinedItems.forEach(item => {
      const categoryId = getCategoryIdForItem(item.menuItemId, item);
      const category = displayCategories.find(cat => cat.id === categoryId);
      const groupKey = category?.name || 'Unknown Category'; // Group by category name
      if (!categoryGroups[groupKey]) categoryGroups[groupKey] = [];
      categoryGroups[groupKey].push(item);
    });

    // Sort items within each category group by item name first, then size, then addons, then notes
    Object.values(categoryGroups).forEach(groupItems => {
      groupItems.sort((a, b) => {
        // First sort by item name
        if (a.name !== b.name) {
          return a.name.localeCompare(b.name);
        }
        // Then by size
        if (a.size !== b.size) {
          if (a.size === undefined) return -1;
          if (b.size === undefined) return 1;
          return a.size.localeCompare(b.size);
        }
        // Then by number of addons
        if ((a.addons || []).length !== (b.addons || []).length)
          return (a.addons || []).length - (b.addons || []).length;

        // Then by addon names
        const aAddonNames = (a.addons || []).map(addon => addon.name).sort().join(',');
        const bAddonNames = (b.addons || []).map(addon => addon.name).sort().join(',');
        if (aAddonNames !== bAddonNames) return aAddonNames.localeCompare(bAddonNames);

        // Finally by notes
        return (a.notes || '').localeCompare(b.notes || '');
      });
    });

    return categoryGroups;
  }, [items, getCategoryIdForItem, displayCategories]);

  // Memoize handler for selecting an item for customization
  const handleSelectItem = useCallback((menuItem: MenuItem, size: string, supplements: OrderAddon[], notes: string) => {
    if (menuItem) {
      setSelectedItemForSupplements(`${menuItem.id}-${size || 'default'}`);
      setSelectedItemSizes(prev => ({
        ...prev,
        [menuItem.id]: size || 'default'
      }));

      const supplementKey = getSupplementKey(menuItem.id, size || 'default');
      const supplementSet = new Set(supplements.map(supplement => supplement.id));
      setSelectedSupplements(prev => ({
        ...prev,
        [supplementKey]: supplementSet
      }));

      const noteKey = getItemNoteKey(menuItem.id, size || 'default');
      setItemNotes(prev => ({
        ...prev,
        [noteKey]: notes || ''
      }));
    }
  }, [
    setSelectedItemForSupplements,
    setSelectedItemSizes,
    setSelectedSupplements,
    setItemNotes,
    getSupplementKey,
    getItemNoteKey
  ]);

  // Calculate total number of items across all groups
  const totalItemCount = useMemo(() => {
    return items.reduce((total, item) => total + item.quantity, 0);
  }, [items]);



  return (
    <div className="h-full flex flex-col">
      {/* Header - Fixed */}
      <div className="px-3 py-2 border-b bg-muted/5 flex items-center justify-between flex-shrink-0">
        <h3 className="font-semibold text-sm">Résumé de la commande</h3>
        <div className="text-sm font-bold bg-primary/10 text-primary px-2 py-1 rounded-full">
          {totalItemCount} article(s)
        </div>
      </div>

      {/* Scrollable Content */}
      <ScrollArea className="flex-1 min-h-0 h-full">

        <div className="p-0 flex flex-col h-full">
          {items.length === 0 ? (
            <div className="flex-grow flex flex-col items-center justify-center py-4 px-2 text-center">
              <div className="w-6 h-6 rounded-full bg-muted/50 flex items-center justify-center mb-1">
                <ShoppingCart className="h-3 w-3 text-muted-foreground" />
              </div>
              <div className="text-muted-foreground text-xs">Aucun article dans la commande</div>
              <div className="text-[10px] text-muted-foreground mt-0.5">Sélectionnez des articles dans le menu pour commencer</div>
            </div>
          ) : (
            <div className="flex-grow flex flex-col">
              {Object.entries(groupedItems).map(([categoryName, itemsInCategory]) => {
                const isCollapsed = collapsedGroups[categoryName] === true;

                return (
                  <div key={categoryName} className="border-b border-gray-100 last:border-b-0">
                    <div
                      className="flex items-center justify-between px-2 py-2 cursor-pointer hover:bg-gray-50 transition-colors"
                      onClick={() => toggleGroupCollapse(categoryName)}
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className={`transition-transform duration-200 ${isCollapsed ? '' : 'rotate-90'}`}
                        >
                          <ChevronRight className="h-4 w-4 text-gray-500" />
                        </div>
                        <div>
                          <div className="font-semibold text-sm truncate">{categoryName}</div>
                          <div className="text-xs text-muted-foreground">
                            {itemsInCategory.reduce((total, item) => total + item.quantity, 0)} article(s)
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="text-sm font-semibold text-right">
                          {itemsInCategory.reduce((total, item) => {
                            const itemBasePrice = item.price * item.quantity;
                            const itemAddonPrice = (item.addons || []).reduce((sum, addon) => sum + (addon.price || 0), 0) * item.quantity;
                            return total + itemBasePrice + itemAddonPrice;
                          }, 0)} DA
                        </div>
                      </div>
                    </div>

                    <div
                      className={`transition-all duration-300 ease-in-out ${isCollapsed ? 'max-h-0 overflow-hidden' : 'max-h-[1000px]'}`}
                    >
                      <div className="divide-y divide-gray-100">
                        {itemsInCategory.map((item: CombinedOrderItem) => {
                          const individualItemCategoryId = getCategoryIdForItem(item.menuItemId, item);
                          const individualItemColor = getItemColor(individualItemCategoryId, item.menuItemId, displayCategories);
                          const isSelectedForEditing = item.menuItemId === 'custom_pizza'
                            ? selectedItemForSupplements === `custom_pizza_editor-${item.size || 'default'}`
                            : selectedItemForSupplements === `${item.menuItemId}-${item.size || 'default'}`;

                          const handleItemClick = () => {
                            // Handle custom pizzas
                            if (item.menuItemId === 'custom_pizza') {
                              onCustomPizzaClick(item);
                              return;
                            }

                            const menuCategory = displayCategories.find((c: any) =>
                              c.items.some((menuItem: MenuItem) => menuItem.id === item.menuItemId)
                            );

                            if (!menuCategory) return;

                            const menuItem = menuCategory.items.find((i: MenuItem) => i.id === item.menuItemId);
                            if (!menuItem) return;

                            setSelectedItemForSupplements(`${item.menuItemId}-${item.size || 'default'}`);
                            setSelectedItemSizes(prev => ({ ...prev, [item.menuItemId]: item.size || "" }));

                            const supplementKey = getSupplementKey(item.menuItemId, item.size || "");
                            setSelectedSupplements(prev => {
                              const newSelectedSupplements = { ...prev };
                              newSelectedSupplements[supplementKey] = new Set(
                                (item.addons || []).map((supplement: OrderAddon) => supplement.id)
                              );
                              return newSelectedSupplements;
                            });

                            if (item.notes) {
                              const noteKey = getItemNoteKey(item.menuItemId, item.size || "");
                              setItemNotes(prev => ({
                                ...prev,
                                [noteKey]: item.notes || ""
                              }));
                            }
                          };

                          return (
                            <div
                              key={item.id || `combined-${item.menuItemId}-${item.size}`}
                              className={cn(
                                "transition-colors",
                                isSelectedForEditing ? "bg-blue-50" : "bg-white"
                              )}
                              style={{
                                borderLeft: `3px solid ${isSelectedForEditing ? '#3b82f6' : individualItemColor}`
                              }}
                            >
                              <div className="flex items-center gap-2 px-2 py-2">
                                <div className="flex-1">
                                  <div
                                    className="flex items-center cursor-pointer"
                                    onClick={handleItemClick}
                                  >
                                    <div
                                      className="font-medium text-sm flex-1 group relative"
                                      title={item.name}
                                    >
                                      <span className="font-semibold line-clamp-2">{item.name}</span>
                                      {item.size && item.size !== 'default' && (
                                        <span className="text-muted-foreground ml-2 font-normal text-sm">
                                          ({item.size})
                                        </span>
                                      )}
                                    </div>
                                    <div className="text-sm font-semibold mr-3">{item.price + (item.addons || []).reduce((sum, addon) => sum + (addon.price || 0), 0)} DA</div>
                                  </div>

                                  {((item.addons?.length || 0) > 0 || item.notes) && (
                                    <div className="text-xs text-muted-foreground mt-1 space-y-1 pl-1">
                                      {item.addons && item.addons.length > 0 && (
                                        <div className="flex flex-wrap gap-x-2 gap-y-1">
                                          {item.addons.map((addon: OrderAddon) => (
                                            <div
                                              key={addon.id}
                                              className="inline-flex items-center gap-1.5 px-2 py-1 rounded bg-gray-100 text-gray-700"
                                            >
                                              <span className="text-xs">{addon.name}</span>
                                              {addon.price > 0 && (
                                                <span className="font-semibold text-xs">+{addon.price}</span>
                                              )}
                                            </div>
                                          ))}
                                        </div>
                                      )}
                                      {item.notes && <div className="italic text-gray-600 pt-1.5">{item.notes}</div>}
                                    </div>
                                  )}
                                </div>

                                <div className="flex items-center gap-2">
                                  <Button
                                    variant="outline"
                                    size="icon"
                                    className="w-8 h-8"
                                    onClick={() => onDecrement(item.allIds?.[0] || item.id)}
                                  >
                                    <Minus className="h-4 w-4" />
                                  </Button>
                                  <div
                                    className="w-8 h-8 rounded-md flex items-center justify-center text-base font-semibold bg-gray-100"
                                  >
                                    {item.quantity}
                                  </div>
                                  <Button
                                    variant="outline"
                                    size="icon"
                                    className="w-8 h-8"
                                    onClick={() => onIncrement(item.allIds?.[0] || item.id)}
                                  >
                                    <Plus className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
});

// Complex StaffMenuDetails component removed - will be replaced with clean StaffMenuManager integration

// OrderDetails Component for order type, table selection, and customer info
interface OrderDetailsProps {
  orderType: OrderType;
  tableId: string;
  tables: any[];
  tableStatuses: any[];
  customer: { name: string; phone: string; address?: string } | undefined;
  deliveryPerson: DeliveryPerson | undefined;
  notes: string;
  onOrderTypeChange: (orderType: OrderType) => void;
  onTableChange: (tableId: string) => void;
  onNotesChange: (notes: string) => void;
  onCustomerInfoChange: (field: string, value: string) => void;
}

const OrderDetails: React.FC<OrderDetailsProps> = React.memo(({
  orderType,
  tableId,
  tables,
  tableStatuses,
  customer = { name: "", phone: "" },
  notes,
  onOrderTypeChange,
  onTableChange,
  onNotesChange,
  onCustomerInfoChange,
}) => {
  // Memoize handlers
  const handleNotesChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onNotesChange(e.target.value);
  }, [onNotesChange]);

  const handleCustomerChange = useCallback((field: string, e: React.ChangeEvent<HTMLInputElement>) => {
    onCustomerInfoChange(field, e.target.value);
  }, [onCustomerInfoChange]);

  // Memoize table options to prevent unnecessary recalculations
  const tableOptions = useMemo(() => {
    return tables.map((table) => {
      const tableStatus = tableStatuses.find(t => t.id === table.id);
      const isOccupied = tableStatus?.status === 'occupied';
      const orderCount = tableStatus?.orderCount || 0;
      return {
        id: table.id,
        name: table.name || `Table ${table.seats}`,
        seats: table.seats,
        isOccupied,
        orderCount,
        occupiedSince: tableStatus?.occupiedSince,
        disabled: false // Allow selection of occupied tables for multiple orders
      };
    });
  }, [tables, tableStatuses, tableId]);

  return (
    <div className="flex-shrink-0 border-b bg-muted/10 p-1">
      <div className="flex gap-1 w-full mb-1">
        <Button
          variant={orderType === 'dine-in' ? 'default' : 'outline'}
          size="sm"
          className="w-full gap-1 rounded-md h-8 py-0"
          onClick={() => onOrderTypeChange('dine-in')}
        >
          <Utensils className="h-3 w-3 mr-1" />
          <span className="text-xs font-medium">{getOrderTypeLabel('dine-in')}</span>
        </Button>
        <Button
          variant={orderType === 'takeaway' ? 'default' : 'outline'}
          size="sm"
          className="w-full gap-1 rounded-md h-8 py-0"
          onClick={() => onOrderTypeChange('takeaway')}
        >
          <Package className="h-3 w-3 mr-1" />
          <span className="text-xs font-medium">{getOrderTypeLabel('takeaway')}</span>
        </Button>
        <Button
          variant={orderType === 'delivery' ? 'default' : 'outline'}
          size="sm"
          className="w-full gap-1 rounded-md h-8 py-0"
          onClick={() => onOrderTypeChange('delivery')}
        >
          <Truck className="h-3 w-3 mr-1" />
          <span className="text-xs font-medium">{getOrderTypeLabel('delivery')}</span>
        </Button>
      </div>
      <div className="bg-background rounded-md border p-1">
        {requiresTable(orderType) && (
          <div>
            <Select
              value={tableId}
              onValueChange={onTableChange}
              aria-label="Select Table"
            >
              <SelectTrigger id="table" className="h-7 text-xs">
                <SelectValue placeholder="Sélectionner une table" />
              </SelectTrigger>
              <SelectContent>
                {tableOptions.map((table) => (
                  <SelectItem key={table.id} value={table.id} disabled={table.disabled}>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${table.isOccupied ? 'bg-amber-500' : 'bg-green-500'}`}></div>
                      <span className="font-medium">{table.name}</span>
                      {table.isOccupied && table.orderCount > 0 && (
                        <Badge variant="secondary" className="text-xs bg-amber-100 text-amber-700 px-1.5 py-0">
                          {table.orderCount}
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Table Selection Helper */}

          </div>
        )}

        {requiresCustomerInfo(orderType) && (
          <div className="space-y-1.5">
            <div className="grid grid-cols-2 gap-1.5">
              <Input
                placeholder="Nom du client"
                className="h-7 text-sm"
                value={customer?.name || ''}
                onChange={(e) => handleCustomerChange('name', e)}
              />
              <Input
                placeholder={orderType === 'delivery' ? "Téléphone *" : "Téléphone"}
                className="h-7 text-sm"
                value={customer?.phone || ''}
                onChange={(e) => handleCustomerChange('phone', e)}
              />
            </div>

            {orderType === 'delivery' && (
              <>
                <Input
                  placeholder="Adresse de livraison *"
                  className="h-7 text-sm"
                  value={customer?.address || ''}
                  onChange={(e) => handleCustomerChange('address', e)}
                />
              </>
            )}
          </div>
        )}

        <div className="mt-1">
          <Textarea
            placeholder="Notes pour la commande..."
            className="resize-none h-[32px] text-xs"
            value={notes}
            onChange={handleNotesChange}
          />
        </div>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Only re-render when these specific props change
  return (
    prevProps.orderType === nextProps.orderType &&
    prevProps.tableId === nextProps.tableId &&
    prevProps.notes === nextProps.notes &&
    prevProps.customer?.name === nextProps.customer?.name &&
    prevProps.customer?.phone === nextProps.customer?.phone &&
    prevProps.customer?.address === nextProps.customer?.address &&
    prevProps.deliveryPerson?.name === nextProps.deliveryPerson?.name &&
    prevProps.deliveryPerson?.phone === nextProps.deliveryPerson?.phone &&
    prevProps.tables.length === nextProps.tables.length &&
    prevProps.tableStatuses.length === nextProps.tableStatuses.length
  );
});

// Main component
interface OrderingInterfaceProps { }

const NewOrderingInterface: React.FC<OrderingInterfaceProps> = () => {
  // Main hooks
  const { isAuthenticated, user } = useAuth();
  const { categories, isLoading: menuLoading, error: menuError, isReady: menuReady } = useMenuV4();
  const { tables, isLoading: tablesLoading, error: tablesError, isReady: tablesReady } = useTableDB();
  const { orders, isLoading: ordersLoading, error: ordersError, isReady: ordersReady, createOrder, updateOrder, refreshOrders, getOrder } = useOrderV4();
  const {
    config: staffMenuConfig,
    staffMenuItems,
    isLoading: staffMenuLoading,
    error: staffMenuError
  } = useStaffMenuV4();


  
  const { editOrder, isEditMode, editSessionId, clearEditOrder, refreshEditSession } = useEditOrder();

  // Order Hold functionality
  const {
    heldOrders,
    activeHolds,
    isHolding,
    isResuming,
    error: holdError,
    holdCurrentOrder,
    resumeOrder,
    clearHold,
    updateHoldLabel,
    updateHoldPriority
  } = useOrderHold();

  // Core state with reducer
  const [orderState, dispatch] = useReducer(orderReducer, initialOrderState);

  // UI State (for temporary states during item customization)
  const [uiState, setUiState] = useState<UiState>(initialUiState);



  // UI/UX state - SIMPLIFIED LOADING LOGIC
  const isLoading = menuLoading && tablesLoading && ordersLoading; // Only loading if ALL are loading
  const error = menuError || tablesError || ordersError;
  const isReady = (menuReady || (categories.length > 0 && tables.length >= 0)) && tablesReady; // More flexible ready state
  const [tableStatuses, setTableStatuses] = useState<{ id: string; status: 'free' | 'occupied'; occupiedSince: string | null; currentOrderId: string | null; }[]>([]);
  const [isOperationLoading, setIsOperationLoading] = useState(false);
  const [localCategories, setLocalCategories] = useState<any[]>([]);
  // isEditMode now comes from useEditOrder context
  
  // Hold system UI state
  const [showHoldQueue, setShowHoldQueue] = useState(false);
  
  const [customPizzaEditData, setCustomPizzaEditData] = useState<{
    originalItemId: string;
    quarters: PizzaQuarter[];
    size: string;
    notes: string;
  } | null>(null);
  
  
  const [loadingTimeout, setLoadingTimeout] = useState(false);
  const staffMenuRef = useRef<any>(null);
  const [isStaffMenuReady, setIsStaffMenuReady] = useState(false);

  // State for PaymentForm
  const [payOrder, setPayOrder] = useState<Order | null>(null);
  const [paymentErrorMsg, setPaymentErrorMsg] = useState("");
  const [shouldPrintToKitchen, setShouldPrintToKitchen] = useState(true);
  const { processOrderPayment } = useSimplifiedOrderFinance();

  // Memoize the displayCategories value to prevent unnecessary recalculations - MOVED UP EARLY
  const displayCategories = useMemo(() => {
    const baseCategories = localCategories.length > 0 ? localCategories : categories;

    // 🔍 DEBUG: Log staff menu values
    console.log('🔍 [Staff Menu Debug]', {
      staffMenuConfigIsEnabled: staffMenuConfig?.isEnabled,
      staffMenuItemsLength: staffMenuItems.length,
      staffMenuItems: staffMenuItems,
      userRole: user?.role,
      allConditionsMet: staffMenuConfig?.isEnabled && staffMenuItems.length > 0
    });

    // Add staff menu category if configured and user is authenticated staff  
    if (staffMenuConfig?.isEnabled && staffMenuItems.length > 0) {

      const staffCategory = {
        id: 'staff-menu',
        name: 'Équipe',
        emoji: '👥',
        color: '#10b981', // Green color for staff
        items: staffMenuItems
          .filter(staffItem => staffItem.isActive !== false)
          .map(staffItem => ({
            id: `staff-${staffItem.id}`,
            name: staffItem.itemName,
            prices: { default: staffItem.staffPrice },
            isStaffItem: true,
            originalMenuItemId: staffItem.menuItemId,
            staffItemId: staffItem.id,
            categoryName: staffItem.categoryName,
            size: staffItem.size,
            originalPrice: staffItem.originalPrice
          })),
        isStaffCategory: true
      };

      return [...baseCategories, staffCategory];
    }

    return baseCategories;
  }, [localCategories, categories, staffMenuConfig, staffMenuItems, user]);

  // Add loading timeout to show manual refresh option
  useEffect(() => {
    if (isLoading) {
      const timer = setTimeout(() => {
        setLoadingTimeout(true);
      }, 8000); // Show timeout after 8 seconds

      return () => clearTimeout(timer);
    } else {
      setLoadingTimeout(false);
    }
  }, [isLoading]);

  // Reset staff menu ready state when switching away from staff menu
  useEffect(() => {
    if (uiState.selectedCategory !== 'staff-menu') {
      setIsStaffMenuReady(false);
    }
  }, [uiState.selectedCategory]);

  // Get current business day and time info
  const getCurrentBusinessInfo = () => {
    const now = new Date();
    const currentHour = now.getHours();

    // Business day reset at 5 AM (from memory)
    let businessDate;
    if (currentHour < 5) {
      // Before 5 AM = previous calendar day's business
      businessDate = new Date(now);
      businessDate.setDate(businessDate.getDate() - 1);
    } else {
      businessDate = now;
    }

    const businessDateStr = format(businessDate, 'yyyy-MM-dd');
    const dayName = format(businessDate, 'EEEE').toLowerCase();

    return { businessDateStr, dayName, currentTime: format(now, 'HH:mm') };
  };

  // --- UTILITY FUNCTIONS ---
  const getItemNoteKey = useCallback((itemId: string, size: string) => `note-${itemId}-${size}`, []);
  const getSupplementKey = useCallback((itemId: string, size: string) => `${itemId}-${size}`, []);

  const isSupplementSelected = useCallback((itemId: string, size: string, supplementId: string) => {
    const key = getSupplementKey(itemId, size);
    return uiState.selectedSupplements[key]?.has(supplementId) || false;
  }, [uiState.selectedSupplements, getSupplementKey]);

  // --- UI STATE UPDATERS ---
  const setSelectedCategory = useCallback((categoryId: string) => {
    setUiState(prev => ({
      ...prev,
      selectedCategory: categoryId,
      selectedItemForSupplements: null // Clear panel when switching categories
    }));
  }, []);

  const setSelectedItemForSupplements = useCallback((itemId: string | null) => {
    setUiState(prev => ({ ...prev, selectedItemForSupplements: itemId }));
  }, []);

  const setLastAddedItem = useCallback((itemSignature: string | null) => {
    setUiState(prev => ({ ...prev, lastAddedItem: itemSignature }));
    if (itemSignature) {
      // Clear the "just added" highlight after a delay
      setTimeout(() => {
        setUiState(prev => ({ ...prev, lastAddedItem: null }));
      }, 800);
    }
  }, []);

  const initializeFreshItemState = useCallback((itemId: string, size: string, forceClear: boolean = false) => {
    // Update size selection
    setUiState(prev => {
      // Create new references for nested objects to ensure React detects the changes
      const newSupplements = { ...prev.selectedSupplements };
      const supplementKey = getSupplementKey(itemId, size);
      // Only clear if forced or if no existing selections
      if (forceClear || !newSupplements[supplementKey]) {
        newSupplements[supplementKey] = new Set();
      }

      const newNotes = { ...prev.itemNotes };
      const noteKey = getItemNoteKey(itemId, size);
      // Only clear if forced or if no existing notes
      if (forceClear || !newNotes[noteKey]) {
        newNotes[noteKey] = '';
      }

      return {
        ...prev,
        selectedItemSizes: { ...prev.selectedItemSizes, [itemId]: size },
        selectedSupplements: newSupplements,
        itemNotes: newNotes
      };
    });
  }, [getSupplementKey, getItemNoteKey]);

  const toggleSupplementSelection = useCallback((itemId: string, size: string, supplementId: string) => {
    const key = getSupplementKey(itemId, size);
    console.log('🔍 [toggleSupplementSelection] Toggle:', { itemId, size, supplementId, key });

    setUiState(prev => {
      // Create a new reference for the selectedSupplements object
      const newSelections = { ...prev.selectedSupplements };
      if (!newSelections[key]) newSelections[key] = new Set();

      // Create a new Set reference to ensure React detects the change
      const updatedSet = new Set(newSelections[key]);
      const wasSelected = updatedSet.has(supplementId);
      if (wasSelected) {
        updatedSet.delete(supplementId);
      } else {
        updatedSet.add(supplementId);
      }

      newSelections[key] = updatedSet;
      console.log('🔍 [toggleSupplementSelection] Updated selections for key:', key, 'selections:', Array.from(updatedSet));
      return {
        ...prev,
        selectedSupplements: newSelections
      };
    });
  }, [getSupplementKey]);

  const updateItemNote = useCallback((itemId: string, size: string, note: string) => {
    const noteKey = getItemNoteKey(itemId, size);

    setUiState(prev => {
      // Create a new reference for the itemNotes object
      const newNotes = { ...prev.itemNotes };
      newNotes[noteKey] = note;

      return {
        ...prev,
        itemNotes: newNotes
      };
    });
  }, [getItemNoteKey]);

  // Helper function to get supplements for a specific category
  const getCategorySupplements = useCallback(async (categoryId: string) => {
    try {
      const { getAllSupplements } = await import('@/lib/db/v4/operations/supplement-ops');
      return await getAllSupplements(categoryId);
    } catch (error) {
      console.error('Error getting category supplements:', error);
      return [];
    }
  }, []);

  // Helper function to get supplement prices for a specific category
  const getCategorySupplementPrices = useCallback(async (categoryId: string) => {
    try {
      const { getAllSupplementPrices } = await import('@/lib/db/v4/operations/supplement-ops');
      return await getAllSupplementPrices(categoryId);
    } catch (error) {
      console.error('Error getting category supplement prices:', error);
      return {};
    }
  }, []);

  // Memoize handlers for order manipulation
  const handleIncrement = useCallback((itemId: string) => {
    dispatch({ type: 'INCREMENT_QUANTITY', payload: { itemId } });
  }, []);

  const handleDecrement = useCallback((itemId: string) => {
    dispatch({ type: 'DECREMENT_QUANTITY', payload: { itemId } });
  }, []);

  const handleRemoveItem = useCallback((itemId: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: { itemId } });
  }, []);

  // Staff menu handlers removed - will be replaced with clean StaffMenuManager integration

  const handleOrderTypeChange = useCallback((orderType: OrderType) => {
    dispatch({ type: 'SET_ORDER_TYPE', payload: { orderType } });

    // Clear table when switching away from dine-in
    if (orderType !== 'dine-in') {
      dispatch({ type: 'SET_TABLE', payload: { tableId: '' } });
    }

    // Clear customer info when switching to dine-in
    if (orderType === 'dine-in') {
      dispatch({ type: 'SET_CUSTOMER_INFO', payload: { name: '', phone: '', address: '' } });
    }
  }, []);

  // Hold system handlers
  const handleHoldOrder = useCallback(async () => {
    if (!orderState.items?.length) {
      console.warn('[Hold] Cannot hold empty order');
      return;
    }

    try {
      console.log('[Hold] Creating hold for current order...');
      const holdId = await holdCurrentOrder(orderState, uiState, {
        priority: 'normal'
      });
      
      console.log(`[Hold] Order held successfully: ${holdId}`);
      
      // Reset order and UI state
      dispatch({ type: 'RESET_ORDER' });
      setUiState(initialUiState);
      
      // Clear any edit session
      if (isEditMode) {
        clearEditOrder();
      }
      
      // Auto-show hold queue after holding an order
      setShowHoldQueue(true);
      
      // Show success feedback
      if (typeof window !== 'undefined') {
        // You can add toast notification here if available
        console.log('[Hold] Order parked successfully');
      }
      
    } catch (error) {
      console.error('[Hold] Failed to hold order:', error);
      // Show error feedback
      if (typeof window !== 'undefined') {
        // You can add error toast notification here if available
        console.error('[Hold] Failed to park order');
      }
    }
  }, [orderState, uiState, holdCurrentOrder, isEditMode, clearEditOrder]);

  const handleResumeOrder = useCallback(async (holdId: string) => {
    try {
      console.log(`[Hold] Resuming order: ${holdId}`);
      const { orderState: resumedOrderState, uiState: resumedUiState } = await resumeOrder(holdId);
      
      // Validate resumed state
      if (!resumedOrderState || !resumedOrderState.items || resumedOrderState.items.length === 0) {
        throw new Error('Invalid order state in held order');
      }
      
      // Restore order state
      dispatch({ type: 'RESUME_ORDER', payload: resumedOrderState });
      
      // Restore UI state
      setUiState(resumedUiState);
      
      // Hide hold queue
      setShowHoldQueue(false);
      
      console.log(`[Hold] Order resumed successfully: ${holdId}`);
      
      // Show success feedback
      if (typeof window !== 'undefined') {
        console.log('[Hold] Order resumed successfully');
      }
      
    } catch (error) {
      console.error(`[Hold] Failed to resume order ${holdId}:`, error);
      // Show error feedback
      if (typeof window !== 'undefined') {
        console.error('[Hold] Failed to resume order');
      }
    }
  }, [resumeOrder]);

  const handleClearHold = useCallback(async (holdId: string) => {
    // Confirm before clearing
    if (typeof window !== 'undefined') {
      const confirmed = window.confirm('Are you sure you want to clear this held order? This action cannot be undone.');
      if (!confirmed) {
        return;
      }
    }
    
    try {
      console.log(`[Hold] Clearing hold: ${holdId}`);
      await clearHold(holdId);
      console.log(`[Hold] Hold cleared successfully: ${holdId}`);
      
      // Show success feedback
      if (typeof window !== 'undefined') {
        console.log('[Hold] Held order cleared');
      }
      
    } catch (error) {
      console.error(`[Hold] Failed to clear hold ${holdId}:`, error);
      // Show error feedback
      if (typeof window !== 'undefined') {
        console.error('[Hold] Failed to clear held order');
      }
    }
  }, [clearHold]);

  const handleTableChange = useCallback((tableId: string) => {
    dispatch({ type: 'SET_TABLE', payload: { tableId } });
  }, []);

  const handleNotesChange = useCallback((notes: string) => {
    dispatch({ type: 'SET_NOTES', payload: { notes } });
  }, []);

  const handleCustomerInfoChange = useCallback((field: string, value: string) => {
    const customer = { ...(orderState.customer || { name: "", phone: "" }) };
    customer[field as keyof typeof customer] = value;
    dispatch({
      type: 'SET_CUSTOMER_INFO',
      payload: {
        name: customer.name,
        phone: customer.phone,
        address: customer.address
      }
    });
  }, [orderState.customer]);


  // --- ACTION DISPATCHERS ---
  const updateSelectedItem = useCallback(async () => {
    if (!uiState.selectedItemForSupplements) return;

    // Extract item ID from the format "itemId-size"
    // The size is always the last part, itemId is everything before that
    const parts = uiState.selectedItemForSupplements.split('-');
    const itemId = parts.slice(0, -1).join('-');
    const size = uiState.selectedItemSizes[itemId] || 'default';

    // Skip processing for custom pizza items - they should only be handled by the custom pizza flow
    if (itemId === 'custom_pizza' || itemId === 'custom_pizza_editor') {
      return;
    }

    // Find the menu item and its category
    let menuItem: MenuItem | undefined;
    let categoryId: string | undefined;

    for (const category of displayCategories) {
      const foundItem = category.items.find((item: MenuItem) => item.id === itemId);
      if (foundItem) {
        menuItem = foundItem;
        categoryId = category.id;
        break;
      }
    }

    if (!menuItem || !categoryId) return;

    // Get selected supplements for this item+size combination
    const key = getSupplementKey(itemId, size);
    const selectedSupplementIds = Array.from(uiState.selectedSupplements[key] || new Set());

    // Get supplements for this category
    const categorySupplements = await getCategorySupplements(categoryId);
    const supplementPrices = await getCategorySupplementPrices(categoryId);

    // Create supplement objects for the order
    const supplementObjects = await Promise.all(
      selectedSupplementIds.map(async (supplementId: string) => {
        console.log('🔍 [updateSelectedItem] Processing supplementId:', supplementId);
        // Check if this supplement ID corresponds to a supplement
        const supplement = categorySupplements.find(s => s.id === supplementId);
        console.log('🔍 [updateSelectedItem] Found supplement:', supplement ? supplement.name : 'NOT FOUND');
        if (supplement) {
          // Get the correct price for this supplement based on the item's size
          const supplementPrice = supplementPrices[size] || 0;
          const supplementObject = {
            id: supplement.id,
            name: supplement.name,
            price: supplementPrice, // Use category pricing based on item size
            type: 'supplement',
            // Include stock consumption info for backend processing
            stockConsumption: supplement.stockConsumption,
          };
          console.log('🔍 [updateSelectedItem] Created supplement object:', supplementObject);
          return supplementObject;
        }

        console.log('🔍 [updateSelectedItem] Skipping supplementId (not found):', supplementId);
        return null;
      })
    );

    const validSupplementObjects = supplementObjects.filter(Boolean) as OrderAddon[];

    // Get notes for this item+size combination
    const noteKey = getItemNoteKey(itemId, size);
    const itemNote = uiState.itemNotes[noteKey] || '';

    // Find if this item+size already exists in the order
    const existingItem = orderState.items?.find((item: OrderItem) =>
      item.menuItemId === itemId && item.size === size
    );

    // Check if supplements or notes have changed
    const supplementSignature = validSupplementObjects.map((s: any) => s.id).sort().join(',');
    const existingSupplementSignature = (existingItem?.addons || [])
      .map((a: any) => a.id).sort().join(',');

    const hasChanged = supplementSignature !== existingSupplementSignature ||
      existingItem?.notes !== itemNote;

    // Debug: Log update details
    console.log('🔍 [updateSelectedItem] Update check:', {
      itemId,
      size,
      existingItem: !!existingItem,
      selectedSupplementIds,
      categorySupplements: categorySupplements.length,
      supplementPrices,
      supplementSignature,
      existingSupplementSignature,
      itemNote,
      existingNotes: existingItem?.notes,
      hasChanged,
      validSupplementObjects,
      categoryId
    });

    // If the item exists and has changed, update it
    if (existingItem && hasChanged) {
      dispatch({
        type: 'UPDATE_ITEM',
        payload: {
          itemId: existingItem.id,
          updates: {
            size: size,
            price: menuItem.prices[size],
            addons: validSupplementObjects,
            notes: itemNote
          }
        }
      });
    }
  }, [
    uiState.selectedItemForSupplements,
    uiState.selectedItemSizes,
    uiState.selectedSupplements,
    uiState.itemNotes,
    orderState.items,
    localCategories,
    getSupplementKey,
    getItemNoteKey,
    dispatch,
    getCategorySupplements,
    getCategorySupplementPrices
  ]);


  const handleSelectItemForCustomization = useCallback((itemId: string) => {
    // If an item was previously selected, update any changes before selecting the new one
    // Skip update for custom pizza items to prevent duplicate creation
    if (uiState.selectedItemForSupplements) {
      const parts = uiState.selectedItemForSupplements.split('-');
      const currentItemId = parts.slice(0, -1).join('-');
      if (currentItemId !== 'custom_pizza' && currentItemId !== 'custom_pizza_editor') {
        updateSelectedItem();
      }
    }

    // Find the item to get its default size - use displayCategories for consistency
    let item = displayCategories
      .flatMap(category => category.items)
      .find((item: MenuItem) => item.id === itemId);

    if (item) {
      const defaultSize = Object.keys(item.prices)[0] || 'default';
      const currentSize = uiState.selectedItemSizes[itemId] || defaultSize;

      // Set selection with size-specific format
      setSelectedItemForSupplements(`${itemId}-${currentSize}`);

      // Initialize state for the newly selected item if not already set
      if (!uiState.selectedItemSizes[itemId]) {
        initializeFreshItemState(itemId, currentSize);
      }
    }
  }, [
    uiState.selectedItemForSupplements,
    uiState.selectedItemSizes,
    localCategories,
    displayCategories,
    setSelectedItemForSupplements,
    initializeFreshItemState,
    updateSelectedItem
  ]);

  const finalizeCurrentItem = useCallback(() => {
    console.log('🔍 [finalizeCurrentItem] Called with selectedItemForSupplements:', uiState.selectedItemForSupplements);
    // Only update if it's not a custom pizza item
    if (uiState.selectedItemForSupplements) {
      const parts = uiState.selectedItemForSupplements.split('-');
      const itemId = parts.slice(0, -1).join('-');
      console.log('🔍 [finalizeCurrentItem] Parsed itemId:', itemId);
      if (itemId !== 'custom_pizza' && itemId !== 'custom_pizza_editor') {
        console.log('🔍 [finalizeCurrentItem] Calling updateSelectedItem');
        updateSelectedItem();
      }
    }
    setSelectedItemForSupplements(null);
  }, [setSelectedItemForSupplements, updateSelectedItem, uiState.selectedItemForSupplements]);

  // Handle adding menu items to order
  const handleAddItem = useCallback(async (item: MenuItem, size: string) => {
    console.log('🍕 [handleAddItem] Called with:', { itemId: item.id, itemName: item.name, size });

    // Handle custom pizza selection - Add placeholder item and open customization
    if (item.id === 'custom_pizza') {
      console.log('🍕 [handleAddItem] Custom pizza detected');

      // Find the actual pizza category (not the currently selected category)
      let pizzaCategory = displayCategories.find(cat =>
        cat.name?.toLowerCase().includes('pizza') || cat.id?.toLowerCase().includes('pizza')
      ) || displayCategories[0];

      // Create a placeholder custom pizza item with proper category
      // Get a reasonable default price from the first item in the category
      const firstItem = pizzaCategory.items?.[0];
      const defaultPrice = firstItem?.prices?.[size] || firstItem?.prices?.[Object.keys(firstItem?.prices || {})[0]] || 0;

      const placeholderItem: MenuItem = {
        id: 'custom_pizza',
        name: 'Pizza Personnalisée',
        prices: { [size]: defaultPrice },
        color: '#f97316'
      };

      // Add the placeholder item to cart
      dispatch({
        type: 'ADD_ITEM',
        payload: {
          item: placeholderItem,
          size,
          addons: [],
          notes: '',
          categoryId: pizzaCategory.id,
          categoryName: pizzaCategory.name
        }
      });

      // Set selected size and open customization panel
      setUiState(prev => ({
        ...prev,
        selectedItemSizes: { ...prev.selectedItemSizes, [item.id]: size }
      }));
      setSelectedItemForSupplements(`${item.id}-${size}`);

      // Initialize fresh state for the custom pizza item (force clear for custom pizza)
      initializeFreshItemState(item.id, size, true);

      console.log('🍕 [handleAddItem] Custom pizza added to cart and customization opened');
      return;
    }

    // Staff menu mode - simplified integration
    if (uiState.selectedCategory === 'staff-menu') {
      // Find the corresponding staff menu item
      const staffMenuItem = staffMenuItems.find(menuItem => {
        const menuItemIdMatch = menuItem.menuItemId === item.id;
        const nameMatch = menuItem.itemName.toLowerCase().trim() === item.name.toLowerCase().trim();
        return menuItemIdMatch || nameMatch;
      });

      // If no specific staff menu item found, create a temporary one from the regular menu item
      let itemToAdd = staffMenuItem;
      if (!staffMenuItem) {
        // Create a temporary staff menu item from the regular menu item
        const price = item.prices[size] || Object.values(item.prices)[0] || 0;
        itemToAdd = {
          id: `temp-${item.id}-${size}`,
          menuItemId: item.id,
          itemName: item.name,
          categoryName: uiState.selectedCategory,
          size: size,
          originalPrice: price,
          staffPrice: Math.floor(price * 0.8), // 20% discount for staff
          isActive: true
        };


      }

      // Add item to selected staff via StaffMenuManager
      if (staffMenuRef.current?.addItemToSelectedStaff && isStaffMenuReady) {
        staffMenuRef.current.addItemToSelectedStaff(itemToAdd);
      } else {
        // Retry after a short delay if component is still mounting
        setTimeout(() => {
          if (staffMenuRef.current?.addItemToSelectedStaff) {
            staffMenuRef.current.addItemToSelectedStaff(itemToAdd);
          }
        }, 50);
      }
      return;
    }

    // 🎯 CONSISTENT FIX: Always find category by item search first, never rely on UI state
    let categoryName: string = 'Unknown';
    let categoryId: string = '';

    // Primary method: Search through all categories to find which one contains this item
    // Use displayCategories instead of localCategories to avoid timing issues
    for (const category of displayCategories) {
      const foundItem = category.items.find((menuItem: MenuItem) => menuItem.id === item.id);
      if (foundItem) {
        categoryName = category.name;
        categoryId = category.id;
        break;
      }
    }

    // Only use UI state as fallback if item search fails completely
    if (categoryName === 'Unknown') {
      const selectedCategory = displayCategories.find(cat => cat.id === uiState.selectedCategory);
      if (selectedCategory) {
        categoryName = selectedCategory.name;
        categoryId = selectedCategory.id;
      }
    }

    console.log('🎯 [handleAddItem] Item:', item.name, 'Found categoryName:', categoryName, 'categoryId:', categoryId);

    const selectedSupplements = uiState.selectedSupplements[getSupplementKey(item.id, size)] || new Set();
    const itemNotes = uiState.itemNotes[getItemNoteKey(item.id, size)] || '';

    // Get supplements for the actual category
    const categorySupplements = await getCategorySupplements(categoryId);
    const supplementPrices = await getCategorySupplementPrices(categoryId);

    // Create supplement objects for the order
    const supplementObjects = await Promise.all(
      Array.from(selectedSupplements).map(async (supplementId: string) => {
        // Check if this supplement ID corresponds to a supplement
        const supplement = categorySupplements.find(s => s.id === supplementId);
        if (supplement) {
          // Get the correct price for this supplement based on the item's size
          const supplementPrice = supplementPrices[size] || 0;
          return {
            id: supplement.id,
            name: supplement.name,
            price: supplementPrice,
            type: 'supplement' as const,
            stockConsumption: supplement.stockConsumption,
          };
        }

        // If no match found, skip this supplement
        return null;
      })
    );

    // Filter out null values and type as OrderAddon[]
    const validSupplements = supplementObjects.filter(Boolean) as OrderAddon[];

    dispatch({
      type: 'ADD_ITEM',
      payload: {
        item,
        size,
        addons: validSupplements,
        notes: itemNotes,
        categoryId: categoryId,
        categoryName: categoryName
      }
    });

    // Create signature for this specific item+size+supplements combination
    const signature = `${item.id}-${size}-${Array.from(selectedSupplements).sort().join(',')}-${itemNotes}`;
    setLastAddedItem(signature);

    // Preserve existing supplement selections for this item when opening customization
    const supplementKey = getSupplementKey(item.id, size);
    const noteKey = getItemNoteKey(item.id, size);

    setUiState(prev => ({
      ...prev,
      selectedItemSizes: { ...prev.selectedItemSizes, [item.id]: size },
      // Only initialize supplement selections if they don't exist, otherwise preserve them
      selectedSupplements: {
        ...prev.selectedSupplements,
        [supplementKey]: prev.selectedSupplements[supplementKey] || new Set()
      },
      // Only initialize notes if they don't exist, otherwise preserve them
      itemNotes: {
        ...prev.itemNotes,
        [noteKey]: prev.itemNotes[noteKey] || ''
      }
    }));

    // 🎯 Open customization panel after adding item to allow supplement/notes customization
    console.log('🔧 [handleAddItem] Setting selectedItemForSupplements:', `${item.id}-${size}`);
    setSelectedItemForSupplements(`${item.id}-${size}`);
  }, [uiState.selectedSupplements, uiState.itemNotes, uiState.selectedCategory, getSupplementKey, getItemNoteKey, getCategorySupplements, getCategorySupplementPrices, setLastAddedItem, dispatch, setUiState, staffMenuItems, displayCategories]);


  // 🍕 Handler for inline pizza customizer within addon panel
  const handleInlineCustomPizzaConfirm = useCallback((
    quarters: PizzaQuarter[],
    size: string,
    notes: string,
    categoryId: string,
    pricingMethod: 'max' | 'average' | 'fixed',
    fixedPrice?: number
  ) => {
    // Find the existing placeholder custom pizza item
    const existingCustomPizza = orderState.items.find(item =>
      item.menuItemId === 'custom_pizza' && item.size === size
    );

    if (existingCustomPizza) {
      // Calculate price based on pricing method
      let price = 0;
      if (pricingMethod === 'max') {
        price = Math.max(...quarters.map(q => q.price));
      } else {
        price = quarters.reduce((sum, q) => sum + q.price, 0) / quarters.length;
      }

      // Update the existing item with customization details
      dispatch({
        type: 'UPDATE_ITEM',
        payload: {
          itemId: existingCustomPizza.id,
          updates: {
            name: `Pizza Personnalisée (${quarters.map(q => q.name).join(', ')})`.length > 50
              ? `Pizza Personnalisée (${quarters.length} types)`
              : `Pizza Personnalisée (${quarters.map(q => q.name).join(', ')})`,
            price: price,
            notes: notes,
            compositeType: 'pizza_quarters',
            quarters: quarters
          }
        }
      });

      
    } else {
      // Fallback: add new custom pizza if no placeholder found
      dispatch({
        type: 'ADD_CUSTOM_PIZZA',
        payload: {
          quarters,
          size,
          notes,
          categoryId,
          pricingMethod,
          fixedPrice
        }
      });

      
    }

    // Close the supplement panel and reset state
    setSelectedItemForSupplements(null);
  }, [dispatch, setSelectedItemForSupplements, orderState.items]);

  // Helper function to validate customer info based on order type
  const validateCustomerInfo = useCallback((orderType: OrderType, customer: any) => {
    // For delivery orders: phone and address are required, name is optional
    if (orderType === 'delivery') {
      return customer?.phone && customer?.address;
    }
    // For takeaway orders: all customer info is optional
    if (orderType === 'takeaway') {
      return true; // No required fields
    }
    // For dine-in orders: no customer info required
    return true;
  }, []);

  const handlePlaceOrder = useCallback(async (printToKitchen: boolean = true) => {
    // Validate order
    if (!orderState.items?.length) return;
    if (requiresTable(orderState.orderType) && !orderState.tableId) return;
    if (!validateCustomerInfo(orderState.orderType, orderState.customer)) return;

    try {
      setIsOperationLoading(true);

      // Regular order handling
      const combinedItemsMap: { [key: string]: OrderItem } = {};
      orderState.items.forEach((item: OrderItem) => {
        const itemSignature = getItemSignature(item);
        if (combinedItemsMap[itemSignature]) {
          combinedItemsMap[itemSignature].quantity += item.quantity;
        } else {
          combinedItemsMap[itemSignature] = { ...item };
        }
      });

      const combinedItems = Object.values(combinedItemsMap);

      // Prepare order data - different handling for edit vs create
      let orderData: any;

      if (isEditMode && editOrder) {
        // For editing, prepare update data
        orderData = {
          tableId: orderState.orderType === 'dine-in' ? orderState.tableId : '',
          orderType: orderState.orderType,
          items: combinedItems.map((item: OrderItem) => ({ ...item, notes: item.notes || '' })),
          total: calculateTotal(combinedItems),
          notes: orderState.notes,
          customer: requiresCustomerInfo(orderState.orderType)
            ? orderState.customer
            : undefined,
          deliveryPerson: orderState.orderType === 'delivery'
            ? orderState.deliveryPerson
            : undefined,
          // Keep existing payment status and other fields when editing
          paymentStatus: editOrder.paymentStatus || 'unpaid'
        };
      } else {
        // For new orders, create complete order object
        orderData = {
          tableId: orderState.orderType === 'dine-in' ? orderState.tableId : '',
          orderType: orderState.orderType,
          status: 'pending',
          items: combinedItems.map((item: OrderItem) => ({ ...item, notes: item.notes || '' })),
          total: calculateTotal(combinedItems),
          notes: orderState.notes,
          customer: requiresCustomerInfo(orderState.orderType)
            ? orderState.customer
            : undefined,
          deliveryPerson: orderState.orderType === 'delivery'
            ? orderState.deliveryPerson
            : undefined,
          paymentStatus: 'unpaid',
          createdBy: user?.name || (user as any)?.username || 'unknown',
          createdByName: user?.name || 'Personnel Inconnu'
        };
      }

      // First create the order in the database to get a real ID
      let result: any;
      if (isEditMode && editOrder) {
        result = await updateOrder(editOrder.id || editOrder._id, orderData);
      } else {
        result = await createOrder(orderData);
      }

      // Set the order to be paid in the PaymentForm using the real ID
      setShouldPrintToKitchen(printToKitchen);
      setPayOrder({
        ...orderData,
        _id: result?._id || result?.id || `order:${Date.now()}-temp`,
        id: result?.id || result?._id || `order:${Date.now()}-temp`,
        type: 'order_document',
        schemaVersion: 'v4.0',
        createdAt: result?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error preparing order for payment:', error);
      setPaymentErrorMsg(error instanceof Error ? error.message : "Failed to prepare order for payment");
    } finally {
      setIsOperationLoading(false);
    }
  }, [
    orderState,
    isEditMode,
    editOrder,
    user,
    setPayOrder,
    setPaymentErrorMsg,
    setShouldPrintToKitchen,
    updateOrder,
    createOrder
  ]);

  // Handle confirming the order after payment form submission
  const handlePaymentFormSubmit = useCallback(async (
    receivedAmount: number,
    wasteData?: WasteProcessingData,
    discountData?: DiscountData
  ) => {
    if (!payOrder) return;

    try {
      setIsOperationLoading(true);
      setPaymentErrorMsg("");

      // 🚀 Process waste BEFORE payment if there's waste data (reusing PaymentForm logic)
      if (wasteData) {
        console.log('Processing waste before payment:', wasteData);

        // Import the waste processing functions
        const { processMenuItemWaste, processWastedMenuItemStockConsumption } = await import('@/lib/db/v4/operations/inventory-ops');
        const { updateOrder: updateOrderOps } = await import('@/lib/db/v4/operations/order-ops');

        // Process menu item waste (what shows in UI)
        await processMenuItemWaste(
          payOrder.id || payOrder._id,
          wasteData.wastedMenuItems,
          wasteData.wasteReason,
          wasteData.totalWasteValue
        );

        // Process actual stock consumption behind the scenes (including supplements!)
        await processWastedMenuItemStockConsumption(
          wasteData.wasteItems,
          payOrder.items.map(item => ({
            menuItemId: item.menuItemId,
            name: item.name,
            quantity: item.quantity,
            size: item.size,
            addons: item.addons
          }))
        );

        // Update order to remove wasted items and adjust total
        const updatedItems = payOrder.items.map((item, index) => {
          const wasteItem = wasteData.wasteItems.find(w => w.itemIndex === index);
          if (wasteItem) {
            const remainingQuantity = item.quantity - wasteItem.quantity;
            if (remainingQuantity > 0) {
              return { ...item, quantity: remainingQuantity };
            } else {
              return null;
            }
          }
          return item;
        }).filter(item => item !== null);

        const newTotal = updatedItems.reduce((total, item) => {
          let itemPrice = item.price;
          if (item.addons && item.addons.length > 0) {
            itemPrice += item.addons.reduce((sum, addon) => sum + addon.price, 0);
          }
          return total + (itemPrice * item.quantity);
        }, 0);

        // Update the order in the database with new items and total
        await updateOrderOps(payOrder.id || payOrder._id, {
          items: updatedItems,
          total: newTotal
        });

        // Update the local payOrder object for payment processing
        payOrder.items = updatedItems;
        payOrder.total = newTotal;
      }

      // 💸 NEW: Process discount if provided
      if (discountData) {
        console.log('Processing discount:', discountData);

        // Update order with discount information
        await updateOrder(payOrder.id || payOrder._id, {
          discountType: discountData.discountType,
          discountValue: discountData.discountValue,
          discountAmount: discountData.discountAmount,
          discountReason: discountData.discountReason,
          subtotal: payOrder.total, // Current total becomes subtotal
          total: payOrder.total - discountData.discountAmount // Apply discount to total
        });

        // Update the local order object for payment processing
        payOrder.total = payOrder.total - discountData.discountAmount;
        payOrder.discountType = discountData.discountType;
        payOrder.discountValue = discountData.discountValue;
        payOrder.discountAmount = discountData.discountAmount;
        payOrder.discountReason = discountData.discountReason;
        payOrder.subtotal = discountData.subtotal;
      }

      // Process payment with potentially updated order using finance service
      console.log('🔧 [NewOrderingInterface] Calling finance service processOrderPayment...');
      const paymentResult = await processOrderPayment(payOrder, 'cash', receivedAmount);
      console.log('🔧 [NewOrderingInterface] Payment result:', paymentResult);

      if (!paymentResult.success) {
        throw new Error(paymentResult.error || 'Payment failed');
      }

      // After successful payment, update the order status (order was already created in handlePayOrder)
      console.log('🔄 [NewOrderingInterface] Updating order status after payment:', payOrder.id || payOrder._id);
      const result = await updateOrder(payOrder.id || payOrder._id, { 
        paymentStatus: 'paid', 
        status: 'pending',
        updatedAt: new Date().toISOString()
      });
      console.log('✅ [NewOrderingInterface] Order status updated successfully:', result);

      // Print to kitchen after order is placed/updated - conditional printing
      if (shouldPrintToKitchen) {
        try {
          console.log('🖨️ [NewOrderingInterface] Starting kitchen print process...');
          const orderForPrint: Order = {
            ...payOrder,
            _id: result?._id || result?.id || payOrder._id || '',
            id: result?.id || result?._id || payOrder.id || '',
            type: 'order_document',
            schemaVersion: 'v4.0',
            createdAt: result?.createdAt || new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };

          if (isEditMode && editOrder) {
            const printResult = await kitchenPrintService.printKitchenUpdate(
              orderForPrint,
              'items_changed'
            );
            console.log('🖨️ Kitchen modification print result:', printResult);
          } else {
            const printResult = await kitchenPrintService.printKitchenOrder(
              orderForPrint,
              orderState.tableId || undefined,
              { fontSize: 'medium' }
            );
            console.log('🖨️ Kitchen order print result:', printResult);
          }

        } catch (error) {
          console.error('❌ Kitchen print error:', error);
        }
      } else {
        console.log('🖨️ [NewOrderingInterface] Kitchen printing skipped - silent update mode');
      }

      // Print receipt immediately
      try {
        const receiptResult = await kitchenPrintService.printReceipt(
          { ...payOrder, _id: result?._id || result?.id || payOrder._id, id: result?.id || result?._id || payOrder.id },
          { fontSize: 'medium' }
        );

        if (receiptResult.success) {
          console.log(`✅ Receipt printed for order ${result.id}`);
        } else {
          console.warn(`⚠️ Receipt print failed: ${receiptResult.error}`);
        }
      } catch (printError) {
        console.error(`❌ Failed to print receipt:`, printError);
      }

      // Refresh orders after a delay
      console.log('🔄 [NewOrderingInterface] Refreshing orders...');
      setTimeout(() => { refreshOrders(); }, 1000);

      // Reset the order and clear edit mode
      console.log('🔄 [NewOrderingInterface] Resetting order state...');
      dispatch({ type: 'RESET_ORDER' });

      // Clear edit mode and UI state
      if (isEditMode) {
        clearEditOrder();
      }
      setUiState(initialUiState);

      // Clear staff selections if in staff menu mode
      if (uiState.selectedCategory === 'staff-menu') {
        // Staff menu clearing will be handled by StaffMenuManager integration
      }

      // Close payment form
      setPayOrder(null);

      console.log('✅ [NewOrderingInterface] Order confirmation process completed successfully');

    } catch (error) {
      console.error('❌ [NewOrderingInterface] Error processing order:', error);
      setPaymentErrorMsg(error instanceof Error ? error.message : "Payment failed");
    } finally {
      setIsOperationLoading(false);
      console.log('🏁 [NewOrderingInterface] Process finished, loading state cleared');
    }
  }, [
    payOrder,
    isEditMode,
    editOrder,
    createOrder,
    updateOrder,
    refreshOrders,
    dispatch,
    setUiState,
    clearEditOrder,
    processOrderPayment,
    orderState.tableId,
    shouldPrintToKitchen
  ]);

  // Handle confirming order without payment
  const handleConfirmOrder = useCallback(async (printToKitchen: boolean = false) => {
    if (!orderState.items?.length) return;
    if (requiresTable(orderState.orderType) && !orderState.tableId) return;
    if (!validateCustomerInfo(orderState.orderType, orderState.customer)) return;

    try {
      setIsOperationLoading(true);

      const combinedItemsMap: { [key: string]: OrderItem } = {};
      orderState.items.forEach((item: OrderItem) => {
        const itemSignature = getItemSignature(item);
        if (combinedItemsMap[itemSignature]) {
          combinedItemsMap[itemSignature].quantity += item.quantity;
        } else {
          combinedItemsMap[itemSignature] = { ...item };
        }
      });

      const combinedItems = Object.values(combinedItemsMap);

      let orderData: any;

      if (isEditMode && editOrder) {
        orderData = {
          tableId: orderState.orderType === 'dine-in' ? orderState.tableId : '',
          orderType: orderState.orderType,
          items: combinedItems.map((item: OrderItem) => ({ ...item, notes: item.notes || '' })),
          total: calculateTotal(combinedItems),
          notes: orderState.notes,
          customer: requiresCustomerInfo(orderState.orderType) ? orderState.customer : undefined,
          paymentStatus: editOrder.paymentStatus || 'unpaid',
          status: editOrder.status || 'pending'
        };
      } else {
        orderData = {
          tableId: orderState.orderType === 'dine-in' ? orderState.tableId : '',
          orderType: orderState.orderType,
          status: 'pending',
          items: combinedItems.map((item: OrderItem) => ({ ...item, notes: item.notes || '' })),
          total: calculateTotal(combinedItems),
          notes: orderState.notes,
          customer: requiresCustomerInfo(orderState.orderType) ? orderState.customer : undefined,
          paymentStatus: 'unpaid',
          createdBy: user?.name || (user as any)?.username || 'unknown',
          createdByName: user?.name || 'Personnel Inconnu'
        };
      }

      let result: any;
      if (isEditMode && editOrder) {
        result = await updateOrder(editOrder.id || editOrder._id, orderData);
      } else {
        result = await createOrder(orderData);
      }

      if (printToKitchen) {
        try {
          const orderForPrint: Order = {
            ...orderData,
            _id: result?._id || result?.id || `order:${Date.now()}`,
            id: result?.id || result?._id || `order:${Date.now()}`,
            type: 'order_document',
            schemaVersion: 'v4.0',
            createdAt: result?.createdAt || new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };

          if (isEditMode && editOrder) {
            console.log('🔧 [DEBUG] About to call printKitchenUpdate for order:', orderForPrint.id);
            const printResult = await kitchenPrintService.printKitchenUpdate(orderForPrint, 'items_changed');
            console.log('🖨️ Kitchen modification print result:', printResult);
            console.log('🔧 [DEBUG] Print jobs generated:', printResult.printJobs?.length || 0);
            console.log('🔧 [DEBUG] Show preview flag:', printResult.showPreview);
            console.log('🔧 [DEBUG] Actually printed flag:', printResult.actuallyPrinted);
          } else {
            console.log('🔧 [DEBUG] About to call printKitchenOrder for order:', orderForPrint.id);
            const printResult = await kitchenPrintService.printKitchenOrder(orderForPrint, orderState.tableId || undefined, { fontSize: 'medium' });
            console.log('🖨️ Kitchen order print result:', printResult);
            console.log('🔧 [DEBUG] Print jobs generated:', printResult.printJobs?.length || 0);
            console.log('🔧 [DEBUG] Show preview flag:', printResult.showPreview);
            console.log('🔧 [DEBUG] Actually printed flag:', printResult.actuallyPrinted);
          }
        } catch (error) {
          console.error('Kitchen modification print error:', error);
        }
      }

      setTimeout(() => { refreshOrders(); }, 1000);
      dispatch({ type: 'RESET_ORDER' });
      
      if (isEditMode) {
        clearEditOrder();
      }
      setUiState(initialUiState);

    } catch (error) {
      console.error('Error confirming order:', error);
    } finally {
      setIsOperationLoading(false);
    }
  }, [
    orderState,
    isEditMode,
    editOrder,
    user,
    createOrder,
    updateOrder,
    refreshOrders,
    dispatch,
    setUiState,
    clearEditOrder
  ]);

  // Handle paying order (modify status only)
  const handlePayOrder = useCallback(async (printReceipt: boolean = false) => {
    if (!orderState.items?.length) return;
    if (requiresTable(orderState.orderType) && !orderState.tableId) return;
    if (!validateCustomerInfo(orderState.orderType, orderState.customer)) return;

    try {
      setIsOperationLoading(true);

      const combinedItemsMap: { [key: string]: OrderItem } = {};
      orderState.items.forEach((item: OrderItem) => {
        const itemSignature = getItemSignature(item);
        if (combinedItemsMap[itemSignature]) {
          combinedItemsMap[itemSignature].quantity += item.quantity;
        } else {
          combinedItemsMap[itemSignature] = { ...item };
        }
      });

      const combinedItems = Object.values(combinedItemsMap);

      let orderData: any;

      if (isEditMode && editOrder) {
        orderData = {
          tableId: orderState.orderType === 'dine-in' ? orderState.tableId : '',
          orderType: orderState.orderType,
          items: combinedItems.map((item: OrderItem) => ({ ...item, notes: item.notes || '' })),
          total: calculateTotal(combinedItems),
          notes: orderState.notes,
          customer: requiresCustomerInfo(orderState.orderType) ? orderState.customer : undefined,
          paymentStatus: 'paid',
          status: 'pending'
        };
      } else {
        orderData = {
          tableId: orderState.orderType === 'dine-in' ? orderState.tableId : '',
          orderType: orderState.orderType,
          status: 'pending',
          items: combinedItems.map((item: OrderItem) => ({ ...item, notes: item.notes || '' })),
          total: calculateTotal(combinedItems),
          notes: orderState.notes,
          customer: requiresCustomerInfo(orderState.orderType) ? orderState.customer : undefined,
          paymentStatus: 'unpaid',
          createdBy: user?.name || (user as any)?.username || 'unknown',
          createdByName: user?.name || 'Personnel Inconnu'
        };
      }

      let result: any;
      if (isEditMode && editOrder) {
        result = await updateOrder(editOrder.id || editOrder._id, orderData);
      } else {
        result = await createOrder(orderData);
      }

      if (printReceipt) {
        try {
          const orderForPrint: Order = {
            ...orderData,
            _id: result?._id || result?.id || `order:${Date.now()}`,
            id: result?.id || result?._id || `order:${Date.now()}`,
            type: 'order_document',
            schemaVersion: 'v4.0',
            createdAt: result?.createdAt || new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };

          await kitchenPrintService.printReceipt(
            orderForPrint,
            { fontSize: 'medium' }
          );
        } catch (error) {
          console.error('Receipt print error:', error);
        }
      }

      setTimeout(() => { refreshOrders(); }, 1000);
      dispatch({ type: 'RESET_ORDER' });
      
      if (isEditMode) {
        clearEditOrder();
      }
      setUiState(initialUiState);

    } catch (error) {
      console.error('Error paying order:', error);
    } finally {
      setIsOperationLoading(false);
    }
  }, [
    orderState,
    isEditMode,
    editOrder,
    user,
    createOrder,
    updateOrder,
    refreshOrders,
    dispatch,
    setUiState,
    clearEditOrder
  ]);

  // Memoize the refreshOrders button handler 
  const handleRefreshOrders = useCallback(() => {
    refreshOrders();
  }, [refreshOrders]);

  // Handler for custom pizza clicks
  const handleCustomPizzaClick = useCallback((item: any) => {
    // Find the category this custom pizza belongs to
    const pizzaCategory = displayCategories.find(cat => cat.id === item.categoryId) || displayCategories[0];
    if (pizzaCategory) {
      setCustomPizzaEditData({
        originalItemId: item.id,
        quarters: item.quarters || [],
        size: item.size || 'Standard',
        notes: item.notes || ''
      });

      setSelectedItemForSupplements(`custom_pizza_editor-${item.size || 'default'}`);
      setUiState(prev => ({
        ...prev,
        selectedItemSizes: { ...prev.selectedItemSizes, custom_pizza_editor: item.size || "" }
      }));
    }
  }, [displayCategories, setSelectedItemForSupplements, setCustomPizzaEditData, setUiState]);

  // --- EFFECTS ---
  // Initialize selected category when categories are loaded
  useEffect(() => {
    if (isReady && categories.length > 0) {
      setSelectedCategory(categories[0]?.id || "");
    }
  }, [isReady, categories, setSelectedCategory]);

  // Initialize table statuses when tables and orders are loaded
  useEffect(() => {
    if (isReady && tables.length > 0) {
      const initialTableStatuses = tables.map(table => {
        const activeOrders = orders.filter((o: any) =>
          o.tableId === table.id && (o.status === 'pending' || o.status === 'preparing' || o.status === 'served')
        );
        const firstActiveOrder = activeOrders[0];

        return {
          id: table.id || "",
          status: activeOrders.length > 0 ? 'occupied' as const : 'free' as const,
          occupiedSince: firstActiveOrder ? firstActiveOrder.createdAt : null,
          currentOrderId: firstActiveOrder?._id || null
        };
      });

      setTableStatuses(initialTableStatuses);
    }
  }, [isReady, tables, orders]);

  // Click outside handling is now managed by the CustomizationModal component

  // Panel state management - must be called at top level (Rules of Hooks)
  const { parsedData: panelParsedData, error: panelError } = usePanelState(uiState.selectedItemForSupplements, displayCategories);

  // Log panel errors
  useEffect(() => {
    if (panelError) {
      console.error('🔧 [Inline Panel] Error:', panelError);
    }
  }, [panelError]);

  // 🎯 Enhanced edit order handling with state persistence
  useEffect(() => {
    if (editOrder && editSessionId) {
      console.log('📝 [NewOrderingInterface] Loading order for editing:', editOrder.id || editOrder._id);

      // Load the order into the reducer
      dispatch({ type: 'LOAD_ORDER', payload: editOrder });

      // 🔄 Restore UI state from localStorage if available
      try {
        const storedUiState = localStorage.getItem(`ui_state_${editSessionId}`);
        if (storedUiState) {
          const parsedUiState = JSON.parse(storedUiState);
          console.log('🔄 [NewOrderingInterface] Restoring UI state from localStorage');

          // Restore selected supplements (convert arrays back to Sets)
          const restoredSupplements: { [key: string]: Set<string> } = {};
          Object.entries(parsedUiState.selectedSupplements || {}).forEach(([key, value]) => {
            restoredSupplements[key] = new Set(value as string[]);
          });

          setUiState(prev => ({
            ...prev,
            selectedItemSizes: parsedUiState.selectedItemSizes || {},
            itemNotes: parsedUiState.itemNotes || {},
            selectedSupplements: restoredSupplements,
            selectedCategory: parsedUiState.selectedCategory || prev.selectedCategory
          }));
        }
      } catch (error) {
        console.warn('⚠️ [NewOrderingInterface] Failed to restore UI state:', error);
      }
    } else if (!editOrder) {
      // Clear UI state when not editing
      setUiState(initialUiState);
    }
  }, [editOrder, editSessionId]);

  // 💾 Persist UI state changes when in edit mode
  useEffect(() => {
    if (isEditMode && editSessionId) {
      try {
        // Convert Sets to arrays for JSON serialization
        const serializableSupplements: { [key: string]: string[] } = {};
        Object.entries(uiState.selectedSupplements).forEach(([key, value]) => {
          serializableSupplements[key] = Array.from(value);
        });

        const uiStateToStore = {
          selectedItemSizes: uiState.selectedItemSizes,
          itemNotes: uiState.itemNotes,
          selectedSupplements: serializableSupplements,
          selectedCategory: uiState.selectedCategory
        };

        localStorage.setItem(`ui_state_${editSessionId}`, JSON.stringify(uiStateToStore));
      } catch (error) {
        console.warn('⚠️ [NewOrderingInterface] Failed to persist UI state:', error);
      }
    }
  }, [uiState.selectedItemSizes, uiState.itemNotes, uiState.selectedSupplements, uiState.selectedCategory, isEditMode, editSessionId]);

  // 🧹 Cleanup UI state when edit session ends
  useEffect(() => {
    return () => {
      if (editSessionId) {
        try {
          localStorage.removeItem(`ui_state_${editSessionId}`);
        } catch (error) {
          console.warn('⚠️ [NewOrderingInterface] Failed to cleanup UI state:', error);
        }
      }
    };
  }, [editSessionId]);

  // Process categories to ensure they have supplements available
  useEffect(() => {
    if (categories.length > 0) {
      // Categories now use supplements instead of addons
      // No need to add fake addons - supplements are managed separately
      setLocalCategories(categories);
    }
  }, [categories]);

  // Auto-update order when supplements change
  useEffect(() => {
    if (uiState.selectedItemForSupplements) {
      console.log('🔍 [useEffect] Supplements changed, calling updateSelectedItem');
      updateSelectedItem();
    }
  }, [uiState.selectedSupplements, updateSelectedItem]);

  // Removed problematic useEffect that was causing infinite loops
  // The updateSelectedItem function is now called explicitly when needed

  // --- JSX: MAIN COMPONENT RENDER ---
  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-destructive">{error.message}</p>
            <Button className="mt-4" onClick={() => window.location.reload()}>Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle>Initializing System</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center p-8 gap-4">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
              <p className="text-sm text-muted-foreground">{isAuthenticated ? "Loading data..." : "Connecting to database..."}</p>
              {loadingTimeout && (
                <div className="text-center space-y-2">
                  <p className="text-sm text-orange-600">⚠️ Loading is taking longer than expected</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.location.reload()}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh Page
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full h-full flex flex-col overflow-hidden p-2 max-w-full">
      <Card className="flex flex-col shadow-sm overflow-hidden h-full max-w-full">
        <div className="grid grid-cols-12 h-full gap-0.5 lg:grid-cols-12 md:grid-cols-12 sm:grid-cols-1 min-h-0 max-w-full overflow-hidden">
          {/* Left Panel - Menu */}
          <div className="col-span-8 flex flex-col h-full lg:col-span-8 md:col-span-7 sm:col-span-12 border-r relative min-h-0 overflow-hidden">
            <div className="flex-shrink-0 p-1 border-b flex items-center justify-between bg-muted/30">
              <h3 className="font-semibold text-xs">Menu</h3>
              <div className="flex gap-1">
                <Button variant="outline" size="sm" className="h-6 px-1.5" onClick={handleRefreshOrders}>
                  <RefreshCw className="h-3 w-3" />
                </Button>
              </div>
            </div>
            <div className="flex flex-col h-full min-h-0">
              <div className="flex-shrink-0 border-b py-1 px-1.5">
                <CategorySelector
                  categories={displayCategories}
                  selectedCategory={uiState.selectedCategory}
                  onSelectCategory={setSelectedCategory}
                />
              </div>
              {/* Menu Items Section - Fixed height with proper scrolling */}
              <div className="overflow-hidden relative flex-1 min-h-0">
                {displayCategories.map((category) => (
                  <div key={category.id || ''} className={`h-full w-full ${uiState.selectedCategory === category.id ? 'block' : 'hidden'}`}>
                    <ScrollArea className="h-full w-full">
                      <div className="p-1 grid auto-rows-auto grid-cols-4 2xl:grid-cols-6 xl:grid-cols-5 lg:grid-cols-4 md:grid-cols-3 sm:grid-cols-2 gap-1 pb-4">
                        {/* Add synthetic "Custom" item for pizza categories */}
                        {(category.name?.toLowerCase().includes('pizza') || category.id?.toLowerCase().includes('pizza')) && category.isQuarterable && (() => {
                          // Get the actual sizes from the category but set prices to 0 (won't be displayed)
                          const firstItem = category.items?.[0];
                          const actualSizes = firstItem?.prices || { 'Standard': 0 };
                          const customPrices = Object.keys(actualSizes).reduce((acc, size) => ({ ...acc, [size]: 0 }), {});

                          const customItem: MenuItem = {
                            id: 'custom_pizza',
                            name: 'Personnalisé',
                            description: 'Créez votre pizza personnalisée',
                            prices: customPrices, // Use actual sizes but with 0 prices (won't be displayed)
                            color: '#f97316' // Orange color for custom pizza
                          };
                          const selectedSize = uiState.selectedItemSizes[customItem.id];
                          const isSelected = selectedSize && uiState.selectedItemForSupplements === `${customItem.id}-${selectedSize}`;
                          return (
                            <MenuItemCard
                              key={customItem.id}
                              item={customItem}
                              categoryId={category.id}
                              categories={displayCategories}
                              isSelected={isSelected}
                              selectedSize={selectedSize}
                              lastAddedItem={uiState.lastAddedItem}
                              onSelect={handleSelectItemForCustomization}
                              onAddItem={handleAddItem}
                            />
                          );
                        })()}

                        {(category.items || []).map((item: MenuItem) => {
                          const selectedSize = uiState.selectedItemSizes[item.id];
                          const isSelected = selectedSize && uiState.selectedItemForSupplements === `${item.id}-${selectedSize}`;
                          return (
                            <MenuItemCard
                              key={item.id}
                              item={item}
                              categoryId={category.id}
                              categories={displayCategories}
                              isSelected={isSelected}
                              selectedSize={selectedSize}
                              lastAddedItem={uiState.lastAddedItem}
                              onSelect={handleSelectItemForCustomization}
                              onAddItem={handleAddItem}
                            />
                          );
                        })}
                      </div>
                    </ScrollArea>
                  </div>
                ))}

                {/* Item Customization Panel - Bottom of menu items */}
                {uiState.selectedItemForSupplements && panelParsedData && !panelError && (() => {
                  const { itemId, category, item } = panelParsedData;
                  const selectedSize = uiState.selectedItemSizes[itemId] ||
                    Object.keys(item?.prices || {})[0] || 'default';

                  return (
                    <div className="absolute bottom-0 left-0 right-0 bg-background border-t border-border shadow-lg max-h-[24rem] overflow-auto">
                      <ItemCustomizationPanel
                        category={category}
                        item={item}
                        selectedSize={selectedSize}
                        selectedSupplements={uiState.selectedSupplements}
                        itemNotes={uiState.itemNotes}
                        getSupplementKey={getSupplementKey}
                        getItemNoteKey={getItemNoteKey}
                        isSupplementSelected={isSupplementSelected}
                        toggleSupplementSelection={toggleSupplementSelection}
                        updateItemNote={updateItemNote}
                        finalizeItem={finalizeCurrentItem}
                        categories={displayCategories}
                        onCustomPizzaConfirm={handleInlineCustomPizzaConfirm}
                        customPizzaEditData={customPizzaEditData}
                        onClose={() => {
                          setSelectedItemForSupplements(null);
                          setCustomPizzaEditData(null);
                        }}
                        
                      />
                    </div>
                  );
                })()}

              </div>
            </div>
          </div>

          {/* Right Panel - Order Details & Summary */}
          <div className="col-span-4 flex flex-col h-full lg:col-span-4 md:col-span-5 sm:col-span-12 pl-0.5 min-h-0 max-w-full overflow-hidden">
            <div className="flex-shrink-0 px-3 py-2 border-b bg-gray-50/80 flex items-center justify-between">
              <h3 className="font-medium text-sm text-gray-700">Détails de la commande</h3>
              <div className="flex items-center gap-1">
                {/* Hold Queue Toggle */}
                {activeHolds.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-6 px-2 text-xs"
                    onClick={() => setShowHoldQueue(!showHoldQueue)}
                  >
                    <List className="h-3 w-3 mr-1" />
                    <span>{activeHolds.length}</span>
                  </Button>
                )}
              </div>
            </div>

            {/* Order Type, Table Selection, Customer Info - Hide for staff menu */}
            {uiState.selectedCategory !== 'staff-menu' && (
              <div className="flex-shrink-0">
                <OrderDetails
                  orderType={orderState.orderType}
                  tableId={orderState.tableId}
                  tables={tables}
                  tableStatuses={tableStatuses}
                  customer={orderState.customer}
                  deliveryPerson={orderState.deliveryPerson}
                  notes={orderState.notes}
                  onOrderTypeChange={handleOrderTypeChange}
                  onTableChange={handleTableChange}
                  onNotesChange={handleNotesChange}
                  onCustomerInfoChange={handleCustomerInfoChange}
                />
              </div>
            )}

            {/* Hold Queue Panel */}
            {showHoldQueue && activeHolds.length > 0 && (
              <div className="flex-shrink-0 border-b">
                <div className="p-2 bg-orange-50 border-l-4 border-orange-200">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Pause className="h-4 w-4 text-orange-600" />
                      <span className="font-medium text-sm text-orange-800">Orders on Hold</span>
                      <Badge variant="outline" className="h-5 px-1.5 text-xs bg-orange-100 text-orange-700 border-orange-300">
                        {activeHolds.length}
                      </Badge>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 text-orange-600 hover:text-orange-800"
                      onClick={() => setShowHoldQueue(false)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                  
                  {/* Hold Error Display */}
                  {holdError && (
                    <div className="mb-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                      <div className="flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        <span className="font-medium">Hold Error:</span>
                      </div>
                      <div className="mt-1">{holdError}</div>
                    </div>
                  )}
                  
                  <ScrollArea className="max-h-32">
                    <div className="space-y-1">
                      {activeHolds.map((hold) => (
                        <div
                          key={hold.holdId}
                          className="flex items-center justify-between p-2 bg-white rounded border border-orange-200 text-xs"
                        >
                          <div className="flex items-center gap-2 flex-1 min-w-0">
                            <div className="font-medium truncate">{hold.label}</div>
                            <Badge variant="outline" className="h-4 px-1 text-xs">
                              {hold.orderState.items.length}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-1">
                            <Button
                              size="sm"
                              className="h-6 px-2 text-xs bg-green-600 hover:bg-green-700"
                              onClick={() => handleResumeOrder(hold.holdId)}
                              disabled={isResuming}
                            >
                              {isResuming ? (
                                <div className="animate-spin rounded-full h-3 w-3 border border-white border-t-transparent" />
                              ) : (
                                'Resume'
                              )}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-6 w-6 p-0 text-red-600 hover:text-red-800 border-red-200"
                              onClick={() => handleClearHold(hold.holdId)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              </div>
            )}

            {/* Staff Menu Manager */}
            {uiState.selectedCategory === 'staff-menu' && (
              <div className="flex-1 overflow-y-auto min-h-0 bg-white">
                <StaffMenuManager
                  ref={(ref) => {
                    staffMenuRef.current = ref;
                    setIsStaffMenuReady(!!ref);
                  }}
                  config={staffMenuConfig}
                  menuItems={staffMenuItems}
                  currentUser={user}
                  onStaffSelect={(staffId) => {
                    // Handle staff selection if needed
                  }}
                />
              </div>
            )}



            {/* Order Items and Summary - Hide for staff menu */}
            {uiState.selectedCategory !== 'staff-menu' && (
              <div className="flex-1 overflow-hidden min-h-0">
                <OrderSummary
                  items={orderState.items}
                  total={orderState.total}
                  onIncrement={handleIncrement}
                  onDecrement={handleDecrement}
                  onRemove={handleRemoveItem}
                  selectedItemForSupplements={uiState.selectedItemForSupplements}
                  selectedItemSizes={uiState.selectedItemSizes}
                  setSelectedItemForSupplements={setSelectedItemForSupplements}
                  setSelectedItemSizes={(updater) => setUiState(prev => ({
                    ...prev,
                    selectedItemSizes: updater(prev.selectedItemSizes)
                  }))}
                  setSelectedSupplements={(updater) => setUiState(prev => ({
                    ...prev,
                    selectedSupplements: updater(prev.selectedSupplements)
                  }))}
                  setItemNotes={(updater) => setUiState(prev => ({
                    ...prev,
                    itemNotes: updater(prev.itemNotes)
                  }))}
                  getSupplementKey={getSupplementKey}
                  getItemNoteKey={getItemNoteKey}
                  displayCategories={displayCategories}
                  onCustomPizzaClick={handleCustomPizzaClick}
                />
              </div>
            )}

            {/* Place Order Button - Hide for staff menu */}
            {uiState.selectedCategory !== 'staff-menu' && (
              <div className="flex-shrink-0 border-t">
                <div className="px-2 py-1.5 flex items-center justify-between border-b bg-muted/10">
                  <h3 className="font-medium text-sm">Total</h3>
                  <div className="font-bold text-base text-primary tabular-nums">{orderState.total} DA</div>
                </div>
                <div className="p-2 space-y-2">
                  {/* Hold System UI - Show hold queue toggle and panel */}
                  {!isEditMode && uiState.selectedCategory !== 'staff-menu' && (
                    <div className="space-y-2">
                      {/* Hold Control Buttons */}
                      <div className="grid grid-cols-2 gap-1.5">
                        {/* Hold Button - Only show when there are items */}
                        {orderState.items.length > 0 && (
                          <Button
                            variant="outline"
                            className="h-8 text-xs font-medium bg-orange-50 hover:bg-orange-100 text-orange-700 border-orange-300 transition-colors duration-200"
                            size="sm"
                            onClick={handleHoldOrder}
                            disabled={isHolding || isOperationLoading}
                          >
                            {isHolding ? (
                              <div className="flex items-center gap-1">
                                <div className="animate-spin rounded-full h-3 w-3 border-2 border-orange-600 border-t-transparent"></div>
                                <span>Holding...</span>
                              </div>
                            ) : (
                              <div className="flex items-center gap-1">
                                <Pause className="h-3 w-3" />
                                <span>Hold</span>
                              </div>
                            )}
                          </Button>
                        )}
                        
                        {/* Hold Queue Toggle */}
                        <Button
                          variant={showHoldQueue ? "default" : "outline"}
                          className={cn(
                            "h-8 text-xs font-medium transition-colors duration-200",
                            showHoldQueue 
                              ? "bg-blue-600 hover:bg-blue-700 text-white border-blue-600" 
                              : "bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-300"
                          )}
                          size="sm"
                          onClick={() => setShowHoldQueue(!showHoldQueue)}
                        >
                          <div className="flex items-center gap-1">
                            <List className="h-3 w-3" />
                            <span>Queue</span>
                            {activeHolds.length > 0 && (
                              <Badge variant="secondary" className="h-4 px-1 text-xs ml-1">
                                {activeHolds.length}
                              </Badge>
                            )}
                          </div>
                        </Button>
                      </div>
                      
                      {/* Hold Queue Panel */}
                      {showHoldQueue && (
                        <div className="border rounded-lg bg-card shadow-sm">
                          <div className="flex items-center justify-between p-2 border-b bg-muted/30">
                            <div className="flex items-center gap-2">
                              <Pause className="h-3 w-3 text-orange-500" />
                              <span className="font-medium text-xs">Held Orders</span>
                              <Badge variant="outline" className="h-4 px-1 text-xs">
                                {activeHolds.length}
                              </Badge>
                            </div>
                          </div>
                          
                          <ScrollArea className="max-h-48">
                            <div className="p-2 space-y-1.5">
                              {activeHolds.length === 0 ? (
                                <div className="text-center py-4 text-xs text-muted-foreground">
                                  <Package className="h-6 w-6 mx-auto mb-1 opacity-50" />
                                  <p>No orders on hold</p>
                                </div>
                              ) : (
                                activeHolds.map((hold) => {
                                  const heldFor = new Date().getTime() - new Date(hold.createdAt).getTime();
                                  const minutes = Math.floor(heldFor / 60000);
                                  const hours = Math.floor(minutes / 60);
                                  const timeAgo = minutes < 1 ? 'Just now' : 
                                    hours > 0 ? `${hours}h ${minutes % 60}m ago` : `${minutes}m ago`;
                                  
                                  const isUrgent = hold.context.priority === 'urgent' || minutes > 30;
                                  const isHigh = hold.context.priority === 'high' || minutes > 15;
                                  
                                  return (
                                    <div
                                      key={hold.holdId}
                                      className={cn(
                                        "border rounded p-2 bg-white hover:bg-gray-50 transition-colors",
                                        isUrgent && "border-red-300 bg-red-50 hover:bg-red-100",
                                        isHigh && !isUrgent && "border-orange-300 bg-orange-50 hover:bg-orange-100"
                                      )}
                                    >
                                      <div className="flex items-center justify-between mb-1">
                                        <div className="flex items-center gap-1 flex-1">
                                          {isUrgent && <AlertTriangle className="h-3 w-3 text-red-500" />}
                                          {isHigh && !isUrgent && <Clock className="h-3 w-3 text-orange-500" />}
                                          <span className={cn(
                                            "font-medium text-xs truncate",
                                            isUrgent && "text-red-700",
                                            isHigh && !isUrgent && "text-orange-700"
                                          )}>
                                            {hold.label}
                                          </span>
                                        </div>
                                        <div className="flex items-center gap-1 ml-2">
                                          <span className={cn(
                                            "text-xs",
                                            isUrgent ? "text-red-600" : isHigh ? "text-orange-600" : "text-muted-foreground"
                                          )}>
                                            {timeAgo}
                                          </span>
                                        </div>
                                      </div>
                                      
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                          <Package className="h-3 w-3" />
                                          <span>{hold.orderState.items.length} items</span>
                                          {hold.orderState.tableId && (
                                            <>
                                              <span>•</span>
                                              <span>Table {hold.orderState.tableId}</span>
                                            </>
                                          )}
                                        </div>
                                        
                                        <div className="flex items-center gap-1">
                                          <Button
                                            size="sm"
                                            className="h-6 px-2 text-xs bg-green-500 hover:bg-green-600"
                                            onClick={() => handleResumeOrder(hold.holdId)}
                                            disabled={isResuming}
                                          >
                                            {isResuming ? (
                                              <div className="animate-spin rounded-full h-2 w-2 border border-white border-t-transparent" />
                                            ) : (
                                              <>
                                                <Play className="h-2 w-2 mr-1" />
                                                Resume
                                              </>
                                            )}
                                          </Button>
                                          
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            className="h-6 w-6 p-0 text-red-500 hover:text-red-600 hover:bg-red-50"
                                            onClick={() => handleClearHold(hold.holdId)}
                                          >
                                            <X className="h-3 w-3" />
                                          </Button>
                                        </div>
                                      </div>
                                      
                                      {hold.orderState.notes && (
                                        <div className="mt-1 text-xs text-muted-foreground truncate">
                                          "{hold.orderState.notes}"
                                        </div>
                                      )}
                                    </div>
                                  );
                                })
                              )}
                            </div>
                          </ScrollArea>
                        </div>
                      )}
                    </div>
                  )}
                  {isEditMode ? (
                    <>
                      {/* Split modify buttons */}
                      <div className="grid grid-cols-2 gap-2">
                        <Button
                          variant="outline"
                          className="h-10 text-sm font-medium bg-white hover:bg-gray-50 text-gray-700 border-gray-300 transition-colors duration-200"
                          size="sm"
                          disabled={
                            isOperationLoading ||
                            !orderState.items.length ||
                            (requiresTable(orderState.orderType) && !orderState.tableId) ||
                            !validateCustomerInfo(orderState.orderType, orderState.customer)
                          }
                          onClick={() => handleConfirmOrder(false)}
                        >
                          {isOperationLoading ? (
                            <div className="flex items-center gap-1.5">
                              <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-400 border-t-transparent"></div>
                              <span className="text-xs">Modification...</span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-1.5">
                              <CheckCircle className="h-4 w-4" />
                              <span className="font-medium whitespace-nowrap overflow-hidden text-ellipsis">Modifier silencieusement</span>
                            </div>
                          )}
                        </Button>
                        <Button
                          className="h-10 text-sm font-medium bg-orange-600 hover:bg-orange-700 text-white border-orange-600 transition-colors duration-200"
                          size="sm"
                          disabled={
                            isOperationLoading ||
                            !orderState.items.length ||
                            (requiresTable(orderState.orderType) && !orderState.tableId) ||
                            !validateCustomerInfo(orderState.orderType, orderState.customer)
                          }
                          onClick={() => handleConfirmOrder(true)}
                        >
                          {isOperationLoading ? (
                            <div className="flex items-center gap-1.5">
                              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                              <span className="text-xs">Impression...</span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-1.5">
                              <Receipt className="h-4 w-4" />
                              <span className="font-medium whitespace-nowrap overflow-hidden text-ellipsis">Modifier & imprimer</span>
                            </div>
                          )}
                        </Button>
                      </div>
                      <div className="mt-2">
                        <Button
                          variant="destructive"
                          className="w-full h-10 text-sm font-medium bg-red-600 hover:bg-red-700 text-white border-red-600 transition-colors duration-200"
                          size="sm"
                          onClick={() => {
                            clearEditOrder();
                            dispatch({ type: 'RESET_ORDER' });
                            setUiState(initialUiState);
                          }}
                        >
                          <X className="h-4 w-4 mr-1.5" />
                          <span className="font-medium">Annuler</span>
                        </Button>
                      </div>
                    </>
                  ) : (
                    <div className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <Button
                          variant="outline"
                          className="h-10 text-sm font-medium bg-white hover:bg-gray-50 text-gray-700 border-gray-300 transition-colors duration-200"
                          size="sm"
                          disabled={
                            isOperationLoading ||
                            !orderState.items.length ||
                            (requiresTable(orderState.orderType) && !orderState.tableId) ||
                            !validateCustomerInfo(orderState.orderType, orderState.customer)
                          }
                          onClick={() => handleConfirmOrder(false)}
                        >
                          <CheckCircle className="h-4 w-4 mr-1.5" />
                          <span className="font-medium">Confirmer</span>
                        </Button>
                        <Button
                          className="h-10 text-sm font-medium bg-orange-600 hover:bg-orange-700 text-white border-orange-600 transition-colors duration-200"
                          size="sm"
                          disabled={
                            isOperationLoading ||
                            !orderState.items.length ||
                            (requiresTable(orderState.orderType) && !orderState.tableId) ||
                            !validateCustomerInfo(orderState.orderType, orderState.customer)
                          }
                          onClick={() => handleConfirmOrder(true)}
                        >
                          <Receipt className="h-4 w-4 mr-1.5" />
                          <span className="font-medium whitespace-nowrap overflow-hidden text-ellipsis">Confirmer & imprimer</span>
                        </Button>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <Button
                          className="h-10 text-sm font-medium bg-green-600 hover:bg-green-700 text-white border-green-600 transition-colors duration-200"
                          size="sm"
                          disabled={
                            isOperationLoading ||
                            !orderState.items.length ||
                            (requiresTable(orderState.orderType) && !orderState.tableId) ||
                            !validateCustomerInfo(orderState.orderType, orderState.customer)
                          }
                          onClick={() => {
                            // Create order first then show payment form 
                            handlePayOrder(false);
                          }}
                        >
                          <CheckSquare className="h-4 w-4 mr-1.5" />
                          <span className="font-medium">Payer</span>
                        </Button>
                        <Button
                          className="h-10 text-sm font-medium bg-gray-800 hover:bg-gray-900 text-white border-gray-800 transition-colors duration-200"
                          size="sm"
                          disabled={
                            isOperationLoading ||
                            !orderState.items.length ||
                            (requiresTable(orderState.orderType) && !orderState.tableId) ||
                            !validateCustomerInfo(orderState.orderType, orderState.customer)
                          }
                          onClick={() => handlePlaceOrder()}
                        >
                          {isOperationLoading ? (
                            <div className="flex items-center gap-1.5">
                              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                              <span className="text-xs">Traitement...</span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-1.5">
                              <Receipt className="h-4 w-4" />
                              <span className="font-medium whitespace-nowrap overflow-hidden text-ellipsis">Payer & imprimer</span>
                            </div>
                          )}
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}


          </div>
        </div>
      </Card>

      {/* Payment Dialog */}
      <Dialog open={!!payOrder} onOpenChange={open => { if (!open) setPayOrder(null); }}>
        <DialogContent className="max-w-5xl w-[95vw] max-h-[90vh] p-0 overflow-hidden">
          <DialogHeader className="sr-only">
            <DialogTitle>Traitement du Paiement</DialogTitle>
          </DialogHeader>
          {payOrder && (
            <PaymentForm
              order={payOrder}
              isProcessing={isOperationLoading}
              errorMsg={paymentErrorMsg}
              onCancel={() => {
                setPayOrder(null);
                setPaymentErrorMsg("");
                setIsOperationLoading(false);
              }}
              onSubmit={handlePaymentFormSubmit}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Item Customization is now rendered inline within the menu section */}
    </div>
  );
};

export default NewOrderingInterface;