"use client"

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { format, parseISO, isValid, startOfDay, isSameDay } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import {
  ChevronDownIcon,
  ChevronUpIcon,
  BanknoteIcon,
  CreditCardIcon,
  TruckIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ClockIcon,
  UserIcon,
  ShoppingCartIcon,
  Loader2,
  RefreshCwIcon,
  EyeIcon
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils/currency';
import { cn } from '@/lib/utils';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

// Helper functions
function getCollectionStatusIcon(status?: string): string {
  switch (status) {
    case 'collected': return '✅';
    case 'not_collected': return '⏳';
    case 'partial': return '⚠️';
    case 'failed': return '❌';
    default: return '';
  }
}

function getEventInfo(event: FinanceEvent) {
  switch (event.type) {
    case 'sale':
      const isDelivery = event.sale?.orderType === 'delivery';
      const statusIcon = isDelivery ? getCollectionStatusIcon(event.sale?.collectionStatus) : '';
      const orderTypeIcon = event.sale?.orderType === 'delivery' ? '🚚' : 
                           event.sale?.orderType === 'takeaway' ? '📦' : '🍽️';
      
      return {
        type: `Vente ${orderTypeIcon}`,
        typeColor: 'bg-transparent text-emerald-600 border-emerald-200',
        id: `#${event.sale?.orderId.split('-').pop() || '?'}`,
        mainInfo: event.sale?.customer || 'Client',
        subInfo: `${event.sale?.items ? event.sale.items.substring(0, 30) + (event.sale.items.length > 30 ? '...' : '') : 'Articles'}${event.sale?.tableId ? ` • Table ${event.sale.tableId}` : ''}${event.sale?.paymentMethod ? ` • ${event.sale.paymentMethod === 'card' ? '💳' : '💵'}` : ''}`,
        amount: event.netAmount,
        statusIcon: statusIcon
      };

    case 'collection':
      // Enhanced collection information with more details
      const kind = event.collection?.kind || 'delivery';
      const grossAmount = event.collection?.grossAmount || 0;
      const driverFee = event.collection?.driverFee || 0;
      const netAmount = event.collection?.netAmount || event.netAmount || 0;
      
      return {
        type: kind === 'caisse' ? 'Caisse Validation' : 'Livraison Collection',
        typeColor: kind === 'caisse' ? 'bg-transparent text-purple-600 border-purple-200' : 'bg-transparent text-blue-600 border-blue-200',
        id: kind === 'delivery' ? `${event.collection?.orderCount || 0} cmd` : 'Comptage',
        mainInfo: kind === 'delivery' ? (event.collection?.driverName || 'Driver') : (event.collection?.validatedBy || event.performedBy || 'User'),
        subInfo: kind === 'delivery'
          ? `Brut: ${formatCurrency(grossAmount)}${driverFee > 0 ? ` • Tarif: ${formatCurrency(driverFee)}` : ''} • Net: ${formatCurrency(netAmount)}`
          : `Compté: ${formatCurrency(netAmount)}${event.collection?.comments ? ' • ' + event.collection.comments.substring(0, 30) + '...' : ''}`,
        amount: event.netAmount,
        statusIcon: kind === 'delivery' ? '🚚' : '🧮'
      };

    case 'manual_in':
      return {
        type: 'Entrée Manuelle',
        typeColor: 'bg-transparent text-green-600 border-green-200',
        id: '💰',
        mainInfo: event.performedBy,
        subInfo: `${event.manualTransaction?.reason || event.description}${event.manualTransaction?.notes ? ' • ' + event.manualTransaction.notes.substring(0, 40) + '...' : ''}`,
        amount: event.netAmount,
        statusIcon: '⬆️'
      };

    case 'manual_out':
      return {
        type: 'Sortie Manuelle',
        typeColor: 'bg-transparent text-red-600 border-red-200',
        id: '💸',
        mainInfo: event.performedBy,
        subInfo: `${event.manualTransaction?.reason || event.description}${event.manualTransaction?.notes ? ' • ' + event.manualTransaction.notes.substring(0, 40) + '...' : ''}`,
        amount: event.netAmount,
        statusIcon: '⬇️'
      };
    
    default:
      return {
        type: event.type,
        typeColor: 'bg-gray-50 text-gray-700 border-gray-200',
        id: '-',
        mainInfo: event.performedBy,
        subInfo: event.description,
        amount: event.netAmount,
        statusIcon: ''
      };
  }
}

// New event-based structure for finance history
export interface FinanceEvent {
  id: string;
  type: 'sale' | 'collection' | 'manual_in' | 'manual_out';
  timestamp: string;
  netAmount: number; // Amount that affected the drawer
  performedBy: string;
  description: string;
  
  // Sale-specific data
  sale?: {
    orderId: string;
    orderType: 'dine-in' | 'takeaway' | 'delivery';
    customer: string;
    tableId?: string;
    items: string;
    paymentMethod: 'cash' | 'card';
    // Collection status ONLY for delivery orders
    collectionStatus?: 'not_collected' | 'collected' | 'partial' | 'failed';
  };
  
  // Collection-specific data
  collection?: {
    // Common
    kind?: 'delivery' | 'caisse';
    netAmount: number;

    // Delivery-specific
    driverName?: string;
    orderCount?: number;
    orders?: Array<{
      orderId: string;
      amount: number;
      customer: string;
      status: 'collected' | 'partial' | 'failed';
    }>;
    grossAmount?: number;
    driverFee?: number;

    // Caisse-specific
    validatedBy?: string;
    countedBy?: string;
    comments?: string;

    // Enhanced breakdown for better tracking
    collectionBreakdown?: {
      totalOrders?: number;
      grossCollected?: number;
      driverFees?: number;
      netToRestaurant?: number;
      discrepancies?: any[];
      manualExpenses?: any[];
    };
  };
  
  // Manual transaction data
  manualTransaction?: {
    reason: string;
    notes?: string;
  };
}

interface EventBasedFinanceHistoryProps {
  events: FinanceEvent[];
  isLoading?: boolean;
  onRefresh?: () => void;
  title?: string;
  maxHeight?: string;
}

interface DayGroup {
  date: string;
  events: FinanceEvent[];
  totalIn: number;
  totalOut: number;
  netAmount: number;
}

export default function EventBasedFinanceHistory({
  events,
  isLoading = false,
  onRefresh,
  title = "Historique Financier",
  maxHeight = "600px"
}: EventBasedFinanceHistoryProps) {
  const [expandedDays, setExpandedDays] = useState<Set<string>>(new Set());
  const [expandedHours, setExpandedHours] = useState<Set<string>>(new Set());

  // Group events by day and hour (following old convention)
  const groupedEvents = useMemo(() => {
    const grouped: Record<string, Record<string, FinanceEvent[]>> = {};

    events.forEach(event => {
      const eventDate = new Date(event.timestamp);
      const dateStr = format(eventDate, 'yyyy-MM-dd');
      const hour = eventDate.getHours();
      const hourKey = `${hour}`;

      if (!grouped[dateStr]) {
        grouped[dateStr] = {};
      }

      if (!grouped[dateStr][hourKey]) {
        grouped[dateStr][hourKey] = [];
      }

      grouped[dateStr][hourKey].push(event);
    });

    // Sort days (most recent first) and hours within each day
    return Object.entries(grouped)
      .sort(([dateA], [dateB]) => new Date(dateB).getTime() - new Date(dateA).getTime())
      .map(([date, hourGroups]) => ({
        date,
        hourGroups: Object.entries(hourGroups)
          .sort(([hourA], [hourB]) => parseInt(hourB) - parseInt(hourA))
          .map(([hour, events]) => ({
            hour: parseInt(hour),
            events: events.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
          }))
      }));
  }, [events]);

  const toggleDay = (date: string) => {
    const newExpanded = new Set(expandedDays);
    if (newExpanded.has(date)) {
      newExpanded.delete(date);
    } else {
      newExpanded.add(date);
    }
    setExpandedDays(newExpanded);
  };

  const toggleHour = (groupId: string) => {
    const newExpanded = new Set(expandedHours);
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId);
    } else {
      newExpanded.add(groupId);
    }
    setExpandedHours(newExpanded);
  };

  // Calculate day totals
  const calculateDayTotal = (hourGroups: any[]) => {
    return hourGroups.reduce((dayTotal, { events }) => {
      const salesOnly = events.filter((e: FinanceEvent) => e.type === 'sale');
      const sum = salesOnly.reduce((acc: number, e: FinanceEvent) => acc + e.netAmount, 0);
      return dayTotal + sum;
    }, 0);
  };

  // Calculate hour totals
  const calculateHourSummary = (events: FinanceEvent[]) => {
    const sales = events.filter(e => e.type === 'sale');
    const total = sales.reduce((sum, e) => sum + e.netAmount, 0);
    const inflow = sales.filter(e => e.netAmount > 0).reduce((sum, e) => sum + e.netAmount, 0);
    const outflow = Math.abs(sales.filter(e => e.netAmount < 0).reduce((sum, e) => sum + e.netAmount, 0));
    return { total, inflow, outflow, count: events.length };
  };

  // Format hour range
  const formatHourRange = (hour: number) => {
    const nextHour = (hour + 1) % 24;
    return `${hour.toString().padStart(2, '0')}:00 - ${nextHour.toString().padStart(2, '0')}:00`;
  };

  // Format date
  const formatEventDate = (dateStr: string) => {
    return format(parseISO(dateStr), 'EEEE d MMMM', { locale: fr });
  };

  // Get event icon based on type
  const getEventIcon = (event: FinanceEvent) => {
    switch (event.type) {
      case 'sale':
        return event.sale?.paymentMethod === 'card' 
          ? <CreditCardIcon className="h-4 w-4" />
          : <BanknoteIcon className="h-4 w-4" />;
      case 'collection':
        return <TruckIcon className="h-4 w-4 text-blue-500" />;
      case 'manual_in':
        return <ArrowUpIcon className="h-4 w-4 text-green-500" />;
      case 'manual_out':
        return <ArrowDownIcon className="h-4 w-4 text-red-500" />;
      default:
        return <BanknoteIcon className="h-4 w-4" />;
    }
  };


  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BanknoteIcon className="h-5 w-5" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="border rounded-lg overflow-hidden">
      {/* Header - Compact like old convention */}
      <div className="flex items-center justify-between p-1.5 border-b bg-muted/20">
        <div className="flex items-center gap-1">
          <BanknoteIcon className="h-3.5 w-3.5 text-primary" />
          <h3 className="text-sm font-semibold">{title}</h3>
        </div>
        {onRefresh && (
          <Button variant="ghost" size="sm" onClick={onRefresh} className="h-6 w-6 p-0">
            <RefreshCwIcon className="h-3 w-3" />
          </Button>
        )}
      </div>

      <ScrollArea style={{ height: maxHeight }}>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-4 w-4 animate-spin" />
          </div>
        ) : groupedEvents.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground text-sm">
            Aucun événement financier trouvé
          </div>
        ) : (
          <div>
            {groupedEvents.map(({ date, hourGroups }) => {
              const isDayExpanded = expandedDays.has(date);
              const dayTotal = calculateDayTotal(hourGroups);
              const dayEventCount = hourGroups.reduce((sum, { events }) => sum + events.length, 0);

              return (
                <div key={date}>
                  {/* Day header - Blue like old convention */}
                  <button
                    className="w-full bg-blue-50 hover:bg-blue-100 p-1.5 border-b border-blue-200 text-left"
                    onClick={() => toggleDay(date)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1">
                        <ChevronDownIcon className={cn("h-3.5 w-3.5", isDayExpanded && "rotate-180")} />
                        <span className="text-xs font-medium">📅 {formatEventDate(date)}</span>
                        <span className="text-xs text-muted-foreground">
                          {dayEventCount} événement{dayEventCount > 1 ? 's' : ''}
                        </span>
                      </div>
                      <span className={cn(
                        "text-xs font-medium",
                        dayTotal >= 0 ? 'text-green-600' : 'text-red-600'
                      )}>
                        {dayTotal >= 0 ? '+' : ''}{formatCurrency(dayTotal)}
                      </span>
                    </div>
                  </button>

                  {/* Hour groups - Orange like old convention */}
                  {isDayExpanded && (
                    <div>
                      {hourGroups.map(({ hour, events }) => {
                        const groupId = `${date}_${hour}`;
                        const isHourExpanded = expandedHours.has(groupId) ?? true;
                        const summary = calculateHourSummary(events);

                        return (
                          <div key={groupId}>
                            {/* Hour header */}
                            <button
                              className="w-full bg-orange-50 hover:bg-orange-100 px-2 py-1 border-b border-orange-200 text-left"
                              onClick={() => toggleHour(groupId)}
                            >
                              <div className="flex justify-between items-center">
                                <div className="flex items-center gap-1">
                                  <ChevronDownIcon className={cn("h-3 w-3", isHourExpanded && "rotate-180")} />
                                  <ClockIcon className="h-3 w-3" />
                                  <span className="text-xs">{formatHourRange(hour)}</span>
                                  <span className="text-xs text-muted-foreground">
                                    {summary.count} événement{summary.count > 1 ? 's' : ''}
                                  </span>
                                </div>
                                <div className="text-xs">
                                  <span className="font-medium">{formatCurrency(summary.total)}</span>
                                  <span className="ml-1 text-green-600">+{formatCurrency(summary.inflow)}</span>
                                  <span className="ml-1 text-red-600">-{formatCurrency(summary.outflow)}</span>
                                </div>
                              </div>
                            </button>

                            {/* Events list - Compact table format */}
                            {isHourExpanded && (
                              <div className="bg-white">
                                {events.map((event) => (
                                  <EventRow key={event.id} event={event} />
                                ))}
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </ScrollArea>
    </div>
  );
}

// Compact event row component following old table convention
interface EventRowProps {
  event: FinanceEvent;
}

function EventRow({ event }: EventRowProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const eventInfo = getEventInfo(event);
  const eventTime = format(parseISO(event.timestamp), 'HH:mm');

  return (
    <div className="border-b border-gray-100 last:border-b-0">
      {/* Clean, consistent row */}
      <div
        className="flex items-center py-1.5 px-3 hover:bg-muted/20 cursor-pointer text-sm border-b border-gray-100 last:border-b-0"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {/* Event type */}
        <div className="w-[120px] flex-shrink-0">
          <Badge variant="outline" className={cn("text-xs px-2 py-1", eventInfo.typeColor)}>
            {eventInfo.type}
          </Badge>
        </div>

        {/* ID */}
        <div className="w-[60px] flex-shrink-0 font-mono font-bold text-blue-600">
          {eventInfo.id}
        </div>

        {/* Main info */}
        <div className="w-[120px] flex-shrink-0 truncate font-medium">
          {eventInfo.mainInfo}
        </div>

        {/* Sub info */}
        <div className="flex-1 min-w-0 truncate text-muted-foreground px-2">
          {eventInfo.subInfo}
        </div>

        {/* Time */}
        <div className="w-[50px] flex-shrink-0 text-muted-foreground text-xs">
          {eventTime}
        </div>

        {/* Amount */}
        <div className="w-[100px] flex-shrink-0 text-right font-medium">
          <span className={cn(
            // Enhanced color logic for different event types
            event.type === 'sale' ? "text-green-600" :
            event.type === 'collection' ? "text-blue-600" :
            event.type === 'manual_in' ? "text-green-600" :
            event.type === 'manual_out' ? "text-red-600" :
            "text-muted-foreground"
          )}>
            {/* Enhanced sign display logic */}
            {event.type === 'manual_out' ? 
              `-${formatCurrency(Math.abs(eventInfo.amount))}` :
              `+${formatCurrency(Math.abs(eventInfo.amount))}`
            }
          </span>
          {eventInfo.statusIcon && (
            <span className="ml-1">{eventInfo.statusIcon}</span>
          )}
        </div>

        {/* View snapshot for delivery collection */}
        {event.collection?.kind === 'delivery' && (
          <div className="w-[30px] flex-shrink-0 flex justify-center">
            <button className="text-xs text-blue-600 hover:underline flex items-center gap-1" onClick={(e)=>{e.stopPropagation(); setDialogOpen(true);}}>
              <EyeIcon className="h-4 w-4" />
            </button>
          </div>
        )}

        {/* Expand icon */}
        <div className="w-[30px] flex-shrink-0 flex justify-center">
          <ChevronDownIcon className={cn("h-4 w-4 text-muted-foreground", isExpanded && "rotate-180")} />
        </div>
      </div>

      {/* Expanded details */}
      {isExpanded && (
        <div className="px-3 py-1.5 border-t">
          <EventDetails event={event} />
        </div>
      )}

      {/* Snapshot dialog (view-only) */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Delivery Collection Snapshot</DialogTitle>
          </DialogHeader>
          <div className="mt-2">
            <CollectionDetails event={event} />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

function SaleDetails({ event }: { event: FinanceEvent }) {
  if (!event.sale) return null;

  return (
    <div className="p-2 rounded-md text-xs bg-muted/10">
      <div className="grid grid-cols-[86px_1fr] gap-x-2 gap-y-1 items-start">
        {event.sale.items && (
          <>
            <div className="text-muted-foreground">Items</div>
            <div className="truncate">{event.sale.items}</div>
          </>
        )}
        <div className="text-muted-foreground">Payment</div>
        <div>
          <Badge
            variant={event.sale.paymentMethod === 'card' ? 'default' : 'secondary'}
            className="px-1.5 py-0.5 text-[10px]"
          >
            {event.sale.paymentMethod === 'card' ? 'Card' : 'Cash'}
          </Badge>
        </div>
        {event.sale.orderType === 'delivery' && event.sale.collectionStatus && (
          <>
            <div className="text-muted-foreground">Collection</div>
            <div>
              <Badge
                variant={event.sale.collectionStatus === 'collected' ? 'default' : 'secondary'}
                className="px-1.5 py-0.5 text-[10px]"
              >
                {getCollectionStatusIcon(event.sale.collectionStatus)} {event.sale.collectionStatus}
              </Badge>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

function CollectionDetails({ event }: { event: FinanceEvent }) {
  if (!event.collection) return null;

  const isCaisse = event.collection?.kind === 'caisse';

  return (
    <div className="bg-muted/10 p-2 rounded-md text-xs space-y-2">
      {isCaisse ? (
        <div className="grid grid-cols-[108px_1fr] gap-x-2 gap-y-1">
          <div className="text-muted-foreground">Validated by</div>
          <div>{event.collection?.validatedBy || event.performedBy}</div>
          <div className="text-muted-foreground">Amount counted</div>
          <div className="font-medium">{formatCurrency(event.collection?.netAmount || 0)}</div>
          {event.collection?.comments && (
            <>
              <div className="text-muted-foreground">Notes</div>
              <div className="whitespace-pre-wrap break-words">{event.collection.comments}</div>
            </>
          )}
        </div>
      ) : (
        <>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Gross Collected:</span>
              <span className="font-medium">{formatCurrency(event.collection?.grossAmount || 0)}</span>
            </div>
            <div className="flex justify-between">
              <span>Driver Fee:</span>
              <span className="text-red-600">-{formatCurrency(event.collection?.driverFee || 0)}</span>
            </div>
            <div className="flex justify-between font-medium border-t pt-1">
              <span>Net to Drawer:</span>
              <span className="text-green-600">+{formatCurrency(event.collection?.netAmount || 0)}</span>
            </div>
          </div>

          {event.collection?.orders && (
            <div>
              <div className="font-medium mb-2">Orders Collected ({event.collection?.orderCount || 0}):</div>
              <div className="space-y-1">
                {event.collection.orders.map((order, index) => (
                  <div key={index} className="flex justify-between text-xs bg-white p-1 rounded">
                    <span className="font-mono text-blue-600">#{order.orderId.split('-').pop()}</span>
                    <span className="truncate mx-2 flex-1">{order.customer}</span>
                    <span>{formatCurrency(order.amount)}</span>
                    <span className="ml-1">✅</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}

// Relevant details only
function EventDetails({ event }: { event: FinanceEvent }) {
  if (event.sale) {
    return <SaleDetails event={event} />;
  }

  if (event.collection) {
    return <CollectionDetails event={event} />;
  }

  if (event.manualTransaction) {
    return (
      <div className="p-2 rounded-md text-xs bg-muted/10">
        <div className="flex justify-between">
          <div className="text-muted-foreground">Details</div>
          <div className="truncate">{event.manualTransaction.reason}</div>
        </div>
        {event.manualTransaction.notes && (
          <>
            <div className="text-muted-foreground">Notes</div>
            <div className="whitespace-pre-wrap break-words">{event.manualTransaction.notes}</div>
          </>
        )}
      </div>
    );
  }

  return null;
}
