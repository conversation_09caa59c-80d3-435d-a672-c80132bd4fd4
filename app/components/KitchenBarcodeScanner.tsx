'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  Clock, 
  Scan, 
  AlertCircle, 
  Package,
  Printer,
  RefreshCw,
  ChefHat
} from 'lucide-react';
import { kitchenPrintService } from '@/lib/services/kitchen-print-service';
import { useToast } from '@/components/ui/use-toast';
import { simpleHTMLPrintService } from '@/lib/services/simple-html-print';


interface ScanHistoryItem {
  barcode: string;
  timestamp: string;
  success: boolean;
  message: string;
}

export default function KitchenBarcodeScanner() {
  const [barcodeInput, setBarcodeInput] = useState('');
  const [isScanning, setIsScanning] = useState(false);
  const [scanHistory, setScanHistory] = useState<ScanHistoryItem[]>([]);
  
  
  const inputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Auto-focus input on mount
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  // Handle barcode scan - UPDATED for async queue service
  const handleBarcodeScan = async (barcode: string) => {
    if (!barcode.trim()) return;
    
    setIsScanning(true);
    
    try {
      console.log('🔍 Scanning barcode:', barcode);
      
      // 🎯 UPDATED: Use async version of scanItemBarcode
      const result = await kitchenPrintService.scanItemBarcode(barcode);
      
      // Add to scan history
      setScanHistory(prev => [{
        barcode,
        timestamp: new Date().toISOString(),
        success: result.success,
        message: result.message
      }, ...prev.slice(0, 9)]); // Keep last 10 scans
      
      if (result.success) {
        toast({
          title: "✅ Item Completed",
          description: result.message,
        });
        
        // If order is ready for expo, print expo ticket
        if (result.shouldPrintExpo) {
          const orderId = barcode.split('-')[0]; // Extract order ID from barcode
          const expoTicket = kitchenPrintService.generateExpoTicket(orderId);
          
          if (expoTicket) {
            simpleHTMLPrintService.print(expoTicket);
            
            toast({
              title: "🍽️ Order Ready!",
              description: "All items completed. Expo ticket sent to printer.",
            });
          }
        }
      } else {
        toast({
          title: "❌ Scan Failed",
          description: result.message,
          variant: "destructive"
        });
      }
      
    } catch (error: any) {
      console.error('Error processing barcode:', error);
      
      // Add to scan history
      setScanHistory(prev => [{
        barcode,
        timestamp: new Date().toISOString(),
        success: false,
        message: error.message || 'Unknown error'
      }, ...prev.slice(0, 9)]);
      
      toast({
        title: "❌ Error",
        description: error.message || 'Failed to process barcode',
        variant: "destructive"
      });
    } finally {
      setIsScanning(false);
      setBarcodeInput('');
      
      // Refocus input
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 100);
    }
  };

  // Handle form submit
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleBarcodeScan(barcodeInput);
  };

  // Clear scan history
  const clearHistory = () => {
    setScanHistory([]);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <ChefHat className="h-8 w-8" />
            Kitchen Barcode Scanner
          </h1>
          <p className="text-muted-foreground">Scan item barcodes to mark them as complete</p>
        </div>
      </div>

      {/* Scanner Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Scan className="h-5 w-5" />
            Barcode Scanner
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="flex gap-2">
              <Input
                ref={inputRef}
                value={barcodeInput}
                onChange={(e) => setBarcodeInput(e.target.value)}
                placeholder="Scan or enter barcode..."
                className="flex-1 text-lg font-mono"
                disabled={isScanning}
              />
              <Button type="submit" disabled={isScanning || !barcodeInput.trim()}>
                {isScanning ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    Scanning...
                  </>
                ) : (
                  <>
                    <Scan className="h-4 w-4 mr-2" />
                    Scan
                  </>
                )}
              </Button>
            </div>
            
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                📱 Point your barcode scanner at the item barcode or manually enter the barcode above.
              </AlertDescription>
            </Alert>
          </form>
        </CardContent>
      </Card>

      {/* Scan History */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Recent Scans
            </CardTitle>
            {scanHistory.length > 0 && (
              <Button variant="outline" size="sm" onClick={clearHistory}>
                Clear History
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {scanHistory.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No scans yet. Start scanning item barcodes to see history here.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {scanHistory.map((scan, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {scan.success ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-500" />
                    )}
                    <div>
                      <div className="font-mono text-sm">{scan.barcode}</div>
                      <div className="text-sm text-muted-foreground">{scan.message}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={scan.success ? "default" : "destructive"}>
                      {scan.success ? "Success" : "Failed"}
                    </Badge>
                    <div className="text-xs text-muted-foreground mt-1">
                      {new Date(scan.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            How to Use
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="flex items-start gap-2">
              <span className="font-bold text-primary">1.</span>
              <span>When you finish preparing an item, scan its barcode from the kitchen ticket.</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="font-bold text-primary">2.</span>
              <span>The system will mark the item as complete and track progress.</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="font-bold text-primary">3.</span>
              <span>When all items for an order are scanned, an expo ticket will automatically print.</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="font-bold text-primary">4.</span>
              <span>The expo team can then assemble and serve the complete order.</span>
            </div>
          </div>
        </CardContent>
      </Card>

      
    </div>
  );
}