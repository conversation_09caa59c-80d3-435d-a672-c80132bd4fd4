// knowledge:payment-form-v3:start
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Order } from '@/lib/db/v4/schemas/order-schema';
import { Clock, X, Calendar, User, CreditCard, Beaker, Trash2, Percent, DollarSign } from 'lucide-react';
import { format } from 'date-fns';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

import { kitchenPrintService } from '@/lib/services/kitchen-print-service';
import { updateOrder } from '@/lib/db/v4/operations/order-ops';
import { explodeMenuItemToStockConsumption, addWasteLog } from '@/lib/db/v4/operations/inventory-ops';
import { createCashTransaction } from '@/lib/db/v4';
import { toast } from 'sonner';

interface PaymentFormProps {
  order: Order;
  onSubmit: (receivedAmount: number, wasteData?: WasteProcessingData, discountData?: DiscountData) => void;
  onCancel: () => void;
  isProcessing?: boolean;
  errorMsg?: string;
}

interface WasteItem {
  itemIndex: number;
  quantity: number;
}

// 🚀 NEW: Interface for passing waste data to payment processor
export interface WasteProcessingData {
  wasteItems: WasteItem[];
  wasteReason: string;
  totalWasteValue: number;
  wastedMenuItems: Array<{
    name: string;
    quantity: number;
    originalQuantity: number;
    price: number;
    addons?: any[];
    menuItemId?: string; // 🚀 Add menuItemId for cost calculation
  }>;
}

// 💸 NEW: Interface for passing discount data to payment processor
export interface DiscountData {
  discountType: 'percentage' | 'fixed_amount';
  discountValue: number;
  discountAmount: number;
  discountReason: string;
  subtotal: number;
}

export default function PaymentForm({ order, onSubmit, onCancel, isProcessing, errorMsg }: PaymentFormProps) {
  // removed two-step confirmation state: we now submit immediately on click
  const [wasteItems, setWasteItems] = useState<WasteItem[]>([]);
  const [wasteReason, setWasteReason] = useState('');
  const [showWasteMode, setShowWasteMode] = useState(false);

  // 💸 NEW: Discount state - showDiscountMode is removed as discount UI is always visible
  const [discountType, setDiscountType] = useState<'percentage' | 'fixed_amount'>('fixed_amount');
  const [discountValue, setDiscountValue] = useState<number>(0);
  const [discountReason, setDiscountReason] = useState('');

  // Calculate adjusted totals with waste and discount
  const wastedAmount = wasteItems.reduce((total, wasteItem) => {
    const item = order.items[wasteItem.itemIndex];
    if (!item) return total;

    let itemPrice = item.price;
    if (item.addons && item.addons.length > 0) {
      itemPrice += item.addons.reduce((sum, addon) => sum + addon.price, 0);
    }

    return total + (itemPrice * wasteItem.quantity);
  }, 0);

  // Calculate subtotal (after waste but before discount)
  const subtotalAfterWaste = order.total - wastedAmount;

  // Calculate discount amount
  let discountAmount = 0;
  if (discountValue > 0) {
    if (discountType === 'percentage') {
      discountAmount = (subtotalAfterWaste * discountValue) / 100;
    } else if (discountType === 'fixed_amount') {
      discountAmount = Math.min(discountValue, subtotalAfterWaste); // Can't discount more than subtotal
    }
  }

  // Final total after waste and discount
  const adjustedTotal = Math.max(0, subtotalAfterWaste - discountAmount);

  const received = adjustedTotal; // Always exact amount
  const change = 0; // No change needed

  


  const handleWasteItemChange = (itemIndex: number, checked: boolean, qtyIndex?: number) => {
    if (checked) {
      setWasteItems(prev => {
        const existing = prev.find(w => w.itemIndex === itemIndex);
        if (existing) {
          return prev.map(w => 
            w.itemIndex === itemIndex 
              ? { ...w, quantity: w.quantity + 1 }
              : w
          );
        } else {
          return [...prev, { itemIndex, quantity: 1 }];
        }
      });
    } else {
      setWasteItems(prev => {
        const existing = prev.find(w => w.itemIndex === itemIndex);
        if (existing && existing.quantity > 1) {
          return prev.map(w => 
            w.itemIndex === itemIndex 
              ? { ...w, quantity: w.quantity - 1 }
              : w
          );
        } else {
          return prev.filter(w => w.itemIndex !== itemIndex);
        }
      });
    }
  };

  // 🚀 NEW: Modified handleSubmit to pass waste data to payment processor
  const handleSubmit = () => {
    if (isProcessing) return;

    // Validate discount if applied
    if (discountValue > 0) {
      // Optional: still validate basic numeric rules
      if (discountValue <= 0) {
        toast.error('La valeur de la remise doit être supérieure à 0');
        return;
      }
      if (discountType === 'percentage' && discountValue > 100) {
        toast.error('La remise ne peut pas dépasser 100%');
        return;
      }
    }

    let wasteData: WasteProcessingData | undefined;

    // If there are waste items, prepare the waste data
    if (wasteItems.length > 0) {

      // Prepare waste data for processing after payment
      wasteData = {
        wasteItems,
        wasteReason: wasteReason.trim(),
        totalWasteValue: wastedAmount,
        wastedMenuItems: wasteItems.map(w => {
          const item = order.items[w.itemIndex];
          return {
            name: item.name,
            quantity: w.quantity,
            originalQuantity: item.quantity,
            price: item.price,
            addons: item.addons,
            menuItemId: item.menuItemId // 🚀 Add menuItemId for cost calculation
          };
        })
      };
    }

    // 💸 NEW: Prepare discount data
    const discountData = discountValue > 0 ? {
      discountType,
      discountValue,
      discountAmount,
      discountReason: discountReason.trim(),
      subtotal: subtotalAfterWaste
    } : undefined;

    // Call onSubmit with received amount, waste data, and discount data
    onSubmit(received, wasteData, discountData);
  };


  

  const isItemWasted = (itemIndex: number, qtyIndex?: number) => {
    const wasteItem = wasteItems.find(w => w.itemIndex === itemIndex);
    return wasteItem ? wasteItem.quantity > (qtyIndex || 0) : false;
  };

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Fixed Header */}
      <div className="flex items-center justify-between px-5 py-3 border-b bg-muted/30 shrink-0">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
            <Clock className="h-3.5 w-3.5" />
            {format(new Date(order.createdAt), 'HH:mm')}
          </div>
          <Separator orientation="vertical" className="h-4" />
          <span className="text-sm font-semibold">#{(order.id || order._id).slice(-6)}</span>
          <Badge variant="secondary" className="text-xs px-2 py-0.5">{order.status}</Badge>
        </div>
      </div>

      <div className="flex-1 grid grid-cols-2 min-h-0 overflow-hidden">
        {/* Left Panel - Order Details */}
        <div className="flex flex-col h-full border-r overflow-hidden">
          {/* Fixed Customer Info */}
          <div className="px-5 py-2.5 bg-muted/20 border-b shrink-0">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div className="flex items-center gap-1.5">
                <User className="h-3.5 w-3.5" />
                {order.customer?.name || 'Client'}
              </div>
              <div className="flex items-center gap-1.5">
                <Calendar className="h-3.5 w-3.5" />
                {format(new Date(order.createdAt), 'dd/MM/yyyy')}
              </div>
            </div>
          </div>

          {/* Scrollable Items List Only */}
          <div className="flex-1 overflow-y-auto min-h-0 max-h-[270px]">
            <div className="p-5 space-y-3">
              {order.items.flatMap((item, itemIdx) => 
                Array.from({ length: item.quantity }, (_, qtyIdx) => {
                  const uniqueIdx = `${itemIdx}-${qtyIdx}`;
                  const isWasted = isItemWasted(itemIdx, qtyIdx);
                  const totalItemPrice = item.price + (item.addons?.reduce((sum, addon) => sum + addon.price, 0) || 0);

                  return (
                    <div key={uniqueIdx} className={`flex items-start gap-3 ${isWasted ? 'opacity-50' : ''}`}>
                      {showWasteMode && (
                        <Checkbox
                          checked={isWasted}
                          onCheckedChange={(checked) => handleWasteItemChange(itemIdx, checked as boolean, qtyIdx)}
                          disabled={isProcessing}
                          className="mt-1 data-[state=checked]:bg-destructive data-[state=checked]:border-destructive"
                        />
                      )}

                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-xs font-semibold text-primary">
                        1
                      </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-1.5 mb-0.5">
                        <h4 className="font-medium text-sm">{item.name}</h4>
                        {isWasted && (
                          <Badge variant="destructive" className="text-xs px-1.5 py-0.5">
                            WASTE
                          </Badge>
                        )}
                      </div>

                      {item.addons && item.addons.length > 0 && (
                        <div className="space-y-0.5 mb-1">
                          {item.addons.map((addon, addonIdx) => (
                            <div key={addonIdx} className="flex items-center gap-1.5 text-xs text-muted-foreground">
                              <Beaker className="h-2.5 w-2.5" />
                              <span>{addon.name}</span>
                              {addon.price > 0 && (
                                <span className="font-medium">+{addon.price} DA</span>
                              )}
                            </div>
                          ))}
                        </div>
                      )}

                      <div className="text-xs text-muted-foreground">
                        1 × {totalItemPrice} DA
                      </div>
                    </div>

                      <div className="text-right">
                        <div className="text-base font-semibold">
                          {totalItemPrice} DA
                        </div>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </div>

          {/* Fixed Sections Below Items */}
          <div className="shrink-0">

            {/* 🚀 NEW: Waste Selection Panel - Shows selected items and reason */}
            {showWasteMode && (
              <div className="border-t bg-destructive/5 p-5">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Trash2 className="h-4 w-4 text-destructive" />
                    <h3 className="font-medium text-sm text-destructive">Select Items for Waste</h3>
                  </div>

                  <Textarea
                    placeholder="Reason for waste (optional)..."
                    value={wasteReason}
                    onChange={(e) => setWasteReason(e.target.value)}
                    className="min-h-[70px] text-sm"
                    disabled={isProcessing}
                  />

                  {wasteItems.length > 0 && (
                    <div className="space-y-2">
                      <div className="flex justify-between items-center p-2.5 bg-destructive/10 rounded-md">
                        <span className="text-xs font-medium text-destructive">
                          {wasteItems.length} item(s) selected for waste
                        </span>
                        <span className="font-semibold text-destructive">-{wastedAmount} DA</span>
                      </div>

                      <div className="text-xs text-muted-foreground bg-amber-50 border border-amber-200 rounded-md p-2">
                        💡 Waste will be processed automatically after payment completion
                      </div>
                    </div>
                  )}

                  <Button
                    variant="outline"
                    onClick={() => {
                      setWasteItems([]);
                      setWasteReason('');
                      setShowWasteMode(false);
                    }}
                    disabled={isProcessing}
                    className="w-full h-9 text-sm"
                  >
                    Done Selecting
                  </Button>
                </div>
              </div>
            )}


            {/* Fixed Bottom Section - Waste Toggle Button */}
            {!showWasteMode && (
              <div className="p-5 border-t">
                <Button
                  variant="outline"
                  onClick={() => setShowWasteMode(true)}
                  disabled={isProcessing}
                  className="w-full h-9 text-sm"
                >
                  <Trash2 className="h-3.5 w-3.5 mr-1.5" />
                  Mark Items as Waste
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - Payment */}
        <div className="flex flex-col h-full overflow-hidden">
          {/* Fixed Amount Due Header */}
          <div className="p-3 text-center border-b shrink-0">
            <p className="text-xs text-muted-foreground">Amount Due</p>

            {/* Show breakdown if there's waste or discount */}
            {(wastedAmount > 0 || discountAmount > 0) && (
              <div className="space-y-0.5 mb-1">
                <p className="text-xs text-muted-foreground line-through">
                  Original: {order.total} DA
                </p>
                {wastedAmount > 0 && (
                  <p className="text-xs text-destructive">
                    💸 Waste: -{wastedAmount} DA
                  </p>
                )}
                {discountAmount > 0 && (
                  <p className="text-xs text-blue-600">
                    💰 Discount: -{discountAmount.toFixed(0)} DA
                    {discountType === 'percentage' ? ` (${discountValue}%)` : ''}
                  </p>
                )}
              </div>
            )}

            <p className="text-2xl font-bold tracking-tight">{adjustedTotal} DA</p>
          </div>

          {/* Compact Discount Section */}
          <div className="flex-1 p-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                <Percent className="h-4 w-4" />
                Apply Discount
              </div>

              <div className="grid grid-cols-2 gap-2">
                <Select
                  value={discountType}
                  onValueChange={(value: 'percentage' | 'fixed_amount') => {
                    setDiscountType(value);
                  }}
                  disabled={isProcessing}
                >
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="percentage">%</SelectItem>
                    <SelectItem value="fixed_amount">DA</SelectItem>
                  </SelectContent>
                </Select>

                <div className="relative">
                  <Input
                    type="number"
                    placeholder="Amount"
                    value={discountValue || ''}
                    onChange={(e) => setDiscountValue(parseFloat(e.target.value) || 0)}
                    disabled={isProcessing}
                    className="h-8 text-xs pr-8 text-right"
                    min="0"
                    max={discountType === 'percentage' ? 100 : subtotalAfterWaste}
                    step={discountType === 'percentage' ? 1 : 10}
                  />
                  <div className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
                    {discountType === 'percentage' ? '%' : 'DA'}
                  </div>
                </div>
              </div>

              <Textarea
                placeholder="Reason (optional)..."
                value={discountReason}
                onChange={(e) => setDiscountReason(e.target.value)}
                className="min-h-[50px] text-xs"
                disabled={isProcessing}
              />

              {discountValue > 0 && discountAmount > 0 && (
                <div className="p-2 bg-blue-50/50 rounded text-xs">
                  <div className="flex justify-between font-medium text-blue-700">
                    <span>{discountType === 'percentage' ? `${discountValue}%` : `${discountValue} DA`}</span>
                    <span>-{discountAmount.toFixed(0)} DA</span>
                  </div>
                  <div className="text-muted-foreground mt-1">
                    New total: {adjustedTotal} DA
                  </div>
                </div>
              )}
              
              {errorMsg && (
                <div className="text-xs text-destructive bg-destructive/10 p-2 rounded">
                  {errorMsg}
                </div>
              )}
            </div>
          </div>

          {/* Fixed Actions at Bottom */}
          <div className="p-3 border-t bg-background shrink-0">
            <div className="space-y-1.5">
              <Button
                onClick={handleSubmit}
                disabled={isProcessing}
                className="w-full h-11 text-lg font-semibold bg-green-600 hover:bg-green-700"
                size="lg"
              >
                {isProcessing ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    Processing...
                  </>
                ) : (
                  "Confirm Payment"
                )}
              </Button>

              <Button
                variant="outline"
                onClick={onCancel}
                disabled={isProcessing}
                className="w-full h-9 text-sm"
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      </div>

      
    </div>
  );
}
// knowledge:payment-form-v3:end