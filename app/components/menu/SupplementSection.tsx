"use client";

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, X, Check, Package, Beaker, PencilIcon, TrashIcon } from "lucide-react";
import { Supplement } from "@/lib/db/v4/schemas/menu-schema";
import { CategorySupplementConfig } from '@/components/menu/GlobalSupplementConfig';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSupplements } from '@/lib/hooks/useSupplements';

interface SupplementSectionProps {
    categoryId: string;
    availableSizes: string[];
    stockItems: any[];
    refreshCategories: () => Promise<void>;
}

export function SupplementSection({
    categoryId,
    availableSizes,
    stockItems,
    refreshCategories,
}: SupplementSectionProps) {
    const [isAddingSupplement, setIsAddingSupplement] = useState(false);
    const [editingSupplementId, setEditingSupplementId] = useState<string | null>(null);
    
    // Use the supplements hook with categoryId - this is the NEW way
    const { 
        supplements, 
        isLoading,
        error,
        isReady,
        createSupplement: createSupplementFn, 
        updateSupplement: updateSupplementFn, 
        deleteSupplement: deleteSupplementFn,
        refreshSupplements 
    } = useSupplements(categoryId);
    
    // State for adding new supplement
    const [newSupplement, setNewSupplement] = useState<{
        name: string;
        stockConsumption?: {
            stockItemId: string;
            quantities: { [sizeName: string]: number };
        };
    }>({ name: "" });

    // State for editing supplement
    const [editedSupplement, setEditedSupplement] = useState<{
        name: string;
        stockConsumption?: {
            stockItemId: string;
            quantities: { [sizeName: string]: number };
        };
    }>({ name: "" });

    // Handle adding a supplement - NEW way using supplements hook
    const handleAddSupplement = async () => {
        try {
            if (!newSupplement.name) {
                return;
            }

            // Helper: only include stockConsumption if it's meaningfully configured
            const isStockConsumptionConfigured = (sc?: { stockItemId: string; quantities: { [k: string]: number } }) => {
                return !!sc && !!sc.stockItemId && Object.values(sc.quantities || {}).some(q => q > 0);
            };

            // Create supplement as its own entity, NOT as a menu item
            await createSupplementFn({
                name: newSupplement.name,
                stockConsumption: isStockConsumptionConfigured(newSupplement.stockConsumption) ? newSupplement.stockConsumption : undefined,
                isActive: true
            });
            
            setNewSupplement({ name: "" });
            setIsAddingSupplement(false);
            // Refresh both supplements and categories
            await refreshSupplements();
            await refreshCategories();
        } catch (error) {
            console.error("Error adding supplement:", error);
        }
    };

    // Start editing a supplement
    const startEditingSupplement = (supplement: Supplement) => {
        setEditingSupplementId(supplement.id);
        setEditedSupplement({
            name: supplement.name,
            stockConsumption: supplement.stockConsumption || undefined
        });
    };

    // Cancel supplement edit
    const cancelSupplementEdit = () => {
        setEditingSupplementId(null);
        setEditedSupplement({ name: "" });
    };

    // Save supplement edit - NEW way using supplements hook
    const saveSupplementEdit = async () => {
        if (editingSupplementId && editedSupplement.name) {
            try {
                // Update supplement as its own entity, NOT as a menu item
                const isStockConsumptionConfigured = (sc?: { stockItemId: string; quantities: { [k: string]: number } }) => {
                    return !!sc && !!sc.stockItemId && Object.values(sc.quantities || {}).some(q => q > 0);
                };

                await updateSupplementFn(editingSupplementId, {
                    name: editedSupplement.name,
                    stockConsumption: isStockConsumptionConfigured(editedSupplement.stockConsumption) ? editedSupplement.stockConsumption : undefined
                });
                cancelSupplementEdit();
                await refreshSupplements();
                await refreshCategories();
            } catch (error) {
                console.error('❌ Error updating supplement:', error);
            }
        }
    };

    // Handle delete supplement click - NEW way using supplements hook
    const handleDeleteSupplementClick = async (supplementId: string) => {
        try {
            if (window.confirm('Êtes-vous sûr de vouloir supprimer ce supplément?')) {
                // Delete supplement as its own entity, NOT as a menu item
                await deleteSupplementFn(supplementId);
                await refreshSupplements();
                await refreshCategories();
            }
        } catch (error) {
            console.error('❌ Error deleting supplement:', error);
        }
    };

    return (
        <Card className="overflow-hidden">
            <CardHeader className="py-2 px-3">
                <CardTitle className="text-sm">Suppléments</CardTitle>
            </CardHeader>
            <CardContent className="py-2 px-3 space-y-2">
                {/* Global Supplement Configuration */}
                {availableSizes && availableSizes.length > 0 && (
                    <CategorySupplementConfig
                        categoryId={categoryId}
                        availableSizes={availableSizes}
                        onConfigUpdate={refreshCategories}
                    />
                )}

                {/* Supplement Management Container */}
                <div className="border rounded-lg bg-muted/10 overflow-hidden">
                    {/* Header */}
                    <div className="flex items-center justify-between px-3 py-2 bg-muted/20 border-b">
                        <div className="flex items-center gap-2">
                            <Beaker className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm font-medium">Suppléments de la Catégorie</span>
                            {/* Debug info */}
                            {(isLoading || error || !isReady) && (
                                <Badge variant="outline" className="text-xs">
                                    {isLoading ? '⏳ Chargement...' : error ? '❌ Erreur' : !isReady ? '⚠️ Pas prêt' : '✅ Prêt'}
                                </Badge>
                            )}
                        </div>
                        <div className="flex gap-1">
                            <Button
                                variant="outline"
                                size="sm"
                                className="h-7 px-2"
                                onClick={() => setIsAddingSupplement(true)}
                                disabled={!isReady}
                            >
                                <Plus className="h-3 w-3 mr-1" />
                                Ajouter
                            </Button>
                        </div>
                    </div>
                    
                    {/* Content */}
                    <div className="p-3">
                        {/* Show loading state */}
                        {isLoading && (
                            <div className="text-center text-muted-foreground text-sm py-4">
                                <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent mx-auto mb-2"></div>
                                Chargement des suppléments...
                            </div>
                        )}

                        {/* Show error state */}
                        {error && (
                            <div className="text-center text-red-600 text-sm py-4">
                                ❌ Erreur: {error.message}
                            </div>
                        )}

                        {/* Show content when ready */}
                        {isReady && !isLoading && !error && (
                            <>
                                {isAddingSupplement ? (
                                    <div className="space-y-3">
                                        {/* Supplement name */}
                                        <div>
                                            <Label className="text-xs text-muted-foreground mb-1 block">Nom</Label>
                                            <Input
                                                value={newSupplement.name}
                                                onChange={(e) => setNewSupplement({...newSupplement, name: e.target.value})}
                                                placeholder="Nom du supplément"
                                                className="h-6 text-xs"
                                                autoFocus
                                            />
                                        </div>

                                        {/* Stock consumption per size */}
                                        <div className="space-y-2">
                                            <Label className="text-xs text-muted-foreground mb-1 block">Article en stock</Label>
                                            <Select
                                                value={newSupplement.stockConsumption?.stockItemId || ''}
                                                onValueChange={(value) => setNewSupplement(prev => ({
                                                    ...prev,
                                                    stockConsumption: {
                                                        ...(prev.stockConsumption || { quantities: {} }),
                                                        stockItemId: value,
                                                    }
                                                }))}
                                            >
                                                <SelectTrigger className="h-8 text-xs">
                                                    <SelectValue placeholder="Sélectionner un article" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {stockItems.length > 0 ? (stockItems.map(item => (
                                                        <SelectItem key={item.id} value={item.id}>
                                                            {item.name} ({item.unit})
                                                        </SelectItem>
                                                    ))) : (
                                                        <SelectItem value="no-stock" disabled>
                                                            Aucun article de stock disponible
                                                        </SelectItem>
                                                    )}
                                                </SelectContent>
                                            </Select>

                                            {availableSizes && availableSizes.length > 0 && (
                                                <div className="space-y-2">
                                                    <Label className="text-xs text-muted-foreground mb-1 block">Quantités par taille</Label>
                                                    <div className="grid grid-cols-2 gap-2">
                                                        {availableSizes.map(size => (
                                                            <div key={size} className="flex items-center gap-2">
                                                                <Label className="text-xs min-w-[50px]">{size}:</Label>
                                                                <Input
                                                                    type="number"
                                                                    min="0"
                                                                    step="0.01"
                                                                    value={newSupplement.stockConsumption?.quantities[size] || ''}
                                                                    onChange={(e) => setNewSupplement(prev => ({
                                                                        ...prev,
                                                                        stockConsumption: {
                                                                            ...(prev.stockConsumption || { stockItemId: newSupplement.stockConsumption?.stockItemId || '', quantities: {} }),
                                                                            quantities: {
                                                                                ...(prev.stockConsumption?.quantities || {}),
                                                                                [size]: parseFloat(e.target.value) || 0
                                                                            }
                                                                        }
                                                                    }))}
                                                                    placeholder="0"
                                                                    className="h-6 text-xs"
                                                                />
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            )}
                                        </div>

                                        {/* Action buttons */}
                                        <div className="flex justify-end gap-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="h-7 px-3 text-xs"
                                                onClick={() => {
                                                    setIsAddingSupplement(false);
                                                    setNewSupplement({ name: "" });
                                                }}
                                            >
                                                <X className="h-3 w-3 mr-1" />
                                                Annuler
                                            </Button>
                                            <Button
                                                size="sm"
                                                className="h-7 px-3 text-xs"
                                                onClick={handleAddSupplement}
                                                disabled={!newSupplement.name}
                                            >
                                                <Check className="h-3 w-3 mr-1" />
                                                Ajouter
                                            </Button>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="space-y-2">
                                        {supplements.length === 0 ? (
                                            <div className="text-center text-muted-foreground text-sm py-4">
                                                <Beaker className="h-8 w-8 mx-auto mb-2 text-muted-foreground/50" />
                                                <p>Aucun supplément configuré.</p>
                                                <p className="text-xs mt-1">Cliquez sur "Ajouter" pour créer votre premier supplément.</p>
                                            </div>
                                        ) : (
                                            <div className="grid grid-cols-1 gap-2">
                                                {supplements.map(supplement => (
                                                    <div key={supplement.id} className={`group border rounded-md p-2 bg-background hover:bg-muted/50 transition-colors shadow-sm ${editingSupplementId === supplement.id ? 'space-y-2' : 'flex items-center justify-between'}`}>
                                                        {editingSupplementId === supplement.id ? (
                                                            // Full editing mode - takes full width
                                                            <div className="w-full space-y-2">
                                                                {/* Name editing */}
                                                                <div>
                                                                    <Label className="text-xs text-muted-foreground mb-1 block">Nom</Label>
                                                                    <Input
                                                                        value={editedSupplement.name}
                                                                        onChange={(e) => setEditedSupplement({...editedSupplement, name: e.target.value})}
                                                                        placeholder="Nom du supplément"
                                                                        className="h-7 text-xs"
                                                                        autoFocus
                                                                    />
                                                                </div>
                                                                
                                                                {/* Stock item selection */}
                                                                <div>
                                                                    <Label className="text-xs text-muted-foreground mb-1 block">Article en stock</Label>
                                                                    <Select 
                                                                        value={editedSupplement.stockConsumption?.stockItemId || ""} 
                                                                        onValueChange={(value) => setEditedSupplement(prev => ({
                                                                            ...prev,
                                                                            stockConsumption: {
                                                                                ...(prev.stockConsumption || { stockItemId: "", quantities: {} }),
                                                                                stockItemId: value
                                                                            }
                                                                        }))}
                                                                    >
                                                                        <SelectTrigger className="h-7 text-xs">
                                                                            <SelectValue placeholder="Sélectionner un article" />
                                                                        </SelectTrigger>
                                                                        <SelectContent>
                                                                            {stockItems.map(item => (
                                                                                <SelectItem key={item.id} value={item.id}>
                                                                                    {item.name} ({item.unit})
                                                                                </SelectItem>
                                                                            ))}
                                                                        </SelectContent>
                                                                    </Select>
                                                                </div>
                                                                
                                                                {/* Quantities per size editing */}
                                                                {availableSizes && availableSizes.length > 0 && (
                                                                    <div>
                                                                        <Label className="text-xs text-muted-foreground mb-1 block">Quantités par taille</Label>
                                                                        <div className="grid grid-cols-2 gap-2">
                                                                            {availableSizes.map(size => (
                                                                                <div key={size} className="flex items-center gap-2">
                                                                                    <Label className="text-xs min-w-[50px]">{size}:</Label>
                                                                                    <Input
                                                                                        type="number"
                                                                                        min="0"
                                                                                        step="0.01"
                                                                                        value={editedSupplement.stockConsumption?.quantities[size] || ''}
                                                                                        onChange={(e) => setEditedSupplement(prev => ({
                                                                                            ...prev,
                                                                                            stockConsumption: {
                                                                                                ...(prev.stockConsumption || { stockItemId: editedSupplement.stockConsumption?.stockItemId || '', quantities: {} }),
                                                                                                quantities: {
                                                                                                    ...(prev.stockConsumption?.quantities || {}),
                                                                                                    [size]: parseFloat(e.target.value) || 0
                                                                                                }
                                                                                            }
                                                                                        }))}
                                                                                        placeholder="0"
                                                                                        className="h-6 text-xs"
                                                                                    />
                                                                                </div>
                                                                            ))}
                                                                        </div>
                                                                    </div>
                                                                )}
                                                                
                                                                {/* Action buttons */}
                                                                <div className="flex justify-end gap-1 mt-2">
                                                                    <Button
                                                                        variant="outline"
                                                                        size="sm"
                                                                        className="h-7 w-7 p-0"
                                                                        onClick={saveSupplementEdit}
                                                                        disabled={!editedSupplement.name}
                                                                    >
                                                                        <Check className="h-3.5 w-3.5 text-green-600" />
                                                                    </Button>
                                                                    <Button
                                                                        variant="outline"
                                                                        size="sm"
                                                                        className="h-7 w-7 p-0"
                                                                        onClick={cancelSupplementEdit}
                                                                    >
                                                                        <X className="h-3.5 w-3.5 text-red-600" />
                                                                    </Button>
                                                                </div>
                                                            </div>
                                                        ) : (
                                                            // Normal display mode - flex layout
                                                            <>
                                                                <div className="flex items-center gap-2">
                                                                    <Beaker className="h-4 w-4 text-muted-foreground" />
                                                                    <span className="font-medium text-sm">{supplement.name}</span>
                                                                    {supplement.stockConsumption?.stockItemId && (
                                                                        <span className="text-xs text-muted-foreground ml-1">({stockItems.find(item => item.id === supplement.stockConsumption?.stockItemId)?.name})</span>
                                                                    )}
                                                                    {Object.keys(supplement.stockConsumption?.quantities || {}).length > 0 && (
                                                                        <span className="text-[10px] text-muted-foreground ml-1">
                                                                            {Object.entries(supplement.stockConsumption?.quantities || {}).map(([size, quantity]) => 
                                                                                `${size}: ${quantity}kg`
                                                                            ).join(', ')}
                                                                        </span>
                                                                    )}
                                                                </div>
                                                                <div className="flex items-center gap-1">
                                                                    <button
                                                                        type="button"
                                                                        onClick={() => startEditingSupplement(supplement)}
                                                                        className="h-6 w-6 flex items-center justify-center rounded hover:bg-gray-100 transition-colors cursor-pointer"
                                                                        aria-label="Edit supplement"
                                                                    >
                                                                        <PencilIcon className="h-4 w-4 text-black" />
                                                                    </button>
                                                                    <button
                                                                        type="button"
                                                                        onClick={() => handleDeleteSupplementClick(supplement.id)}
                                                                        className="h-6 w-6 flex items-center justify-center rounded hover:bg-gray-100 text-black transition-colors cursor-pointer"
                                                                        aria-label="Remove supplement"
                                                                    >
                                                                        <TrashIcon className="h-4 w-4" />
                                                                    </button>
                                                                </div>
                                                            </>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
} 