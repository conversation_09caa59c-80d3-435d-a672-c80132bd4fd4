'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { RefreshCw, Settings, Moon, Sun, Trash2, User, Server, Globe, Users, UserPlus, Crown, Shield } from 'lucide-react';
import { useTheme } from 'next-themes';
import Link from 'next/link';
import { useStaticNavigation, isStaticMode } from '@/lib/utils/navigation';
import { CacheCleaner } from '@/components/CacheCleaner';
import { useSidebar } from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';
import { useState, useEffect } from 'react';
import { MultiUserSwitcher } from '@/components/multi-user/MultiUserSwitcher';
import { SwitchUserButton } from '@/components/multi-user/SwitchUserButton';
import { UserSwitchDialog } from '@/components/multi-user/UserSwitchDialog';

// Simple Avatar component
const Avatar = ({ className, children }: { className?: string, children?: React.ReactNode }) => (
  <div className={cn("relative overflow-hidden rounded-full", className)}>
    {children}
  </div>
);

const AvatarFallback = ({ className, children }: { className?: string, children?: React.ReactNode }) => (
  <div className={cn("flex h-full w-full items-center justify-center bg-muted", className)}>
    {children}
  </div>
);

export function UserSwitcher() {
  const router = useRouter();
  const { user, logout, availableUsers, hasMultipleUsers } = useAuth();
  const { theme, setTheme } = useTheme();
  const { state } = useSidebar();
  const isCollapsed = state === 'collapsed';
  const { navigate } = useStaticNavigation();

  // Handle navigation click
  const handleNavClick = (href: string, e: React.MouseEvent) => {
    if (isStaticMode()) {
      e.preventDefault();
      const cleanPath = href.replace(/^\//, '');
      navigate(cleanPath);
    }
    // In dynamic mode, let Link handle it normally
  };

  // If we have multi-user capabilities, use the enhanced switcher
  if (hasMultipleUsers || (availableUsers && availableUsers.length > 0)) {
    return (
      <div className="w-full">
        <MultiUserSwitcher 
          className="w-full" 
          showStats={true}
        />
        
        {/* Additional settings below the multi-user switcher */}
        <div className="mt-2 space-y-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className={cn(
                  "flex w-full items-center justify-start gap-2 h-9 px-2 rounded-md",
                  "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                )}
              >
                <Settings className="h-4 w-4" />
                {!isCollapsed && <span className="text-sm">Settings</span>}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Settings & Preferences</DropdownMenuLabel>
              <DropdownMenuSeparator />

              {/* Theme Toggle */}
              <DropdownMenuItem className="cursor-pointer flex items-center" onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>
                {theme === 'dark' ? (
                  <>
                    <Sun className="h-4 w-4 mr-2" />
                    Light Mode
                  </>
                ) : (
                  <>
                    <Moon className="h-4 w-4 mr-2" />
                    Dark Mode
                  </>
                )}
              </DropdownMenuItem>

              {/* Settings Link */}
              <DropdownMenuItem className="cursor-pointer" asChild>
                <Link href="/settings" onClick={(e) => handleNavClick("/settings", e)} className="flex w-full items-center text-sm">
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </Link>
              </DropdownMenuItem>

              {/* Cache Cleaner */}
              <DropdownMenuItem className="cursor-pointer flex items-center">
                <Trash2 className="h-4 w-4 mr-2 text-destructive" />
                <CacheCleaner />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    );
  }

  // Fallback to original single-user switcher for backward compatibility
  const handleSignOut = async () => {
    logout();
  };

  const [isUserSwitchDialogOpen, setIsUserSwitchDialogOpen] = useState(false);

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Check if user is staff (not owner or admin)
  const isStaff = user?.role === 'staff';

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className={cn(
            "flex w-full items-center justify-between gap-2 h-9 px-2 rounded-md",
            "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
          )}
        >
          <div className="flex items-center gap-2">
            {isCollapsed ? (
              <User className="h-4 w-4" />
            ) : (
              <>
                <Avatar className="h-6 w-6 bg-sidebar-accent">
                  <AvatarFallback className="text-xs font-medium text-sidebar-accent-foreground">
                    {user?.name ? getInitials(user.name) : 'U'}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm font-medium truncate max-w-28">{user?.name || 'User'}</span>
              </>
            )}
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {/* Add Permission Reload option for staff users */}
        {isStaff && (
          <>
            <DropdownMenuItem className="cursor-pointer" asChild>
              <button className="flex w-full items-center text-sm" onClick={() => window.location.reload()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Reload Permissions
              </button>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
          </>
        )}

        {/* Theme Toggle */}
        <DropdownMenuItem className="cursor-pointer flex items-center" onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>
          {theme === 'dark' ? (
            <>
              <Sun className="h-4 w-4 mr-2" />
              Light Mode
            </>
          ) : (
            <>
              <Moon className="h-4 w-4 mr-2" />
              Dark Mode
            </>
          )}
        </DropdownMenuItem>

        {/* Settings Link */}
        <DropdownMenuItem className="cursor-pointer" asChild>
          <Link href="/settings" onClick={(e) => handleNavClick("/settings", e)} className="flex w-full items-center text-sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Link>
        </DropdownMenuItem>

        {/* Cache Cleaner */}
        <DropdownMenuItem className="cursor-pointer flex items-center">
          <Trash2 className="h-4 w-4 mr-2 text-destructive" />
          <CacheCleaner />
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* Use the new secure switch user button */}
        <DropdownMenuItem onClick={() => setIsUserSwitchDialogOpen(true)}>
          <User className="h-4 w-4 mr-2" />
          Switch User
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={handleSignOut}>
          Sign Out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>

    {/* User Switch Dialog */}
    <UserSwitchDialog 
      open={isUserSwitchDialogOpen} 
      onOpenChange={setIsUserSwitchDialogOpen} 
    />
  );
}