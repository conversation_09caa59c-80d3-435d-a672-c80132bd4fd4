// knowledge:start mobile/electron default route logic
'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { usePlatform } from '@/lib/context/platform-context';
import { usePermissions } from '@/lib/hooks/use-permissions';
import { navigateStatic } from '@/lib/utils/navigation';

function getFirstAllowedPage(hasPageAccess: (page: string) => boolean): string {
  const pages = ['menu', 'orders', 'inventory', 'staff', 'finance', 'analytics', 'suppliers', 'settings'];
  for (const page of pages) {
    if (hasPageAccess(page)) {
      return `/${page}`;
    }
  }
  return '/menu'; // fallback
}

export default function HomePage() {
  const router = useRouter();
  const { isAuthenticated, loading, user } = useAuth();
  const { isElectron, isCapacitor } = usePlatform();
  const { hasPageAccess, isOwner } = usePermissions();
  const [hasRedirected, setHasRedirected] = useState(false);

  useEffect(() => {
    // Prevent multiple redirects
    if (hasRedirected) {
      console.log('🚫 [HomePage] Already redirected, skipping');
      return;
    }
    
    // Only run on client and wait for auth to finish loading
    if (loading) {
      console.log('🔄 [HomePage] Auth still loading, waiting...');
      return;
    }
    
    console.log('🏠 [HomePage] Auth complete, determining redirect:', {
      isElectron,
      isCapacitor,
      isAuthenticated,
      loading,
      hasRedirected
    });
    
    if (isElectron || isCapacitor) {
      if (!isAuthenticated) {
        console.log('🔐 [HomePage] Native app - NOT authenticated, hard navigating to auth.html');
        navigateStatic('auth');
        setHasRedirected(true);
      } else {
        console.log('✅ [HomePage] Native app - authenticated, hard navigating to menu.html');
        navigateStatic('menu');
        setHasRedirected(true);
      }
    } else {
      // Web app logic
      if (!isAuthenticated) {
        console.log('🌐 [HomePage] Web app - not authenticated, navigating to /landing');
        router.push('/landing');
        setHasRedirected(true);
      } else {
        // Authenticated web user - redirect to first allowed page
        const redirectPage = isOwner ? '/menu' : getFirstAllowedPage(hasPageAccess);
        console.log('✅ [HomePage] Web app - authenticated, navigating to', redirectPage);
        router.push(redirectPage);
        setHasRedirected(true);
      }
    }
  }, [isElectron, isCapacitor, isAuthenticated, loading, router, hasRedirected, hasPageAccess, isOwner]);

  // Show a loading spinner while checking auth/platform
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center space-y-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
        <div className="text-sm text-muted-foreground">
          {loading ? 'Checking authentication...' : 'Redirecting...'}
        </div>
      </div>
    </div>
  );
}
// knowledge:end mobile/electron default route logic
