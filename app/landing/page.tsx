'use client';

import { ArrowR<PERSON>, Download, Eye, Rocket, Server, Smartphone, Users, WifiOff, Package, BarChart3, RefreshCw, Menu as MenuIcon, X, Settings, ClipboardList, Printer, Truck, Wallet, Globe, Laptop, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { motion, useScroll, useMotionValueEvent, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';
import { Toaster, toast } from 'sonner';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { useRouter } from 'next/navigation';
import { isWebBuild, getBuildTargetName, getAuthButtonText } from '@/lib/utils/build-utils';
import { useStaticNavigation } from '@/lib/utils/navigation';
import DemoVideoSection from '@/app/components/DemoVideoSection';

// Download handler function
const handleWindowsDownload = async () => {
  try {
    toast.loading('🔍 جاري تحضير الملف...', { id: 'download' });
    
    // Fetch latest release info from our API
    const response = await fetch('/api/releases/latest');
    
    if (!response.ok) {
      if (response.status === 404) {
        toast.error('❌ لا يوجد إصدار متاح حالياً', { id: 'download' });
        return;
      }
      throw new Error(`HTTP ${response.status}`);
    }
    
    const releaseData = await response.json();
    
    if (!releaseData.success || !releaseData.downloadUrl) {
      throw new Error('Invalid response from server');
    }
    
    toast.success('✅ بدء التحميل...', { id: 'download' });
    
    // Create download link and trigger download
    const link = document.createElement('a');
    link.href = releaseData.downloadUrl;
    link.download = releaseData.fileName || 'bistro-latest.exe';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Show success message with file size if available
    const sizeText = releaseData.size ? ` (${Math.round(releaseData.size / (1024 * 1024))} MB)` : '';
    toast.success(`🎉 تم بدء تحميل ${releaseData.fileName}${sizeText}`, { 
      id: 'download',
      duration: 5000 
    });
    
  } catch (error) {
    console.error('Download failed:', error);
    toast.error('❌ فشل في تحميل الملف. يرجى المحاولة مرة أخرى لاحقاً.', { id: 'download' });
  }
};

// Enhanced auth handling for web vs native
// Using isWebBuild utility function from build-utils

// --- Enhanced Header Component with Better Auth Handling ---
const Header = ({ handleDashboardClick }: { handleDashboardClick: () => void }) => {
  const [hidden, setHidden] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { scrollY } = useScroll();
  
  // Safely destructure auth with fallbacks
  const auth = useAuth();
  const { 
    isAuthenticated = false, 
    loading: authLoading = true, 
    user = null, 
    error: authError = null 
  } = auth || {};


  useMotionValueEvent(scrollY, "change", (latest) => {
    const previous = scrollY.getPrevious();
    if (latest > previous && latest > 150) {
      setHidden(true);
    } else {
      setHidden(false);
    }
  });

  // Debug authentication state
  useEffect(() => {
    console.log('🔍 [Landing Header] Auth state:', { 
      isAuthenticated, 
      authLoading, 
      hasUser: !!user, 
      userRole: user?.role,
      authError 
    });
  }, [isAuthenticated, authLoading, user, authError]);

  return (
    <>
      <motion.header
        variants={{ visible: { y: 0 }, hidden: { y: "-100%" } }}
        animate={hidden ? "hidden" : "visible"}
        transition={{ duration: 0.35, ease: "easeInOut" }}
        className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-sm border-b border-border"
      >
        <div className="max-w-screen-2xl mx-auto px-4 md:px-8 py-4 flex justify-between items-center">
          <Link href="/" className="text-2xl font-bold font-changa text-foreground">
            bistro
          </Link>
          <nav className="hidden md:flex items-center gap-8">
            <Link href="#features" className="text-muted-foreground hover:text-foreground transition-colors">المميزات</Link>
            <Link href="#how-it-works" className="text-muted-foreground hover:text-foreground transition-colors">كيف يعمل</Link>
            <Link href="#download" className="text-muted-foreground hover:text-foreground transition-colors">التحميل</Link>
          </nav>
          <div className="hidden md:flex items-center gap-4">
            {authLoading ? (
              <Button disabled className="bg-muted text-muted-foreground font-bold rounded-xl">
                جاري التحقق...
              </Button>
            ) : isAuthenticated && user ? (
              <div className="flex items-center gap-3">
                <span className="text-sm text-muted-foreground">
                  مرحباً، {user.name}
                                 </span>
                 {isWebBuild() ? (
                   (() => {
                     const userAgent = typeof window !== 'undefined' ? window.navigator.userAgent.toLowerCase() : '';
                     const isMobile = /mobile|android|iphone|ipad|tablet|phone/i.test(userAgent) || (typeof window !== 'undefined' && window.innerWidth <= 768);
                     
                     return isMobile ? (
                       <Button 
                         onClick={handleDashboardClick}
                         className="bg-green-600 hover:bg-green-700 text-white font-bold rounded-xl flex items-center gap-2"
                       >
                         <Rocket className="w-4 h-4" />
                         دخول التطبيق
                       </Button>
                     ) : (
                       <Button 
                         onClick={handleWindowsDownload}
                         className="bg-blue-600 hover:bg-blue-700 text-white font-bold rounded-xl flex items-center gap-2"
                       >
                         <Download className="w-4 h-4" />
                         تحميل التطبيق
                       </Button>
                     );
                   })()
                ) : (
                  <Button asChild className="bg-primary text-primary-foreground font-bold hover:bg-primary/90 rounded-xl">
                    <Link href="/menu">لوحة التحكم</Link>
                  </Button>
                )}
              </div>
            ) : (
              <Button asChild className="bg-primary text-primary-foreground font-bold hover:bg-primary/90 rounded-xl">
                                 <Link href="/auth">{getAuthButtonText(false)}</Link>
              </Button>
            )}
            <ThemeToggle />
          </div>
          <div className="md:hidden flex items-center gap-2">
            <ThemeToggle />
            <Button onClick={() => setIsMenuOpen(true)} variant="ghost" size="icon">
              <MenuIcon className="h-6 w-6 text-foreground" />
            </Button>
          </div>
        </div>
      </motion.header>

      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-background/60 backdrop-blur-sm z-[100]"
            onClick={() => setIsMenuOpen(false)}
          >
            <motion.div
              initial={{ x: "100%" }}
              animate={{ x: 0 }}
              exit={{ x: "100%" }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              className="fixed top-0 right-0 h-full w-full max-w-sm bg-card shadow-2xl p-6 flex flex-col border-l border-border"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center mb-12">
                <Link href="/" onClick={() => setIsMenuOpen(false)} className="text-2xl font-bold font-changa text-foreground">
                 
                </Link>
                <Button onClick={() => setIsMenuOpen(false)} variant="ghost" size="icon">
                  <X className="h-6 w-6 text-foreground" />
                </Button>
              </div>
              <nav className="flex flex-col items-center justify-center flex-grow gap-8 -mt-16">
                <Link href="#features" onClick={() => setIsMenuOpen(false)} className="text-3xl text-muted-foreground hover:text-foreground transition-colors font-changa">المميزات</Link>
                <Link href="#how-it-works" onClick={() => setIsMenuOpen(false)} className="text-3xl text-muted-foreground hover:text-foreground transition-colors font-changa">كيف يعمل</Link>
                <Link href="#download" onClick={() => setIsMenuOpen(false)} className="text-3xl text-muted-foreground hover:text-foreground transition-colors font-changa">التحميل</Link>
              </nav>
              
                             {authLoading ? (
                 <Button disabled className="w-full bg-muted text-muted-foreground font-bold rounded-xl py-6 text-lg">
                   جاري التحقق...
                 </Button>
               ) : isAuthenticated && user ? (
                 <div className="space-y-3">
                   <div className="text-center text-sm text-muted-foreground">
                     مرحباً، {user.name}
                   </div>
                   {isWebBuild() ? (
                     <Button 
                       onClick={() => {
                         setIsMenuOpen(false);
                         handleWindowsDownload();
                       }}
                       className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold rounded-xl py-6 text-lg flex items-center justify-center gap-2"
                     >
                       <Download className="w-5 h-5" />
                       تحميل التطبيق الكامل
                     </Button>
                   ) : (
                     <Button asChild className="w-full bg-primary text-primary-foreground font-bold hover:bg-primary/90 rounded-xl py-6 text-lg">
                       <Link href="/menu" onClick={() => setIsMenuOpen(false)}>لوحة التحكم</Link>
                     </Button>
                   )}
                 </div>
               ) : (
                 <Button asChild className="w-full bg-primary text-primary-foreground font-bold hover:bg-primary/90 rounded-xl py-6 text-lg">
                                       <Link href="/auth" onClick={() => setIsMenuOpen(false)}>{getAuthButtonText(false)}</Link>
                 </Button>
               )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

// --- Enhanced Hero Section with Better Auth UX ---
const HeroSection = ({ handleDashboardClick }: { handleDashboardClick: () => void }) => {
  // Safely destructure auth with fallbacks
  const auth = useAuth();
  const { 
    isAuthenticated = false, 
    loading: authLoading = true, 
    user = null, 
    error: authError = null, 
    isOfflineMode = false 
  } = auth || {};
  
  const router = useRouter();
  const { navigate } = useStaticNavigation();
  const [isClient, setIsClient] = useState(false);

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Debug authentication state
  useEffect(() => {
    if (isClient) {
      console.log('🔍 [Landing Hero] Auth state:', { 
        isAuthenticated, 
        authLoading, 
        hasUser: !!user, 
        userRole: user?.role,
        authError,
        isOfflineMode,
        isWebBuild: isWebBuild()
      });
    }
  }, [isAuthenticated, authLoading, user, authError, isOfflineMode, isClient]);

  return (
    <section className="relative w-full pt-48 pb-40 text-center overflow-hidden">
      <div className="absolute inset-0 w-full h-full bg-background bg-grid-white/[0.02] -z-10" />
      <div className="absolute bottom-0 left-0 right-0 h-40 bg-gradient-to-t from-background to-transparent -z-10" />

      <div className="max-w-screen-2xl mx-auto px-4 md:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <h1 className="text-5xl md:text-7xl font-extrabold text-foreground mb-6 font-changa leading-tight">
            Bistro: نظام إدارة المطاعم الأذكى والأسهل 🇩🇿
          </h1>
          <p className="max-w-2xl mx-auto text-xl text-muted-foreground mb-10 font-tajawal leading-relaxed">
            حوّل مطعمك إلى تجربة رقمية متطورة. نظام شامل لإدارة الطلبات والمخزون والموظفين مع تقارير مالية مفصلة وتطبيق محمول للنُدل والمالكين لمتابعة المبيعات حتى خارج المطعم.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2, ease: 'easeOut' }}
          className="flex flex-col sm:flex-row items-center justify-center gap-4"
        >
          <Button 
            size="lg" 
            onClick={handleWindowsDownload}
            className="w-full sm:w-auto bg-primary text-primary-foreground font-bold hover:bg-primary/90 rounded-xl text-base px-8 py-6 flex items-center gap-2.5 transition-transform hover:scale-105 shadow-lg shadow-primary/20"
          >
            <Download className="w-5 h-5" />
            <span>تحميل مجاني Windows 10/11</span>
          </Button>

          {/* Enhanced Authentication State Handling for Web/Native */}
          {!isClient ? (
            // Server-side rendering placeholder
            <Button size="lg" variant="outline" disabled className="w-full sm:w-auto rounded-xl text-base px-8 py-6">
              جاري التحميل...
            </Button>
          ) : authLoading ? (
            // Loading state
            <Button size="lg" variant="outline" disabled className="w-full sm:w-auto rounded-xl text-base px-8 py-6 flex items-center gap-2">
              <RefreshCw className="w-4 h-4 animate-spin" />
              جاري التحقق...
            </Button>
          ) : authError ? (
            // Error state
            <Button 
              size="lg" 
              variant="outline" 
              onClick={() => router.push('/auth')}
              className="w-full sm:w-auto rounded-xl text-base px-8 py-6 border-destructive text-destructive hover:bg-destructive/10"
            >
              خطأ في التحقق - إعادة المحاولة
            </Button>
                      ) : isAuthenticated && user ? (
              // Authenticated state - different behavior for web vs native
              (() => {
                const userAgent = typeof window !== 'undefined' ? window.navigator.userAgent.toLowerCase() : '';
                const isMobile = /mobile|android|iphone|ipad|tablet|phone/i.test(userAgent) || (typeof window !== 'undefined' && window.innerWidth <= 768);
                
                if (isWebBuild()) {
                  return isMobile ? (
                    <Button 
                      size="lg" 
                      onClick={handleDashboardClick}
                      className="w-full sm:w-auto bg-green-600 hover:bg-green-700 text-white rounded-xl text-base px-8 py-6 transition-transform hover:scale-105 flex items-center gap-2"
                    >
                      <Rocket className="w-5 h-5" />
                      دخول التطبيق
                    </Button>
                  ) : (
                    <Button 
                      size="lg" 
                      onClick={handleDashboardClick}
                      className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white rounded-xl text-base px-8 py-6 transition-transform hover:scale-105 flex items-center gap-2"
                    >
                      <Laptop className="w-5 h-5" />
                      تحميل التطبيق الكامل
                    </Button>
                  );
                } else {
                  return (
                    <Button 
                      size="lg" 
                      onClick={handleDashboardClick}
                      className="w-full sm:w-auto bg-green-600 hover:bg-green-700 text-white rounded-xl text-base px-8 py-6 transition-transform hover:scale-105 flex items-center gap-2"
                    >
                      <Rocket className="w-5 h-5" />
                      الذهاب إلى لوحة التحكم
                    </Button>
                  );
                }
              })()
          ) : (
            // Not authenticated state
                          <Button asChild size="lg" variant="outline" className="w-full sm:w-auto rounded-xl text-base px-8 py-6 transition-transform hover:scale-105">
                <Link href="/auth">
                  <span>{getAuthButtonText(false)}</span>
                </Link>
              </Button>
          )}
        </motion.div>

                 {/* Enhanced messaging for authenticated web users */}
         {isClient && isAuthenticated && user && isWebBuild() && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="mt-8 p-4 bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800 rounded-xl max-w-md mx-auto"
          >
            <div className="text-center">
              <p className="text-sm text-blue-800 dark:text-blue-200 mb-2">
                مرحباً، {user.name}! 👋
              </p>
              <p className="text-xs text-blue-600 dark:text-blue-300">
                تم إنشاء حسابك بنجاح. لاستخدام جميع الميزات، يرجى تحميل تطبيق سطح المكتب.
              </p>
            </div>
          </motion.div>
        )}

        {/* Demo Video Section */}
        <DemoVideoSection />

        {/* Authentication Status Indicator for Debug */}
        {isClient && process.env.NODE_ENV === 'development' && (
          <div className="mt-8 text-xs text-muted-foreground space-y-1">
            <div>🔍 Auth Debug: {isAuthenticated ? '✅ Authenticated' : '❌ Not Authenticated'}</div>
            {user && <div>👤 User: {user.name} ({user.role})</div>}
            {authError && <div>❌ Error: {authError}</div>}
            {isOfflineMode && <div>📱 Offline Mode Active</div>}
            <div>🌐 Build Target: {getBuildTargetName()}</div>
          </div>
        )}
      </div>
    </section>
  );
};

// --- Features Section ---
const features = [
  {
    icon: Smartphone,
    title: 'تطبيق محمول للنُدل',
    description: 'النُدل يستلمون الطلبات ويتابعونها من هواتفهم مباشرة - سهل وسريع.',
  },
  {
    icon: BarChart3,
    title: 'متابعة مباشرة للمالك',
    description: 'راقب مبيعاتك وأرباحك لحظة بلحظة من أي مكان حتى وأنت في البيت.',
  },
  {
    icon: ClipboardList,
    title: 'إدارة ذكية للطلبات',
    description: 'نظام متطور لاستقبال ومتابعة الطلبات مع تنبيهات فورية للمطبخ.',
  },
  {
    icon: Package,
    title: 'تحكم كامل في المخزون',
    description: 'تتبع دقيق للمواد والمكونات مع تنبيهات ذكية عند النفاد.',
  },
  {
    icon: Users,
    title: 'إدارة فريق العمل',
    description: 'تنظيم الموظفين وتحديد صلاحياتهم مع متابعة أدائهم يومياً.',
  },
  {
    icon: WifiOff,
    title: 'لا يتوقف أبداً',
    description: 'يعمل حتى بدون انترنت ويتزامن تلقائياً عند عودة الاتصال.',
  },
];

const FeaturesSection = () => (
  <section id="features" className="w-full py-24 bg-muted/30">
    <div className="max-w-screen-2xl mx-auto px-4 md:px-8">
      <div className="text-center max-w-2xl mx-auto mb-16">
        <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4 font-changa">كل ما تحتاجه لإدارة مطعم عصري ومتطور</h2>
        <p className="text-lg text-muted-foreground font-tajawal">
          من طلب النُدل على الهاتف إلى متابعة المالك للمبيعات من المنزل - نظام شامل يدير كل شيء بذكاء وبساطة.
        </p>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {features.map((feature, index) => (
          <motion.div 
            key={index} 
            className="bg-card p-6 rounded-xl border border-border text-center flex flex-col items-center transition-all duration-300 hover:bg-accent hover:border-accent-foreground/20 hover:-translate-y-1"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            viewport={{ once: true }}
          >
            <div className="bg-primary/10 p-3 rounded-full mb-4">
              <feature.icon className="w-6 h-6 text-primary" />
            </div>
            <h3 className="text-lg font-bold text-foreground font-changa mb-2">{feature.title}</h3>
            <p className="text-sm text-muted-foreground font-tajawal leading-relaxed">{feature.description}</p>
          </motion.div>
        ))}
      </div>
    </div>
  </section>
);

// --- How It Works Section ---
const steps = [
  {
    icon: Download,
    title: 'حمّل وثبّت',
    description: 'تنزيل سريع وتثبيت بسيط على جهازك.'
  },
  {
    icon: Settings,
    title: 'أعِد مطعمك',
    description: 'أضف القائمة والموظفين وتخصيص الإعدادات.'
  },
  {
    icon: ClipboardList,
    title: 'ابدأ العمل',
    description: 'استقبل الطلبات وأدِر مطعمك بسهولة.'
  },
];

const HowItWorksSection = () => (
  <section id="how-it-works" className="w-full py-24 bg-background">
    <div className="max-w-screen-2xl mx-auto px-4 md:px-8">
      <div className="text-center max-w-2xl mx-auto mb-16">
        <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4 font-changa">ابدأ مع أفضل نظام لإدارة المطاعم في 3 خطوات</h2>
        <p className="text-lg text-muted-foreground font-tajawal">
          إعداد سريع وبسيط لتشغيل برنامج تسيير مطعمك في دقائق معدودة.
        </p>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
        {steps.map((step, index) => (
          <motion.div 
            key={index}
            className="bg-card p-6 rounded-xl border border-border text-center flex flex-col items-center transition-all duration-300 hover:bg-accent hover:border-accent-foreground/20"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            viewport={{ once: true }}
          >
            <div className="bg-secondary/50 p-3 rounded-full mb-4">
              <step.icon className="w-6 h-6 text-secondary-foreground" />
            </div>
            <h3 className="text-lg font-bold text-foreground font-changa mb-2">{step.title}</h3>
            <p className="text-sm text-muted-foreground font-tajawal">{step.description}</p>
          </motion.div>
        ))}
      </div>
    </div>
  </section>
);

// --- Pricing Section ---
const PricingSection = () => (
  <section id="pricing" className="w-full py-24 bg-background">
    <div className="max-w-screen-2xl mx-auto px-4 md:px-8">
      <div className="text-center max-w-3xl mx-auto mb-16">
        <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4 font-changa">سعر خاص للمستخدمين الأوائل</h2>
        <p className="text-lg text-muted-foreground font-tajawal">
          انضم الآن بسعر سنوي مخفض ومضمون مدى الحياة. هذا العرض متوفر لفترة محدودة فقط.
        </p>
      </div>
      <div className="flex justify-center">
        <motion.div
          className="bg-card border border-border rounded-2xl p-8 max-w-lg w-full shadow-lg hover:shadow-primary/10 transition-shadow duration-300"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, ease: 'easeOut' }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-8">
            <Badge variant="outline" className="py-1.5 px-4 text-sm font-semibold text-primary border-primary/50 rounded-full mb-4">
              عرض الإطلاق
            </Badge>
            <h3 className="text-2xl font-bold text-foreground font-changa mb-2">Bistro Pro</h3>
            <div className="flex items-baseline justify-center gap-3 mb-2">
              <span className="text-5xl font-extrabold text-primary tracking-tight">25,000 د.ج</span>
              <span className="text-xl font-medium text-muted-foreground">/ سنوياً</span>
            </div>
            <p className="text-muted-foreground text-base line-through">السعر الأصلي 40,000 د.ج / سنوياً</p>
            <p className="text-primary font-semibold mt-2">
              السعر ثابت مدى الحياة للمشتركين الأوائل
            </p>
          </div>
          <ul className="space-y-4 text-sm mb-10">
            <li className="flex items-start gap-3">
              <div className="p-1 bg-green-500/20 rounded-full mt-1"><Check className="w-4 h-4 text-green-500" /></div>
              <div>
                <span className="font-semibold text-foreground">جميع الميزات</span>
                <p className="text-muted-foreground">وصول كامل لكل الأدوات الحالية والمستقبلية بدون تكلفة إضافية.</p>
              </div>
            </li>
            <li className="flex items-start gap-3">
              <div className="p-1 bg-green-500/20 rounded-full mt-1"><Check className="w-4 h-4 text-green-500" /></div>
              <div>
                <span className="font-semibold text-foreground">تحديثات ودعم</span>
                <p className="text-muted-foreground">تحديثات مستمرة للنظام ودعم فني متكامل لضمان أفضل أداء.</p>
              </div>
            </li>
            <li className="flex items-start gap-3">
              <div className="p-1 bg-green-500/20 rounded-full mt-1"><Check className="w-4 h-4 text-green-500" /></div>
              <div>
                <span className="font-semibold text-foreground">تطبيقات متعددة</span>
                <p className="text-muted-foreground">تطبيق سطح المكتب + تطبيق محمول للنُدل + تطبيق المالك لمتابعة المبيعات من أي مكان.</p>
              </div>
            </li>
          </ul>
          <Button asChild size="lg" className="w-full bg-primary text-primary-foreground font-bold hover:bg-primary/90 rounded-xl text-base py-7 transition-transform hover:scale-105 shadow-lg shadow-primary/20">
            <Link href="/auth">
              <span>الاستفادة من العرض</span>
              <ArrowRight className="w-4 h-4 mr-2" />
            </Link>
          </Button>
        </motion.div>
      </div>
    </div>
  </section>
);


// --- Enhanced Download Section with Auth Integration ---
const DownloadSection = ({ handleDashboardClick }: { handleDashboardClick: () => void }) => {
  // Safely destructure auth with fallbacks
  const auth = useAuth();
  const { 
    isAuthenticated = false, 
    loading: authLoading = true, 
    user = null 
  } = auth || {};

  return (
    <section id="download" className="w-full py-24 bg-muted/30">
      <div className="max-w-screen-2xl mx-auto px-4 md:px-8">
        <div className="bg-card border border-border rounded-xl p-8 md:p-12 text-center max-w-2xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 font-changa text-foreground">حمّل برنامج تسيير المطاعم Bistro</h2>
          <p className="text-muted-foreground mb-8 font-tajawal">
            نظام متكامل مع تطبيقات محمولة للنُدل والمالكين - ابدأ رحلتك الرقمية الآن.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <Button 
              size="lg" 
              onClick={handleWindowsDownload}
              className="w-full sm:w-auto bg-primary text-primary-foreground font-bold hover:bg-primary/90 rounded-xl text-base px-8 py-4 flex items-center gap-2.5 transition-transform hover:scale-105"
            >
              <Download className="w-5 h-5" />
              <span>تحميل مجاني Windows 10/11</span>
            </Button>
            
            {authLoading ? (
              <Button size="lg" variant="outline" disabled className="w-full sm:w-auto rounded-xl text-base px-8 py-4">
                جاري التحقق...
              </Button>
            ) : isAuthenticated && user ? (
              isWebBuild() ? (
                <Button 
                  size="lg" 
                  onClick={handleWindowsDownload}
                  className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white rounded-xl text-base px-8 py-4 transition-transform hover:scale-105 flex items-center gap-2"
                >
                  <Download className="w-5 h-5" />
                  <span>تحميل التطبيق الكامل</span>
                </Button>
              ) : (
                <Button 
                  size="lg" 
                  onClick={handleDashboardClick}
                  className="w-full sm:w-auto bg-green-600 hover:bg-green-700 text-white rounded-xl text-base px-8 py-4 transition-transform hover:scale-105 flex items-center gap-2"
                >
                  <Rocket className="w-5 h-5" />
                  <span>دخول لوحة التحكم</span>
                </Button>
              )
            ) : (
              <Button asChild size="lg" variant="outline" className="w-full sm:w-auto rounded-xl text-base px-8 py-4 transition-transform hover:scale-105">
                <Link href="/auth">
                  <span>{getAuthButtonText(false)}</span>
                </Link>
              </Button>
            )}
          </div>
          <div className="mt-6 space-y-2">
            <Badge variant="secondary" className="py-1 px-3 text-xs">
              <Smartphone className="w-3 h-3 ml-1" />
              تطبيق النُدل المحمول - قريباً
            </Badge>
            <br />
            <Badge variant="outline" className="py-1 px-3 text-xs border-green-500 text-green-700">
              <BarChart3 className="w-3 h-3 ml-1" />
              تطبيق متابعة المالك - قريباً
            </Badge>
          </div>
        </div>
      </div>
    </section>
  );
};

// --- Footer ---
const Footer = () => (
  <footer className="w-full border-t border-border py-8 bg-background">
    <div className="max-w-screen-2xl mx-auto px-4 md:px-8 text-center text-muted-foreground">
      <p>&copy; {new Date().getFullYear()} . جميع الحقوق محفوظة.</p>
    </div>
  </footer>
);

export default function LandingPage() {
  // Safely get auth error with fallback
  const auth = useAuth();
  const authError = auth?.error || null;
  
  const router = useRouter();
  const { navigate } = useStaticNavigation();

  // Handle dashboard navigation - shared function
  const handleDashboardClick = () => {
    if (isWebBuild()) {
      // Check if user is on mobile device
      const userAgent = window.navigator.userAgent.toLowerCase();
      const isMobile = /mobile|android|iphone|ipad|tablet|phone/i.test(userAgent) || window.innerWidth <= 768;
      
      if (isMobile && auth?.isAuthenticated && auth?.user) {
        // Mobile users can access the full app on web
        const targetRoute = '/menu';
        console.log('📱 [Landing] Navigating authenticated mobile user to app:', targetRoute);
        router.push(targetRoute);
        navigate(targetRoute.replace(/^\//, ''));
        return;
      }
      
      // Desktop users get the download prompt
      handleWindowsDownload();
      return;
    }

    if (auth?.isAuthenticated && auth?.user) {
      // Navigate to appropriate dashboard based on user role (only for native builds)
      const targetRoute = '/menu'; // Default to menu for all users
      console.log('🚀 [Landing] Navigating authenticated user to:', targetRoute);
      router.push(targetRoute);
      navigate(targetRoute.replace(/^\//, ''));
    } else {
      console.log('❌ [Landing] No authenticated user, redirecting to auth');
      router.push('/auth');
      navigate('auth');
    }
  };
  
  // Show authentication error toast if needed
  useEffect(() => {
    if (authError) {
      console.error('🚨 [Landing] Authentication error:', authError);
      toast.error(`خطأ في النظام: ${authError}`, {
        description: 'يرجى إعادة تحميل الصفحة أو المحاولة لاحقاً',
        duration: 5000,
      });
    }
  }, [authError]);

  return (
    <div className="min-h-screen w-full relative">
      <div className="relative z-10">
        <Toaster position="bottom-right" />
        <Header handleDashboardClick={handleDashboardClick} />
        <main>
          <HeroSection handleDashboardClick={handleDashboardClick} />
          <FeaturesSection />
          <HowItWorksSection />
          <PricingSection />
          <DownloadSection handleDashboardClick={handleDashboardClick} />
        </main>
        <Footer />
      </div>
    </div>
  );
}