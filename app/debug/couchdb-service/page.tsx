'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { RefreshCw, Settings, Play, Square, RotateCcw, Trash2, Download, Shield, AlertTriangle } from 'lucide-react';

interface ServiceStatus {
  installed: boolean;
  running: boolean;
  port?: number;
  version?: string;
  error?: string;
}

interface ConnectionInfo {
  url: string;
  adminUrl: string;
  port: number;
  status: 'connected' | 'disconnected' | 'error';
  serviceStatus?: ServiceStatus;
  error?: string;
}

export default function CouchDBServiceDebugPage() {
  const [serviceStatus, setServiceStatus] = useState<ServiceStatus | null>(null);
  const [connectionInfo, setConnectionInfo] = useState<ConnectionInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isElevated, setIsElevated] = useState<boolean | null>(null);
  const [isWindows, setIsWindows] = useState(false);

  const isElectron = typeof window !== 'undefined' && (window as any).electron;

  useEffect(() => {
    if (isElectron) {
      // Check if we're on Windows
      const userAgent = navigator.userAgent;
      setIsWindows(userAgent.includes('Windows'));
      
      loadServiceStatus();
      loadConnectionInfo();
      loadElevationStatus();
    }
  }, [isElectron]);

  const loadServiceStatus = async () => {
    try {
      const status = await (window as any).electron.invoke('couchdb-service:get-status');
      setServiceStatus(status);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const loadConnectionInfo = async () => {
    try {
      const info = await (window as any).electron.invoke('couchdb-service:get-info');
      setConnectionInfo(info);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const loadElevationStatus = async () => {
    try {
      const result = await (window as any).electron.invoke('couchdb-service:check-elevation');
      setIsElevated(result.elevated);
    } catch (err: any) {
      console.warn('Failed to check elevation status:', err);
      setIsElevated(null);
    }
  };

  const requestElevation = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await (window as any).electron.invoke('couchdb-service:request-elevation');
      
      if (!result.success) {
        setError(result.error || 'Failed to request elevation');
      } else {
        // The app should restart as Administrator, so this shouldn't execute
        alert('Elevation request successful - app should restart as Administrator');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAction = async (action: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await (window as any).electron.invoke(`couchdb-service:${action}`);
      
      if (!result.success) {
        setError(result.error || `Failed to ${action} service`);
      }
      
      // Refresh status after action
      await loadServiceStatus();
      await loadConnectionInfo();
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await (window as any).electron.invoke('couchdb-service:test');
      
      if (result.success) {
        alert('Connection test successful!');
      } else {
        setError(result.error || 'Connection test failed');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string | boolean | undefined) => {
    if (typeof status === 'boolean') {
      return (
        <Badge variant={status ? 'default' : 'destructive'}>
          {status ? 'Yes' : 'No'}
        </Badge>
      );
    }
    
    switch (status) {
      case 'connected':
        return <Badge variant="default">Connected</Badge>;
      case 'disconnected':
        return <Badge variant="secondary">Disconnected</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  if (!isElectron) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle>CouchDB Service Debug</CardTitle>
            <CardDescription>
              This page is only available in the Electron desktop app.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">CouchDB Service Debug</h1>
          <p className="text-muted-foreground">
            Manage and debug the CouchDB system service
          </p>
        </div>
        <Button 
          onClick={async () => {
            await loadServiceStatus();
            await loadConnectionInfo();
            await loadElevationStatus();
          }}
          disabled={loading}
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      {error && (
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm">{error}</p>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Service Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Service Status
            </CardTitle>
            <CardDescription>
              Current status of the CouchDB system service
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {serviceStatus ? (
              <>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Installed:</span>
                  {getStatusBadge(serviceStatus.installed)}
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Running:</span>
                  {getStatusBadge(serviceStatus.running)}
                </div>
                {serviceStatus.port && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Port:</span>
                    <Badge variant="outline">{serviceStatus.port}</Badge>
                  </div>
                )}
                {serviceStatus.version && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Version:</span>
                    <Badge variant="outline">{serviceStatus.version}</Badge>
                  </div>
                )}
                {serviceStatus.error && (
                  <div className="text-sm text-destructive">
                    <strong>Error:</strong> {serviceStatus.error}
                  </div>
                )}
              </>
            ) : (
              <div className="text-sm text-muted-foreground">Loading...</div>
            )}
          </CardContent>
        </Card>

        {/* Connection Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className="w-5 h-5" />
              Connection Info
            </CardTitle>
            <CardDescription>
              Current connection status and details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {connectionInfo ? (
              <>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Status:</span>
                  {getStatusBadge(connectionInfo.status)}
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Port:</span>
                  <Badge variant="outline">{connectionInfo.port}</Badge>
                </div>
                <div className="text-sm">
                  <div className="font-medium mb-1">URLs:</div>
                  <div className="text-xs space-y-1">
                    <div>Public: <code className="bg-muted px-1 rounded">{connectionInfo.url}</code></div>
                    <div>Admin: <code className="bg-muted px-1 rounded">{connectionInfo.adminUrl.replace('admin:admin@', 'admin:***@')}</code></div>
                  </div>
                </div>
                {connectionInfo.error && (
                  <div className="text-sm text-destructive">
                    <strong>Error:</strong> {connectionInfo.error}
                  </div>
                )}
              </>
            ) : (
              <div className="text-sm text-muted-foreground">Loading...</div>
            )}
          </CardContent>
        </Card>

        {/* Windows Elevation Status */}
        {isWindows && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Windows Privileges
              </CardTitle>
              <CardDescription>
                Administrator privilege status for service installation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Administrator:</span>
                {isElevated === null ? (
                  <Badge variant="outline">Checking...</Badge>
                ) : isElevated ? (
                  <Badge variant="default" className="bg-green-600">Yes</Badge>
                ) : (
                  <Badge variant="destructive">No</Badge>
                )}
              </div>
              
              {isElevated === false && (
                <>
                  <div className="flex items-start gap-2 p-3 bg-orange-50 border border-orange-200 rounded-md">
                    <AlertTriangle className="w-4 h-4 text-orange-600 mt-0.5 flex-shrink-0" />
                    <div className="text-sm">
                      <p className="font-medium text-orange-800 mb-1">Administrator privileges required</p>
                      <p className="text-orange-700">
                        Installing Windows services requires Administrator privileges. 
                        Click below to restart the application with elevation.
                      </p>
                    </div>
                  </div>
                  
                  <Button 
                    onClick={requestElevation}
                    disabled={loading}
                    size="sm"
                    className="w-full"
                  >
                    <Shield className="w-4 h-4 mr-2" />
                    Restart as Administrator
                  </Button>
                </>
              )}
              
              {isElevated === true && (
                <div className="flex items-start gap-2 p-3 bg-green-50 border border-green-200 rounded-md">
                  <Shield className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm">
                    <p className="font-medium text-green-800 mb-1">Ready for service installation</p>
                    <p className="text-green-700">
                      The application has Administrator privileges and can install Windows services.
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Service Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Service Controls</CardTitle>
          <CardDescription>
            Install, start, stop, and manage the CouchDB service
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-4">
            <Button
              onClick={() => handleAction('install')}
              disabled={loading || serviceStatus?.installed}
              size="sm"
            >
              <Download className="w-4 h-4 mr-2" />
              Install Service
            </Button>
            
            <Button
              onClick={() => handleAction('start')}
              disabled={loading || !serviceStatus?.installed || serviceStatus?.running}
              size="sm"
            >
              <Play className="w-4 h-4 mr-2" />
              Start Service
            </Button>
            
            <Button
              onClick={() => handleAction('stop')}
              disabled={loading || !serviceStatus?.installed || !serviceStatus?.running}
              size="sm"
              variant="secondary"
            >
              <Square className="w-4 h-4 mr-2" />
              Stop Service
            </Button>
            
            <Button
              onClick={() => handleAction('restart')}
              disabled={loading || !serviceStatus?.installed}
              size="sm"
              variant="secondary"
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              Restart Service
            </Button>
          </div>
          
          <Separator className="my-4" />
          
          <div className="grid gap-3 sm:grid-cols-2">
            <Button
              onClick={testConnection}
              disabled={loading}
              size="sm"
              variant="outline"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Test Connection
            </Button>
            
            <Button
              onClick={() => handleAction('uninstall')}
              disabled={loading || !serviceStatus?.installed}
              size="sm"
              variant="destructive"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Uninstall Service
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Setup Instructions</CardTitle>
          <CardDescription>
            How to set up CouchDB as a system service
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">How It Works</h4>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• <strong>Step 1:</strong> App extracts bundled CouchDB to user directory</li>
              <li>• <strong>Step 2:</strong> Requests user permission for service installation</li>
              <li>• <strong>Step 3:</strong> Installs CouchDB as a system service with user consent</li>
              <li>• <strong>Step 4:</strong> Service runs independently of the app</li>
              <li>• <strong>Fallback:</strong> If user declines or service fails, uses bundled process mode</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">Benefits of System Service</h4>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• Automatic startup on system boot</li>
              <li>• Better reliability and crash recovery</li>
              <li>• Shared between multiple app instances</li>
              <li>• OS-managed process lifecycle</li>
              <li>• No dependency on app lifecycle</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">Security & Permissions</h4>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• User consent required for service installation</li>
              <li>• <strong>Windows:</strong> Requires Administrator privileges (UAC elevation)</li>
              <li>• <strong>macOS:</strong> May request administrator password</li>
              <li>• Unsigned apps may trigger security warnings</li>
              <li>• Automatic fallback if permissions denied</li>
              <li>• All data remains local and secure</li>
            </ul>
          </div>
          
          {isWindows && (
            <div>
              <h4 className="font-medium mb-2">Windows-Specific Notes</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Service installation requires Administrator privileges</li>
                <li>• Use the "Restart as Administrator" button for automatic elevation</li>
                <li>• Alternative: Right-click app icon → "Run as administrator"</li>
                <li>• One-time elevation needed only for installation</li>
                <li>• Normal app usage doesn't require elevation</li>
                <li>• Antivirus software may block service installation</li>
              </ul>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}