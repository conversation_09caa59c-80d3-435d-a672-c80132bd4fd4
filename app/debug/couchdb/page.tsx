'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface CouchDBDebugInfo {
  status: string;
  port: number;
  logs: string[];
  extractedPath: string;
  bundledPath: string;
  platform: string;
  isPackaged: boolean;
  resourcesPath: string;
}

export default function CouchDBDebugPage() {
  const [debugInfo, setDebugInfo] = useState<CouchDBDebugInfo | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [isInitializing, setIsInitializing] = useState(false);

  // Get initial debug info
  useEffect(() => {
    loadDebugInfo();
    
    // Listen for log updates if we're in electron
    if (typeof window !== 'undefined' && (window as any).electron) {
      (window as any).electron.ipcRenderer.on('couchdb-debug-log', (_event: any, log: string) => {
        setLogs(prev => [...prev.slice(-99), log]); // Keep last 100 logs
      });
    }
  }, []);

  const loadDebugInfo = async () => {
    try {
      if (typeof window !== 'undefined' && (window as any).electron) {
        const info = await (window as any).electron.ipcRenderer.invoke('get-couchdb-debug-info');
        setDebugInfo(info);
      }
    } catch (error) {
      console.error('Failed to load debug info:', error);
    }
  };

  const testCouchDBInit = async () => {
    setIsInitializing(true);
    setLogs([]); // Clear logs
    
    try {
      if (typeof window !== 'undefined' && (window as any).electron) {
        const result = await (window as any).electron.ipcRenderer.invoke('test-couchdb-init');
        console.log('CouchDB init result:', result);
        await loadDebugInfo(); // Refresh debug info
      }
    } catch (error) {
      console.error('CouchDB init failed:', error);
    } finally {
      setIsInitializing(false);
    }
  };

  const checkSystemCouchDB = async () => {
    try {
      const response = await fetch('http://localhost:5984/');
      if (response.ok) {
        const data = await response.json();
        setLogs(prev => [...prev, `✅ System CouchDB found: ${JSON.stringify(data)}`]);
      } else {
        setLogs(prev => [...prev, `❌ System CouchDB not responding: ${response.status}`]);
      }
    } catch (error) {
      setLogs(prev => [...prev, `❌ System CouchDB error: ${(error as Error).message}`]);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">CouchDB Debug</h1>
        <div className="space-x-2">
          <Button onClick={loadDebugInfo} variant="outline">
            Refresh Info
          </Button>
          <Button onClick={checkSystemCouchDB} variant="outline">
            Check System CouchDB
          </Button>
          <Button onClick={testCouchDBInit} disabled={isInitializing}>
            {isInitializing ? 'Testing...' : 'Test CouchDB Init'}
          </Button>
        </div>
      </div>

      {/* System Information */}
      <Card>
        <CardHeader>
          <CardTitle>System Information</CardTitle>
          <CardDescription>Platform and environment details</CardDescription>
        </CardHeader>
        <CardContent>
          {debugInfo ? (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong>Platform:</strong> {debugInfo.platform}
              </div>
              <div>
                <strong>Is Packaged:</strong> {debugInfo.isPackaged ? '✅' : '❌'}
              </div>
              <div>
                <strong>Resources Path:</strong> 
                <code className="ml-2 bg-gray-100 px-2 py-1 rounded text-sm">
                  {debugInfo.resourcesPath}
                </code>
              </div>
              <div>
                <strong>CouchDB Port:</strong> 
                <Badge variant="outline">{debugInfo.port}</Badge>
              </div>
            </div>
          ) : (
            <p>Loading debug information...</p>
          )}
        </CardContent>
      </Card>

      {/* CouchDB Status */}
      <Card>
        <CardHeader>
          <CardTitle>CouchDB Status</CardTitle>
          <CardDescription>Current CouchDB server status</CardDescription>
        </CardHeader>
        <CardContent>
          {debugInfo ? (
            <div className="space-y-4">
              <div>
                <strong>Status:</strong> 
                <Badge className="ml-2" variant={debugInfo.status === 'running' ? 'default' : 'destructive'}>
                  {debugInfo.status}
                </Badge>
              </div>
              
              <div>
                <strong>Bundled Path:</strong>
                <code className="block mt-1 bg-gray-100 p-2 rounded text-sm break-all">
                  {debugInfo.bundledPath || 'Not found'}
                </code>
              </div>
              
              <div>
                <strong>Extracted Path:</strong>
                <code className="block mt-1 bg-gray-100 p-2 rounded text-sm break-all">
                  {debugInfo.extractedPath || 'Not extracted'}
                </code>
              </div>
            </div>
          ) : (
            <p>Loading status...</p>
          )}
        </CardContent>
      </Card>

      {/* Live Logs */}
      <Card>
        <CardHeader>
          <CardTitle>Live Logs</CardTitle>
          <CardDescription>Real-time CouchDB initialization logs</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-96 overflow-y-auto bg-black text-green-400 p-4 rounded font-mono text-sm">
            {logs.length === 0 ? (
              <p className="text-gray-500">No logs yet. Click "Test CouchDB Init" to see logs.</p>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
          <Button 
            onClick={() => setLogs([])} 
            variant="outline" 
            size="sm" 
            className="mt-2"
          >
            Clear Logs
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}