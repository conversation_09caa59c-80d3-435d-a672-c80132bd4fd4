FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Copy package files
COPY package.json package-lock.json ./

# Install dependencies with legacy peer deps and skip native compilation for web builds
RUN npm install --legacy-peer-deps --ignore-scripts

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules

# Define build arguments for environment variables
ARG NEXT_PUBLIC_COUCHDB_URL
ARG NEXT_PUBLIC_COUCHDB_USER
ARG NEXT_PUBLIC_COUCHDB_PASSWORD
ARG NEXT_PUBLIC_COUCHDB_DB_NAME
ARG MONGODB_URI
ARG NEXTAUTH_SECRET
ARG JWT_SECRET
ARG GOOGLE_CLIENT_ID
ARG GOOGLE_CLIENT_SECRET

# Create .env.local file with build args
RUN echo "NEXT_PUBLIC_COUCHDB_URL=${NEXT_PUBLIC_COUCHDB_URL}" > .env.local && \
    echo "NEXT_PUBLIC_COUCHDB_USER=${NEXT_PUBLIC_COUCHDB_USER}" >> .env.local && \
    echo "NEXT_PUBLIC_COUCHDB_PASSWORD=${NEXT_PUBLIC_COUCHDB_PASSWORD}" >> .env.local && \
    echo "NEXT_PUBLIC_COUCHDB_DB_NAME=${NEXT_PUBLIC_COUCHDB_DB_NAME}" >> .env.local && \
    echo "NEXTAUTH_URL=http://localhost:3010" >> .env.local && \
    echo "NEXTAUTH_SECRET=${NEXTAUTH_SECRET:-$(openssl rand -base64 32)}" >> .env.local && \
    echo "JWT_SECRET=${JWT_SECRET:-${NEXTAUTH_SECRET:-$(openssl rand -base64 32)}}" >> .env.local && \
    echo "MONGODB_URI=${MONGODB_URI}" >> .env.local && \
    echo "GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}" >> .env.local && \
    echo "GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}" >> .env.local && \
    echo "GENERATE_SOURCEMAP=false" >> .env.local && \
    echo "NEXT_DISABLE_SOURCEMAPS=true" >> .env.local

# Copy all other files (excluding what's in .dockerignore)
COPY . .

# Build application with legacy peer deps and disable sourcemaps
ENV GENERATE_SOURCEMAP=false
ENV NEXT_DISABLE_SOURCEMAPS=true
ENV BUILD_TARGET=web
RUN npm run build:clean --legacy-peer-deps

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

# Create a non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy necessary files from the builder stage
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/.env.local ./.env.local
COPY --from=builder /app/server.js ./

# Set the user to run the app
USER nextjs

EXPOSE 3010

ENV PORT 3010

# Start Next.js with legacy peer deps
CMD ["npm", "start", "--legacy-peer-deps"] 