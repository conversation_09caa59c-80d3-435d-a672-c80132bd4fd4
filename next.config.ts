import type { NextConfig } from "next";
import path from 'path';

const nextConfig: NextConfig = {
  // Disable sourcemaps in production to prevent source code leakage
  productionBrowserSourceMaps: false,
  
  // 🔧 CRITICAL: Configure static export for electron, static, and mobile builds
  ...(process.env.BUILD_TARGET === 'electron' || process.env.BUILD_TARGET === 'static' || process.env.BUILD_TARGET === 'mobile' ? {
    output: 'export',
    trailingSlash: true,
    // 🚨 CRITICAL: Use relative paths that work with electron-serve directory structure
    assetPrefix: '../',
    images: {
      unoptimized: true,
      loader: 'custom',
      loaderFile: './lib/utils/image-loader.js'
    },
    // 🚫 API routes are automatically excluded from static export in App Router
  } : {
    // For non-static builds, keep default image optimization
    images: {
      domains: ['res.cloudinary.com'],
    },
  }),

  experimental: {
    // Increase header size limit for large data operations
    largePageDataBytes: 128 * 1000, // 128KB
  },
  
  // Disable React strict mode to reduce hydration issues during development
  reactStrictMode: false,
  
  // 🎯 Enhanced webpack configuration for different build targets
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 🌐 Web builds: Conditionally exclude restaurant functionality
    if (process.env.BUILD_TARGET === 'web') {
      // Only exclude heavy services that don't work in browser, but keep core functionality
      config.resolve.alias = {
        ...config.resolve.alias,
        // Replace problematic services with browser-compatible alternatives
  // '@/lib/services/kitchen-print-service': path.resolve(__dirname, 'lib/services/browser-print-service.js'),
        '@/lib/services/barcode-service': path.resolve(__dirname, 'lib/services/browser-barcode-service.js'),
  // '@/lib/services/print-service': path.resolve(__dirname, 'lib/services/browser-print-service.js'),
        // Exclude electron-dependent modules for web builds
        '@/lib/db/electron-db': path.resolve(__dirname, 'lib/services/empty-service.js'),
        '@/lib/auth/mongo-auth-ops': path.resolve(__dirname, 'lib/services/empty-auth-ops.js'),
        '@/lib/mongodb': path.resolve(__dirname, 'lib/services/empty-mongodb.js'),
        // Exclude electron-dependent components and hooks
        '@/app/hooks/use-p2p-sync': path.resolve(__dirname, 'lib/services/empty-hook.ts'),
        '@/app/components/MDNSBrowserComponent': path.resolve(__dirname, 'lib/services/empty-component.tsx'),
      };
    }

    // 📱🖥️ Static builds: Handle Node.js modules and ignore server-only code
    if (process.env.BUILD_TARGET === 'static' || process.env.BUILD_TARGET === 'electron') {
      // API directory is temporarily hidden during build, so no webpack config needed

      // Client-side: Mark Node.js modules as external/false
      if (!isServer) {
        config.resolve.fallback = {
          ...config.resolve.fallback,
          fs: false,
          path: false,
          'fs/promises': false,
          os: false,
          crypto: false,
          stream: false,
          net: false,
          tls: false,
          child_process: false,
          // Fix PouchDB node-fetch issues
          'node-fetch': false,
          'http': false,
          'https': false,
          'url': false,
        };
        
        // Fix PouchDB issues with proper polyfills/fallbacks
        config.resolve.alias = {
          ...config.resolve.alias,
          'node-fetch': path.resolve(__dirname, 'lib/utils/ignore-loader.js'),
          // Ignore problematic server modules
          'mongo-auth-ops': path.resolve(__dirname, 'lib/utils/ignore-loader.js'),
          'mongodb': path.resolve(__dirname, 'lib/utils/ignore-loader.js'),
          'nano': path.resolve(__dirname, 'lib/utils/ignore-loader.js'),
          'bcryptjs': path.resolve(__dirname, 'lib/utils/ignore-loader.js'),
          '@capacitor/http': path.resolve(__dirname, 'lib/services/empty-capacitor-http.js'),
        };

        // Prevent Next.js font optimization from generating absolute URLs
        config.module.rules.push({
          test: /\.(woff|woff2|eot|ttf|otf)$/,
          type: 'asset/resource',
          generator: {
            filename: 'static/fonts/[name].[hash][ext]'
          }
        });
      }

      // Add ignore-loader if not present
      config.resolveLoader.alias = {
        ...config.resolveLoader.alias,
        'ignore-loader': path.resolve(__dirname, 'lib/utils/ignore-loader.js')
      };
    }

    // Exclude electron dependencies from non-electron builds
    if (process.env.BUILD_TARGET !== 'electron' && process.env.BUILD_TARGET !== 'static') {
      config.externals = config.externals || [];
      if (Array.isArray(config.externals)) {
        config.externals.push('electron');
      }
    }
    
    return config;
  },
  
  // Configure server response headers for security
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
    ];
  },

  // 🎯 Build configuration for different environments
  typescript: {
    // Allow builds to complete with type errors (can be tightened later)
    ignoreBuildErrors: true,
  },
  
  eslint: {
    // Allow builds to complete with lint errors (can be tightened later)
    ignoreDuringBuilds: true,
  },

  // 🚀 Enable compression for static assets
  compress: true,

  // 🔄 Configure redirects and rewrites
  async redirects() {
    return [
      // Redirect old routes to new structure
      {
        source: '/orders',
        destination: '/protected/ordering',
        permanent: true,
      },
    ];
  },
};

export default nextConfig;