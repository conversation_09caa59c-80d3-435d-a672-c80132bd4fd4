const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  const server = createServer((req, res) => {
    const parsedUrl = parse(req.url, true);
    handle(req, res, parsedUrl);
  });

  // Start the server
  const PORT = process.env.PORT || 3000;
  server.listen(PORT, (err) => {
    if (err) throw err;
    const os = require('os');
    const ifaces = os.networkInterfaces();
    let lanUrl = null;
    for (const name of Object.keys(ifaces)) {
      for (const iface of ifaces[name]) {
        if (iface.family === 'IPv4' && !iface.internal) {
          lanUrl = `http://${iface.address}:${PORT}`;
          break;
        }
      }
      if (lanUrl) break;
    }
    console.log(`> Ready on http://localhost:${PORT}`);
    if (dev && lanUrl) {
      console.log(`> Network:   ${lanUrl}`);
    }
  });
});