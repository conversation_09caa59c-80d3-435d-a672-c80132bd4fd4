"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';

interface TrainingModeContextType {
  isTrainingMode: boolean;
  toggleTrainingMode: () => void;
  clearTrainingData: () => Promise<void>;
  trainingDataCount: number;
  isClearing: boolean;
}

const TrainingModeContext = createContext<TrainingModeContextType | undefined>(undefined);

export function TrainingModeProvider({ children }: { children: React.ReactNode }) {
  const [isTrainingMode, setIsTrainingMode] = useState(false);
  const [trainingDataCount, setTrainingDataCount] = useState(0);
  const [isClearing, setIsClearing] = useState(false);

  // Load training mode state from localStorage on mount
  useEffect(() => {
    const stored = localStorage.getItem('training_mode');
    if (stored === 'true') {
      setIsTrainingMode(true);
    }
  }, []);

  // Save training mode state to localStorage
  useEffect(() => {
    localStorage.setItem('training_mode', isTrainingMode.toString());
  }, [isTrainingMode]);

  const toggleTrainingMode = async () => {
    if (isTrainingMode) {
      // If turning off training mode, clear all training data first
      setIsClearing(true);
      try {
        const { deleteAllTrainingData } = await import('@/lib/db/v4/operations/training-ops');
        await deleteAllTrainingData();
        setTrainingDataCount(0);
      } catch (error) {
        console.error('Failed to clear training data when exiting training mode:', error);
      } finally {
        setIsClearing(false);
      }
    }
    setIsTrainingMode(!isTrainingMode);
  };

  const clearTrainingData = async () => {
    setIsClearing(true);
    try {
      // Import the delete function dynamically to avoid circular deps
      const { deleteAllTrainingData } = await import('@/lib/db/v4/operations/training-ops');
      await deleteAllTrainingData();
      setTrainingDataCount(0);
    } catch (error) {
      console.error('Failed to clear training data:', error);
      throw error;
    } finally {
      setIsClearing(false);
    }
  };

  // TODO: Add effect to count training data documents
  // This would query the database for documents with is_training: true

  return (
    <TrainingModeContext.Provider 
      value={{
        isTrainingMode,
        toggleTrainingMode,
        clearTrainingData,
        trainingDataCount,
        isClearing
      }}
    >
      {children}
    </TrainingModeContext.Provider>
  );
}

export function useTrainingMode() {
  const context = useContext(TrainingModeContext);
  if (context === undefined) {
    throw new Error('useTrainingMode must be used within a TrainingModeProvider');
  }
  return context;
}