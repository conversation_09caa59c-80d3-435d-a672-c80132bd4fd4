'use client';

import React, { createContext, useContext, ReactNode, useEffect } from 'react';
import { useSync } from '@/lib/hooks/useSync';
import type { LeaderStatus, LeaderConfig } from '@/lib/services/leader-sync-service';

interface SyncContextType {
  status: LeaderStatus;
  isRunning: boolean;
  isConnected: boolean;
  isSyncing: boolean;
  localServers: number;
  internetPeers: number;
  currentLeader: string | null;
  hasLeader: boolean;
  lastLocalDiscovery: Date | null;
  error: string | null;
  
  // Control functions
  start: () => Promise<void>;
  stop: () => Promise<void>;
  discover: () => Promise<void>;
  updateConfig: (config: Partial<LeaderConfig>) => void;
}

const SyncContext = createContext<SyncContextType | undefined>(undefined);

interface SyncProviderProps {
  children: ReactNode;
  config?: Partial<LeaderConfig>;
}

export function SyncProvider({ children, config }: SyncProviderProps) {
  const sync = useSync({
    autoStart: true,
    config: {
      localDiscoveryInterval: 30000,
      localReconnectInterval: 60000,
      internetDiscoveryInterval: 120000,
      internetReconnectInterval: 180000,
      maxReconnectAttempts: 5,
      preferLocalSync: true,
      vpsBaseUrl: 'https://bistro.icu',
      authToken: '',
      deviceId: `provider-${Date.now()}`,
      deviceType: 'mobile',
      ...config
    }
  });

  // Auto-start on mount if not already running
  useEffect(() => {
    if (!sync.isRunning) {
      console.log('👑 [SyncProvider] Auto-starting leader sync...');
      sync.start().catch(error => {
        console.error('👑 [SyncProvider] Auto-start failed:', error);
      });
    }
  }, [sync]);

  return (
    <SyncContext.Provider value={sync}>
      {children}
    </SyncContext.Provider>
  );
}

export function useSyncContext(): SyncContextType {
  const context = useContext(SyncContext);
  if (context === undefined) {
    throw new Error('useSyncContext must be used within a SyncProvider');
  }
  return context;
}

// Backward compatibility exports
export const AutonomousSyncProvider = SyncProvider;
export const useAutonomousSyncContext = useSyncContext;
export type AutonomousSyncContextType = SyncContextType;