/**
 * API route configuration based on build target
 * This helps determine if API routes should be static or dynamic
 */

// For static builds (electron, mobile, static), use 'force-static'
// For server builds (web, server, dev), use 'force-dynamic'
const isStaticBuild = process.env.BUILD_TARGET === 'static' || 
                     process.env.BUILD_TARGET === 'electron' || 
                     process.env.BUILD_TARGET === 'mobile';

export const dynamic = isStaticBuild ? 'force-static' : 'force-dynamic';
export const revalidate = false;