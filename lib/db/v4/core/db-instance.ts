"use client";

import { isCapacitorEnvironment } from '../../pouchdb-init';
import { cleanRestaurantId, getRestaurantDbName, formatDocId } from '../../db-utils';
import { isElectronEnvironment } from '../../electron-db';
import { v4 as uuidv4 } from 'uuid';

/**
 * V4 Database Instance
 *
 * Core database functionality for the v4 database implementation.
 * This class provides the foundation for all database operations.
 */
export class DatabaseV4 {
  private db: PouchDB.Database | null = null;
  private currentRestaurantId: string | null = null;
  private _isInitialized = false;
  private restaurantChangeListeners = new Set<(restaurantId: string | null) => void>();

  // --- NEW Helper Properties ---
  private _isElectron = false; // Renamed for clarity, matching getter
  private dbIdentifier = ''; // To store the PouchDB database name/identifier
  // --- END NEW Helper Properties ---

  // Add document initialization locks to prevent concurrent initialization
  private documentInitLocks = new Map<string, Promise<any>>();

  constructor() {
    if (typeof window !== 'undefined') {
      this._isElectron = isElectronEnvironment();
      console.log('DatabaseV4 constructor called, waiting for explicit initialization...');
    }
  }

  /**
   * Public getter for isElectron
   */
  public get isElectron(): boolean {
    return this._isElectron;
  }

  /**
   * Get the current restaurant ID
   */
  getCurrentRestaurantId(): string | null {
    return this.currentRestaurantId;
  }

  /**
   * Create essential indexes for optimal database performance
   * This method creates the most critical indexes needed for basic operations
   */
  private async createEssentialIndexes(): Promise<void> {
    console.log('⚡ [createEssentialIndexes] Creating comprehensive indexes for optimal performance...');
    
    // Comprehensive index set for all operations
    const allIndexes = [
      // Basic type indexes
      { fields: ['type'], name: 'basic-type-idx', ddoc: 'basic-type-idx' },
      
      // Order indexes
      { fields: ['type', 'status'], name: 'order-type-status-idx', ddoc: 'order-type-status-idx' },
      { fields: ['type', 'createdAt'], name: 'order-type-created-at-idx', ddoc: 'order-type-created-at-idx' },
      { fields: ['type', 'tableId'], name: 'order-type-tableid-idx', ddoc: 'order-type-tableid-idx' },
      { fields: ['type', 'paymentStatus'], name: 'order-type-paymentstatus-idx', ddoc: 'order-type-paymentstatus-idx' },
      { fields: ['createdAt'], name: 'order-pure-created-at-idx', ddoc: 'order-pure-created-at-idx' },
      { fields: ['status', 'createdAt'], name: 'order-status-created-at-idx', ddoc: 'order-status-created-at-idx' },
      { fields: ['type', 'status', 'createdAt'], name: 'order-type-status-created-at-idx', ddoc: 'order-type-status-created-at-idx' },
      
      // Inventory log indexes
      { fields: ['type', 'stockItemId'], name: 'log-type-stockitem-idx', ddoc: 'log-type-stockitem-idx' },
      { fields: ['type', 'stockItemId', 'createdAt'], name: 'log-type-stockitem-date-idx', ddoc: 'log-type-stockitem-date-idx' },
      { fields: ['type', 'createdAt'], name: 'log-type-date-idx', ddoc: 'log-type-date-idx' },
      { fields: ['type', 'orderId'], name: 'log-type-order-idx', ddoc: 'log-type-order-idx' },
      { fields: ['stockItemId', 'createdAt'], name: 'log-stockitem-date-idx', ddoc: 'log-stockitem-date-idx' },
      
      // Sales aggregation indexes
      { fields: ['type', 'date'], name: 'aggregation-daily-date-idx', ddoc: 'aggregation-daily-date-idx' },
      { fields: ['type', 'weekStartDate'], name: 'aggregation-weekly-date-idx', ddoc: 'aggregation-weekly-date-idx' },
      { fields: ['type', 'year', 'month'], name: 'aggregation-monthly-date-idx', ddoc: 'aggregation-monthly-date-idx' },
      { fields: ['type', 'menuItemId'], name: 'aggregation-item-performance-idx', ddoc: 'aggregation-item-performance-idx' }
    ];

    let successCount = 0;
    
    // Create indexes sequentially to avoid overwhelming the database
    for (const indexConfig of allIndexes) {
      try {
        await this.createIndexWithRetry(indexConfig.fields, indexConfig.name, indexConfig.ddoc, 2); // 2 retries max
        successCount++;
      } catch (error) {
        console.warn(`⚠️ [createEssentialIndexes] Failed '${indexConfig.name}' (non-critical):`, error instanceof Error ? error.message : String(error));
      }
    }

    console.log(`⚡ [createEssentialIndexes] Created ${successCount}/${allIndexes.length} indexes successfully`);
  }

  /**
   * Create an index with retry logic (reduced verbosity)
   */
  private async createIndexWithRetry(fields: string[], name?: string, ddoc?: string, maxRetries: number = 3): Promise<void> {
    const indexName = name || `idx-${fields.join('-')}`;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const indexDefinition: PouchDB.Find.CreateIndexOptions = {
          index: {
            fields,
            ...(name && { name }),
            ...(ddoc && { ddoc })
          }
        };

        await this.createIndex(indexDefinition);
        
        // Only log on first attempt success or final success
        if (attempt === 1 || attempt === maxRetries) {
          console.log(`[createEssentialIndexes] ✅ Created index '${indexName}'`);
        }
        return;
        
      } catch (error: any) {
        // Handle "already exists" as success (silent for reduced verbosity)
        if (error.status === 409 || (error.message && error.message.includes('already exists'))) {
          return; // Silent success for existing indexes
        }

        // Only log warnings on final attempt to reduce verbosity
        if (attempt === maxRetries) {
          console.warn(`[createEssentialIndexes] Failed index '${indexName}':`, error.message || 'Unknown error');
          throw error;
        }

        // Brief delay before retry
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 100 * attempt));
        }
      }
    }
  }

  /**
   * Subscribe to restaurant changes
   */
  subscribeToRestaurantChange(callback: (restaurantId: string | null) => void): () => void {
    this.restaurantChangeListeners.add(callback);

    // Immediately call with current value
    callback(this.currentRestaurantId);

    // Return unsubscribe function
    return () => {
      this.restaurantChangeListeners.delete(callback);
    };
  }

  /**
   * Initialize database with restaurant ID
   * Enhanced with better initialization status tracking and event handling
   */
  async initialize(restaurantId: string): Promise<void> {
    console.log('[DBv4.initialize] ENTERED with restaurantId:', restaurantId);
    if (typeof window !== 'undefined') {
      console.log('[DBv4.initialize] Called with restaurantId:', restaurantId);
      console.log('[DBv4.initialize] Stack trace:', new Error().stack);
    }

    if (typeof window === 'undefined') {
      console.log('[DBv4.initialize] Skipping database initialization in server context.');
      return;
    }

    // Clean the restaurant ID to ensure proper format
    // This prevents the 'restaurant-restaurant:UUID' issue
    let cleanedId = cleanRestaurantId(restaurantId);
    // Also handle "restaurant:" format if present
    if (cleanedId.startsWith('restaurant:')) {
      cleanedId = cleanedId.replace(/^restaurant:/, '');
    }
    // Ensure the ID is safe for PouchDB
    cleanedId = formatDocId(cleanedId);
    console.log(`[DBv4.initialize] Cleaned restaurant ID from '${restaurantId}' to '${cleanedId}'`);
    
    // Determine the DB identifier (always 'resto-UUID' format)
    const dbIdentifierToUse = `resto-${cleanedId}`;
    console.log(`[DBv4.initialize] Using cleaned restaurant ID format: ${dbIdentifierToUse}`);

    // Dispatch initialization start event for better tracking
    if (typeof window !== 'undefined') {
      document.dispatchEvent(new CustomEvent('v4-pouchdb-initializing', {
        detail: { restaurantId }
      }));
    }

    if (this._isInitialized && this.dbIdentifier && this.currentRestaurantId === restaurantId) {
      console.log(`[DBv4.initialize] Already initialized for restaurant: ${restaurantId}. dbIdentifier: ${this.dbIdentifier}`);
      // Even if already initialized, dispatch the success event for components waiting on it
      if (typeof window !== 'undefined') {
        document.dispatchEvent(new CustomEvent('v4-pouchdb-initialized', {
          detail: {
            success: true,
            restaurantId,
            dbName: this.dbIdentifier,
            isElectron: this._isElectron,
            alreadyInitialized: true
          }
        }));
      }
      console.log('[DBv4.initialize] EXIT (already initialized)');
      return;
    }

    // If switching restaurants, or first init, cleanup previous state first.
    if (this._isInitialized) {
      console.log(`[DBv4.initialize] Switching restaurant or re-initializing. Cleaning up previous DB state for ${this.currentRestaurantId}.`);
      console.log('[DBv4.initialize] Before await this.cleanup()');
      await this.cleanup(); // Ensures previous instance state is cleared.
      console.log('[DBv4.initialize] After await this.cleanup()');
    }

    try {
      console.log('[DBv4.initialize] TRY BLOCK ENTERED');
      console.log('[DBv4.initialize] Before setting currentRestaurantId');
      this.currentRestaurantId = restaurantId;
      // Notify listeners early about the target restaurant ID
      this.restaurantChangeListeners.forEach(listener => listener(this.currentRestaurantId));

      this.dbIdentifier = dbIdentifierToUse;
      console.log(`[DBv4.initialize] Target DB Identifier set to: ${this.dbIdentifier}`);

      if (this._isElectron) {
        console.log(`[DBv4.initialize] 🖥️ ELECTRON MODE for ${this.dbIdentifier} - using CouchDB via IPC.`);
        console.log(`[DBv4.initialize] 🛡️ Desktop Safety: Mobile PouchDB code will NOT execute.`);
        if (!(window as any).electronAPI?.database?.ensureDbOpened) {
          this._isInitialized = false; // Ensure state reflects failure
          console.error('[DBv4.initialize] CRITICAL_ELECTRON_API_MISSING: electronAPI.database.ensureDbOpened is not available. Preload script might have failed or API not exposed.');
          // Log the full electronAPI object for debugging
          console.error('[DBv4.initialize] window.electronAPI:', (window as any).electronAPI);
          throw new Error('electronAPI.database.ensureDbOpened is not available.');
        }

        console.log(`[DBv4.initialize] ELECTRON_IPC_CALL: Ensuring Electron PouchDB instance '${this.dbIdentifier}' is opened via electronAPI.database.ensureDbOpened.`);
        try {
          console.log('[DBv4.initialize] Before await electronAPI.database.ensureDbOpened');
          const ensureResponse = await (window as any).electronAPI.database.ensureDbOpened(this.dbIdentifier);
          console.log('[DBv4.initialize] After await electronAPI.database.ensureDbOpened');
          console.log(`[DBv4.initialize] ELECTRON_IPC_CALL RESULT for ensureDbOpened:`, ensureResponse);

          if (!ensureResponse || !ensureResponse.ok) {
            this._isInitialized = false; // Ensure state reflects failure
            const errorMsg = ensureResponse?.error?.message || 'Unknown error from main process during ensureDbOpened.';
            console.error(`[DBv4.initialize] ELECTRON_IPC_FAILED: Failed to ensure Electron PouchDB instance for ${this.dbIdentifier}. Error:`, errorMsg, 'Full response:', ensureResponse);
            throw new Error(`Main process failed to ensure PouchDB instance ${this.dbIdentifier} is ready: ${errorMsg}`);
          }

          console.log(`[DBv4.initialize] ELECTRON_IPC_SUCCESS: CouchDB instance ${this.dbIdentifier} ensured via IPC. Main process response:`, ensureResponse.message);
          this.db = null; // Explicitly null in Electron mode, DB lives in main process CouchDB.
          this._isInitialized = true; // Key: now initialized for IPC operations.
          console.log(`[DBv4.initialize] ELECTRON_MODE INITIALIZED for ${this.dbIdentifier} (CouchDB-only).`);
        } catch (ipcError) {
          this._isInitialized = false;
          console.error('[DBv4.initialize] ELECTRON_IPC_CALL ERROR:', ipcError);
          throw ipcError;
        }
      } else {
        console.log(`[DBv4.initialize] 🌐 BROWSER MODE for ${this.dbIdentifier}. Initializing PouchDB directly.`);

        const isMobile = isCapacitorEnvironment();
        console.log(`[DBv4.initialize] Environment: ${this._isElectron ? 'Electron' : isMobile ? 'Mobile' : 'Browser'}`);

        // Load PouchDB library dynamically to avoid build-time issues
        console.log(`[DBv4.initialize] Loading PouchDB library...`);
        const { initPouchDB } = await import('../../pouchdb-init');
        const PouchDB = await initPouchDB();

        if (!PouchDB) {
          this._isInitialized = false;
          console.error('[DBv4.initialize] ❌ PouchDB library loading failed');
          throw new Error('PouchDB library initialization failed');
        }

        console.log(`[DBv4.initialize] ✅ PouchDB library loaded successfully`);

        try {
          // Create PouchDB instance
          console.log(`[DBv4.initialize] Creating PouchDB instance for ${this.dbIdentifier}...`);

          if (isMobile) {
            console.log(`📱 [DBv4.initialize] Mobile: Using IndexedDB adapter`);
            console.log(`📱 [DBv4.initialize] Mobile: IndexedDB available:`, !!window.indexedDB);

            // Mobile-specific configuration
            this.db = new PouchDB(this.dbIdentifier, {
              adapter: 'idb',  // Force IndexedDB for mobile
              auto_compaction: true
            });
          } else {
            // Desktop/Browser configuration
            this.db = new PouchDB(this.dbIdentifier, {
              auto_compaction: true
            });
          }

          console.log(`[DBv4.initialize] ✅ PouchDB instance created successfully`);

          // Test database functionality
          console.log(`[DBv4.initialize] Testing database functionality...`);

          const testDoc = {
            _id: `init_test_${Date.now()}`,
            test: true,
            timestamp: Date.now(),
            environment: isMobile ? 'mobile' : 'desktop'
          };

          await this.db.put(testDoc);
          const retrieved = await this.db.get(testDoc._id);
          await this.db.remove(retrieved._id, retrieved._rev);

          console.log(`[DBv4.initialize] ✅ Database test successful - put/get/remove works`);

          // Verify sync capabilities for mobile - enhanced verification
          if (isMobile) {
            const hasSync = typeof this.db.sync === 'function';
            const hasReplicate = typeof this.db.replicate === 'object';
            const hasReplicateTo = this.db.replicate && typeof this.db.replicate.to === 'function';
            const hasReplicateFrom = this.db.replicate && typeof this.db.replicate.from === 'function';

            console.log(`📱 [DBv4.initialize] Mobile sync capabilities:`, { 
              hasSync, 
              hasReplicate, 
              hasReplicateTo, 
              hasReplicateFrom 
            });

            if (!hasSync) {
              this._isInitialized = false;
              console.error(`📱 [DBv4.initialize] ❌ Mobile PouchDB missing sync capability`);
              throw new Error('Mobile PouchDB missing sync capability');
            }

            if (!hasReplicate || !hasReplicateTo || !hasReplicateFrom) {
              this._isInitialized = false;
              console.error(`📱 [DBv4.initialize] ❌ Mobile PouchDB missing replication capabilities`);
              throw new Error('Mobile PouchDB missing replication capabilities');
            }

            // Test sync method exists and is callable
            try {
              // Verify sync method signature
              if (this.db.sync.length < 1) {
                throw new Error('Sync method has incorrect signature');
              }
              console.log(`📱 [DBv4.initialize] ✅ Mobile PouchDB sync methods verified`);
            } catch (syncTestError) {
              this._isInitialized = false;
              console.error(`📱 [DBv4.initialize] ❌ Mobile PouchDB sync method test failed:`, syncTestError);
              throw new Error('Mobile PouchDB sync method validation failed');
            }
          }

          // Mark as initialized
          this._isInitialized = true;

          if (isMobile) {
            console.log(`📱 [DBv4.initialize] ✅ Mobile PouchDB initialized successfully for ${this.dbIdentifier}`);
          } else {
            console.log(`[DBv4.initialize] ✅ Desktop PouchDB initialized successfully for ${this.dbIdentifier}`);
          }

        } catch (dbError) {
          this._isInitialized = false;
          console.error(`[DBv4.initialize] ❌ Database instance creation failed:`, dbError);
          
          if (isMobile) {
            console.error('📱 [DBv4.initialize] Mobile: PouchDB instance creation failed:', dbError);
            console.error('📱 [DBv4.initialize] Mobile: Environment debug:', {
              PouchDB: !!PouchDB,
              indexedDB: !!window.indexedDB,
              Capacitor: !!(window as any).Capacitor,
              userAgent: navigator.userAgent
            });
          }
          
          throw new Error(`Database instance creation failed: ${dbError instanceof Error ? dbError.message : String(dbError)}`);
        }
      }

      // Common success path for both Electron and Browser
      console.log(`[DBv4.initialize] DB INITIALIZATION SUCCEEDED for ${this.dbIdentifier} (isElectron: ${this._isElectron}). _isInitialized is now TRUE.`);
      
      // 🚀 Enhancement 1: Auto-create indexes after successful initialization
      console.log('[DBv4.initialize] 🔧 Creating essential indexes for optimal performance...');
      try {
        await this.createEssentialIndexes();
        console.log('[DBv4.initialize] ✅ Essential indexes created successfully');
      } catch (indexError) {
        console.warn('[DBv4.initialize] ⚠️ Index creation warning (may already exist):', indexError);
        // Don't fail initialization if indexes fail - they're performance optimizations
      }

      // 🚀 FIXED: Don't create default documents on startup - let operations create them when needed
      // This prevents conflicts with synced documents that might be better/newer
      console.log('[DBv4.initialize] 📄 Skipping default document creation - will be created on-demand');
      console.log('[DBv4.initialize] ✅ Database ready for on-demand document creation');
      
      if (typeof window !== 'undefined') {
        document.dispatchEvent(new CustomEvent('v4-pouchdb-initialized', {
          detail: {
            success: true,
            restaurantId,
            dbName: this.dbIdentifier,
            isElectron: this._isElectron,
            timestamp: Date.now()
          }
        }));
      }
      console.log('[DBv4.initialize] TRY BLOCK EXIT (success)');
    } catch (error) {
      // 🚨 CRITICAL FIX: Don't reset state on minor errors, only on critical failures
      const restaurantForErrorLog = this.currentRestaurantId || restaurantId || 'unknown_restaurant';
      console.error(`[DBv4.initialize] DB INITIALIZATION ERROR for ${restaurantForErrorLog} (dbName: ${this.dbIdentifier || 'N/A'}). Error:`, error);

      // Only reset state on critical errors that make the database completely unusable
      const isCriticalError = error instanceof Error && (
        error.message.includes('CRITICAL_ELECTRON_API_MISSING') ||
        error.message.includes('PouchDB library initialization failed') ||
        error.message.includes('Main process failed to ensure PouchDB instance')
      );

      if (isCriticalError) {
        console.error(`[DBv4.initialize] CRITICAL ERROR - Resetting database state`);
        this._isInitialized = false;
        this.dbIdentifier = '';
        this.currentRestaurantId = null;
      } else {
        console.warn(`[DBv4.initialize] Non-critical error - Keeping database state intact for recovery`);
      }

      if (typeof window !== 'undefined') {
        document.dispatchEvent(new CustomEvent('v4-pouchdb-initialized', {
          detail: {
            success: false,
            error,
            restaurantId: restaurantForErrorLog,
            timestamp: Date.now()
          }
        }));
      }
      console.log('[DBv4.initialize] CATCH BLOCK EXIT (failure)');
      throw error; // Re-throw the error to be caught by the caller (e.g., DatabaseProvider)
    }
    console.log('[DBv4.initialize] EXIT (end of function)');
  }

  /**
   * Clean up database connections
   */
  async cleanup(): Promise<void> {
    console.log(`[DBv4.cleanup] Called for dbIdentifier: '${this.dbIdentifier || 'N/A'}', currentRestaurantId: '${this.currentRestaurantId || 'N/A'}'. isElectron: ${this._isElectron}`);
    console.log('[DBv4.cleanup] Stack trace:', new Error().stack);

    const previousDbIdentifier = this.dbIdentifier;
    const previousRestaurantId = this.currentRestaurantId;

    if (this._isElectron) {
      // If there's an IPC call to explicitly tell the main process to close/release the DB instance, call it here.
      // For example: if ((window as any).electronAPI?.database?.closeDbInstance && previousDbIdentifier) {
      //   try {
      //     console.log(`[DBv4.cleanup] ELECTRON_IPC_CALL: Requesting close of Electron PouchDB instance '${previousDbIdentifier}' via IPC.`);
      //     await (window as any).electronAPI.database.closeDbInstance(previousDbIdentifier);
      //     console.log(`[DBv4.cleanup] ELECTRON_IPC_SUCCESS: Electron PouchDB instance '${previousDbIdentifier}' close requested via IPC.`);
      //   } catch (ipcCloseError) {
      //     console.error(`[DBv4.cleanup] ELECTRON_IPC_FAILED: Error requesting close for '${previousDbIdentifier}'. Error:`, ipcCloseError);
      //   }
      // }
      console.log(`[DBv4.cleanup] ELECTRON MODE: Main process manages DB instances. Renderer state will be reset.`);
    } else {
      // Browser mode: close the local PouchDB instance
      if (this.db && typeof this.db.close === 'function') {
        try {
          console.log(`[DBv4.cleanup] BROWSER_MODE: Closing local PouchDB instance for '${previousDbIdentifier}'.`);
          await this.db.close();
          console.log(`[DBv4.cleanup] BROWSER_MODE: Local PouchDB instance '${previousDbIdentifier}' closed.`);
        } catch (error) {
          console.error(`[DBv4.cleanup] BROWSER_MODE: Error closing database '${previousDbIdentifier}':`, error);
        }
      }
    }

    this.db = null;
    this.currentRestaurantId = null;
    this.dbIdentifier = '';
    this._isInitialized = false;

    // Notify listeners that the DB for the previous restaurant is no longer active/initialized.
    // Important: Pass the previousRestaurantId or null if it was already null.
    if (previousRestaurantId) { // Only notify if there was a previous restaurant context being cleaned up
        this.restaurantChangeListeners.forEach(listener => listener(null));
    }
    console.log(`[DBv4.cleanup] State reset. isInitialized: ${this._isInitialized}, dbIdentifier: '${this.dbIdentifier}', currentRestaurantId: '${this.currentRestaurantId}'.`);
  }

  /**
   * Get database instance (returns null in Electron mode for renderer)
   *
   * IMPORTANT: In Electron mode, this will return null because the database
   * instance lives in the main process. In this case, you should use the
   * other methods of this class (getDoc, putDoc, etc.) which handle the
   * IPC communication with the main process.
   *
   * In browser mode, this returns the actual PouchDB instance.
   */
  getDatabase(): PouchDB.Database | null {
    // Enhanced debug logging
    const electronState = {
      _isElectron: this._isElectron,
      IS_DESKTOP_APP: typeof window !== 'undefined' ? Boolean((window as any).IS_DESKTOP_APP) : false,
      electronAPI: typeof window !== 'undefined' && (window as any).electronAPI ? 
        { database: Boolean((window as any).electronAPI.database) } : false
    };
    
    console.log(`[DBv4.getDatabase] 🔍 State check before DB access: isInitialized=${this._isInitialized}, restaurantId=${this.currentRestaurantId}, dbIdentifier=${this.dbIdentifier}`);
    console.log(`[DBv4.getDatabase] 🔍 Electron state:`, electronState);

    // In Electron mode, this.db is null in the renderer. Operations go via IPC.
    // For browser mode, it returns the local PouchDB instance if initialized.
    if (!this._isInitialized) {
      console.warn(`[DBv4.getDatabase] Attempted to get DB when _isInitialized is false. Current restaurant: ${this.currentRestaurantId}, dbIdentifier: ${this.dbIdentifier}`);
      return null; // Always return null if not initialized
    }

    // Re-check electron state as a safety measure
    const recheck = isElectronEnvironment();
    if (recheck !== this._isElectron) {
      console.warn(`[DBv4.getDatabase] ⚠️ Electron state mismatch detected: constructor=${this._isElectron}, current=${recheck}`);
      // Consider updating the internal state if needed
    }

    if (this._isElectron) {
      // In Electron mode, we need to create a proxy object that mimics PouchDB
      // but forwards all operations to the main process CouchDB via IPC
      console.log('[DBv4.getDatabase] Electron mode: Creating proxy PouchDB object for CouchDB IPC operations');

      // Verify the IPC channel is available
      if (!(window as any).electronAPI?.database) {
        console.error('[DBv4.getDatabase] ❌ electronAPI.database not available - electron IPC channels may not be initialized!');
        return null;
      }

      // Create a minimal proxy object that has the basic PouchDB methods
      // This allows code that expects a PouchDB instance to work in Electron mode
      const proxyDb: any = {
        get: async (id: string) => this.getDoc(id),
        put: async (doc: any) => this.putDoc(doc),
        remove: async (docId: string, rev: string) => this.deleteDoc(docId, rev),
        bulkDocs: async (docs: any[]) => this.bulkDocs(docs),
        find: async (query: any) => this.findDocs(query),
        createIndex: async (indexDef: any) => this.createIndex(indexDef),
        
        // CouchDB attachment methods
        putAttachment: async (docId: string, attachmentName: string, rev: string, data: ArrayBuffer, contentType: string) => 
          this.putAttachment(docId, attachmentName, rev, data, contentType),
        getAttachment: async (docId: string, attachmentName: string) => 
          this.getAttachment(docId, attachmentName),
        removeAttachment: async (docId: string, attachmentName: string, rev: string) => 
          this.removeAttachment(docId, attachmentName, rev),

        // Add other methods as needed

        // Add a flag to identify this as a proxy
        _isElectronProxy: true,
        _dbIdentifier: this.dbIdentifier
      };

      return proxyDb as any;
    }

    if (!this.db) {
      console.warn(`[DBv4.getDatabase] Browser mode: this.db is null. _isInitialized: ${this._isInitialized}`);
      return null;
    }

    return this.db;
  }

  /**
   * Get PouchDB instance specifically for sync operations
   * This ensures the instance is properly initialized and has sync capabilities
   */
  getPouchDBForSync(): PouchDB.Database | null {
    if (!this._isInitialized) {
      console.warn(`[DBv4.getPouchDBForSync] Database not initialized`);
      return null;
    }

    if (this._isElectron) {
      console.warn(`[DBv4.getPouchDBForSync] Sync operations in Electron mode should use CouchDB directly`);
      return null;
    }

    const isMobile = isCapacitorEnvironment();
    
    // 🚀 FIX: Allow sync on desktop browsers for testing
    if (!isMobile && typeof window !== 'undefined') {
      console.log(`🌐 [DBv4.getPouchDBForSync] Desktop browser sync enabled for testing`);
      
      if (!this.db) {
        console.warn(`[DBv4.getPouchDBForSync] Desktop PouchDB instance not available`);
        return null;
      }

      // Verify sync capabilities
      const hasSync = typeof this.db.sync === 'function';
      if (!hasSync) {
        console.error(`[DBv4.getPouchDBForSync] Desktop PouchDB instance lacks sync capability`);
        return null;
      }

      console.log(`🌐 [DBv4.getPouchDBForSync] Returning verified desktop PouchDB instance for sync`);
      return this.db;
    }

    if (!this.db) {
      console.warn(`[DBv4.getPouchDBForSync] PouchDB instance not available`);
      return null;
    }

    // Verify sync capabilities one more time
    const hasSync = typeof this.db.sync === 'function';
    if (!hasSync) {
      console.error(`[DBv4.getPouchDBForSync] PouchDB instance lacks sync capability`);
      return null;
    }

    console.log(`📱 [DBv4.getPouchDBForSync] Returning verified PouchDB instance for sync`);
    return this.db;
  }

  /**
   * Check if a document exists
   */
  async checkDocumentExists(docId: string): Promise<boolean> {
    if (!this._isInitialized || !this.dbIdentifier) {
      console.error('[DBv4.checkDocumentExists] PRECONDITION_FAILED: Database not initialized or dbIdentifier not set. isInitialized:', this._isInitialized, 'dbIdentifier:', this.dbIdentifier);
      throw new Error('Database not initialized or dbIdentifier not set for checkDocumentExists.');
    }

    if (this._isElectron) {
      if (!(window as any).electronAPI?.database?.get) {
        console.error('[DBv4.checkDocumentExists] PRECONDITION_FAILED: electronAPI.database.get is not available for Electron mode existence check.');
        throw new Error('electronAPI.database.get is not available for checkDocumentExists in Electron.');
      }
      try {
        console.log(`[DBv4.checkDocumentExists] IPC_CALL: Checking existence of '${docId}' in '${this.dbIdentifier}' via electronAPI.database.get.`);
        await (window as any).electronAPI.database.get(this.dbIdentifier, docId);
        return true; // Document exists
      } catch (error: any) {
        if (error.status === 404 || (error.message && error.message.includes('missing')) || (error.name && error.name === 'not_found')) {
          return false; // Document does not exist
        }
        console.error(`[DBv4.checkDocumentExists] IPC_ERROR: Error checking document '${docId}' in Electron:`, error);
        throw error; // Re-throw other errors
      }
    } else {
      const browserDb = this.getDatabase();
      if (!browserDb) {
        console.error('[DBv4.checkDocumentExists] BROWSER_MODE_ERROR: PouchDB instance is null. Initialization might have failed.');
        throw new Error('Browser PouchDB instance not available for checkDocumentExists.');
      }
      try {
        await browserDb.get(docId);
        return true;
      } catch (error: any) {
        if (error.status === 404) {
          return false;
        }
        console.error(`[DBv4.checkDocumentExists] BROWSER_MODE_ERROR: Error getting document '${docId}':`, error);
        throw error;
      }
    }
  }

  /**
   * Get a document by ID
   */
  async getDoc<T>(docId: string): Promise<T> {
    console.log('[getDoc] dbIdentifier:', this.dbIdentifier, 'currentRestaurantId:', this.currentRestaurantId, 'docId:', docId, 'Stack:', new Error().stack?.split('\n').slice(1,3).join(' | '));
    if (!this._isInitialized || !this.dbIdentifier) {
      console.error('[DBv4.getDoc] PRECONDITION_FAILED: Database not initialized or dbIdentifier not set. isInitialized:', this._isInitialized, 'dbIdentifier:', this.dbIdentifier);
      throw new Error('Database not initialized or dbIdentifier not set for getDoc.');
    }

    if (this._isElectron) {
      if (!(window as any).electronAPI?.database?.get) {
        console.error('[DBv4.getDoc] PRECONDITION_FAILED: electronAPI.database.get is not available.');
        throw new Error('electronAPI.database.get is not available for getDoc.');
      }
      console.log(`[DBv4.getDoc] IPC_GET_CALLING: Electron IPC for db '${this.dbIdentifier}', docId '${docId}'.`);
      let result;
      try {
        console.log(`[DBv4.getDoc] IPC_GET_BEFORE_AWAIT: About to await electronAPI.database.get for docId '${docId}'.`);
        result = await (window as any).electronAPI.database.get(this.dbIdentifier, docId);
        console.log(`[DBv4.getDoc] IPC_GET_AFTER_AWAIT: Completed await for electronAPI.database.get for docId '${docId}'. Raw result:`, result);
      } catch (ipcError) {
        console.error(`[DBv4.getDoc] IPC_GET_ERROR_AWAIT: Error during await electronAPI.database.get for docId '${docId}'. Error:`, ipcError);
        throw ipcError; // Re-throw error from the IPC call itself
      }

      if (result && result.ok && result.data) {
        console.log(`[DBv4.getDoc] IPC_GET_SUCCESS: Call for '${docId}' was OK. Returning data.`);
        return result.data as T;
      } else if (result && !result.ok && result.error) {
        console.error(`[DBv4.getDoc] IPC_GET_FAILED_RESPONSE: Call for '${docId}' returned known error structure:`, result.error);
        
        // Create a detailed error that matches what PouchDB would throw
        const error: any = new Error(result.error.message || 'Document not found or error in main process for getDoc');
        error.status = result.error.status || 404; // Default to 404 if not specified
        error.name = result.error.name || 'not_found'; // Default to not_found
        
        // Let real errors bubble up instead of masking them
        
        // Add additional debugging info for settings-specific errors
        if (docId === 'restaurant-settings') {
          console.error(`[DBv4.getDoc] 🔍 SETTINGS ERROR DEBUG: docId=${docId}, dbIdentifier=${this.dbIdentifier}, error=`, {
            status: error.status,
            name: error.name,
            message: error.message,
            originalError: result.error
          });
        }
        
        throw error;
      }
      // This case means result is undefined, null, or an unexpected structure
      console.error(`[DBv4.getDoc] IPC_GET_UNEXPECTED_RESPONSE: Unexpected response structure from get API for '${docId}'. Result:`, result);
      throw new Error(`Failed to get document '${docId}' via Electron IPC due to unexpected or missing response structure.`);
    } else {
      // Browser path with defensive error handling
      if (!this.db) {
        console.error('[DBv4.getDoc] Browser PouchDB instance not available.');
        throw new Error('Browser PouchDB instance not available.');
      }
      console.log(`[DBv4.getDoc] BROWSER_PATH: Calling this.db.get('${docId}')`);
      
      try {
        const document = await this.db.get<T>(docId);
        return document;
      } catch (error: any) {
        // Re-throw errors as-is - don't mask real issues
        throw error;
      }
    }
  }

  /**
   * Put a document (upsert: avoids 409 conflict by always using latest _rev if exists)
   * knowledge: This logic prevents PouchDB/Electron 409 conflict errors for singleton docs.
   */
  async putDoc<T extends {}>(doc: T & Partial<PouchDB.Core.IdMeta> & Partial<PouchDB.Core.RevisionIdMeta>): Promise<PouchDB.Core.Response> {
    // knowledge: upsert logic start
    const docWithId = { ...doc };
    if (!docWithId._id) {
      docWithId._id = uuidv4();
      console.warn(`[DBv4.putDoc] Document was missing _id. Generated new _id: ${docWithId._id}`, docWithId);
    }
    let latestRev: string | undefined = undefined;
    try {
      // Try to get the latest doc to fetch _rev
      const existing = await this.getDoc<any>(docWithId._id);
      // Safely check if existing is defined and has _rev property
      if (existing && typeof existing === 'object' && existing._rev) {
        latestRev = existing._rev;
      }
    } catch (err: any) {
      if (err.status !== 404 && err.name !== 'not_found') {
        // Only ignore not found errors - let real errors bubble up
        console.warn(`[DBv4.putDoc] Unexpected error getting existing document ${docWithId._id}:`, err);
        throw err;
      }
    }
    // 🚨 CRITICAL FIX: Add conflict detection for document updates
    if (latestRev) {
      // If document already has a _rev and it doesn't match latest, this is a conflict
      if (docWithId._rev && docWithId._rev !== latestRev) {
        console.warn(`[DBv4.putDoc] Document conflict detected for ${docWithId._id}: expected ${docWithId._rev}, found ${latestRev}`);
        // For now, use latest revision but log the conflict
        // TODO: Implement proper conflict resolution strategy
      }
      docWithId._rev = latestRev;
    } else {
      delete docWithId._rev; // Ensure no _rev on new docs
    }
    // knowledge: upsert logic end
    if (!this._isInitialized || !this.dbIdentifier) {
      console.error('[DBv4.putDoc] Database not initialized or dbIdentifier not set.');
      throw new Error('Database not initialized or dbIdentifier not set.');
    }
    if (this._isElectron) {
      if (!(window as any).electronAPI?.database?.put) {
        console.error('[DBv4.putDoc] electronAPI.database.put is not available.');
        throw new Error('electronAPI.database.put is not available.');
      }
      console.log(`[DBv4.putDoc] Electron IPC: Calling electronAPI.database.put('${this.dbIdentifier}', docId: '${docWithId._id}')`);
      const responseFromMain = await (window as any).electronAPI.database.put(this.dbIdentifier, docWithId);
      console.log('[DBv4.putDoc] Electron IPC: Received result for putDoc(' + docWithId._id + '):', responseFromMain);
      if (responseFromMain && responseFromMain.ok && responseFromMain.data) {
        console.log(`[DBv4.putDoc] Electron IPC: Call for '${docWithId._id}' was OK. Returning data.`);
        return responseFromMain.data as PouchDB.Core.Response;
      } else if (responseFromMain && !responseFromMain.ok && responseFromMain.error) {
        console.error('[DBv4.putDoc] Electron IPC: Call for ' + docWithId._id + ' returned error:', responseFromMain.error);
        const error: any = new Error(responseFromMain.error.message || 'Failed to put document in main process');
        error.status = responseFromMain.error.status || 500;
        error.name = responseFromMain.error.name || 'db_error';
        throw error;
      }
      console.error('[DBv4.putDoc] Electron IPC: Unexpected response structure from put API for ' + docWithId._id + ':', responseFromMain);
      throw new Error('Failed to put document via Electron IPC due to unexpected response structure.');
    } else {
      if (!this.db) {
        console.error('[DBv4.putDoc] Browser PouchDB instance not available.');
        throw new Error('Browser PouchDB instance not available.');
      }
      return this.db.put(docWithId);
    }
  }

  /**
   * Bulk docs operation
   */
  async bulkDocs<T extends {}>(docs: (T & Partial<PouchDB.Core.IdMeta> & Partial<PouchDB.Core.RevisionIdMeta>)[]): Promise<(PouchDB.Core.Response | PouchDB.Core.Error)[]> {
    this.ensureInitialized();
    console.log('[bulkDocs] Called with', docs.length, 'docs:', docs.map(d => d._id));
    if (this._isElectron) {
      if (!(window as any).electronAPI?.database?.bulkDocs) {
        throw new Error('electronAPI.database.bulkDocs is not available.');
      }
      console.log(`[DBv4.bulkDocs] Electron path for ${docs.length} docs in ${this.dbIdentifier}`);
      const response = await (window as any).electronAPI.database.bulkDocs(this.dbIdentifier, { docs });
      if (!response.ok && response.error) { // Check for a general error from the IPC call itself
        const error: any = new Error(response.error.message || 'Bulk docs operation failed');
        error.status = response.error.status || 500;
        error.name = response.error.name || 'bulk_error';
        console.error(`[DBv4.bulkDocs] General error from Electron main:`, response.error);
        throw error;
      }
      // response.data contains the array of results from PouchDB
      // Log if there was a warning about partial failures
      if (response.warning) {
          console.warn(`[DBv4.bulkDocs] Warning from Electron main: ${response.warning}`, response.errors);
      }
      return response.data as (PouchDB.Core.Response | PouchDB.Core.Error)[];
    } else if (this.db) {
      const now = new Date().toISOString();
      const docsToPut = docs.map(doc => ({
        ...doc,
        _id: (doc as any)._id || uuidv4(),
        updatedAt: now,
        createdAt: (doc as any).createdAt || now,
      }));
      try {
        const result = await this.db.bulkDocs(docsToPut);
        console.log('[bulkDocs] Result:', result);
        return result;
      } catch (err) {
        console.error('[bulkDocs] Error:', err);
        throw err;
      }
    }
    throw new Error('Database not available.');
  }

  /**
   * Create an index (robust: always ensure DB is open, retry if needed)
   */
  async createIndex(indexDefinition: PouchDB.Find.CreateIndexOptions): Promise<PouchDB.Find.CreateIndexResponse<{}>> {
    this.ensureInitialized();
    const maxRetries = 3;
    let lastError: any = null;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (this._isElectron) {
          // Always ensure DB is open before creating index
          if ((window as any).electronAPI?.database?.ensureDbOpened) {
            await (window as any).electronAPI.database.ensureDbOpened(this.dbIdentifier);
          }
          if (!(window as any).electronAPI?.database?.createIndex) {
            console.error(`[DBv4.createIndex] [Electron] CRITICAL: electronAPI.database.createIndex is not available for dbIdentifier: ${this.dbIdentifier}`);
            throw new Error('electronAPI.database.createIndex is not available.');
          }
          console.log(`[DBv4.createIndex] [Electron] Attempting to create index in '${this.dbIdentifier}' (attempt ${attempt}):`, JSON.stringify(indexDefinition));
          const response = await (window as any).electronAPI.database.createIndex(this.dbIdentifier, indexDefinition);
          console.log(`[DBv4.createIndex] [Electron] Response from main for '${this.dbIdentifier}' index:`, JSON.stringify(response));
          if (!response.ok) {
            console.warn(`[DBv4.createIndex] [Electron] Issue from Electron main (e.g. index might exist):`, response.error);
            const error: any = new Error(response.error.message || 'Failed to create index');
            error.status = response.error.status || 500;
            error.name = response.error.name || 'index_error';
            error.fullResponse = response;
            throw error;
          }
          return response.data as PouchDB.Find.CreateIndexResponse<{}>;
        } else if (this.db) {
          console.log(`[DBv4.createIndex] [Browser] Attempting to create index in '${this.dbIdentifier}':`, JSON.stringify(indexDefinition));
          const response = await this.db.createIndex(indexDefinition);
          console.log(`[DBv4.createIndex] [Browser] Index created:`, JSON.stringify(response));
          return response;
        }
        throw new Error('Database not available.');
      } catch (err: any) {
        lastError = err;
        console.error(`[DBv4.createIndex] Attempt ${attempt} failed for dbIdentifier: ${this.dbIdentifier}, index: ${JSON.stringify(indexDefinition)}. Error:`, err && err.stack ? err.stack : err);
        if (attempt < maxRetries && (err.message?.includes('not available') || err.message?.includes('not initialized') || err.message?.includes('No handler registered'))) {
          console.warn(`[DBv4.createIndex] Retry attempt ${attempt} failed, will retry:`, err.message);
          await new Promise(res => setTimeout(res, 500 * attempt));
          continue;
        }
        break;
      }
    }
    console.error('[DBv4.createIndex] FATAL: Failed to create index after retries:', lastError && lastError.stack ? lastError.stack : lastError);
    throw lastError;
  }

  /**
   * Find documents
   */
  async findDocs<T extends {} = {}>(query: PouchDB.Find.FindRequest<T>): Promise<PouchDB.Find.FindResponse<T>> {
    this.ensureInitialized();
    if (this._isElectron) {
      if (!(window as any).electronAPI?.database?.find) {
        throw new Error('electronAPI.database.find is not available.');
      }
      console.log(`[DBv4.findDocs] Electron path for query in ${this.dbIdentifier}:`, query);
      const response = await (window as any).electronAPI.database.find(this.dbIdentifier, query);
      if (!response.ok) {
        const error: any = new Error(response.error.message || 'Failed to find documents');
        error.status = response.error.status || 500;
        error.name = response.error.name || 'find_error';
        console.error(`[DBv4.findDocs] IPC_FIND_FAILED_RESPONSE: Call for '${this.dbIdentifier}' returned known error structure:`, response.error);
        throw error;
      }
      
      return response.data as PouchDB.Find.FindResponse<T>;
    } else if (this.db) {
      try {
        const result = await this.db.find(query);
        return result as PouchDB.Find.FindResponse<T>;
      } catch (error) {
        console.error(`[DBv4.findDocs] Browser find error:`, error);
        throw error;
      }
    }
    throw new Error('Database not available.');
  }

  /**
   * Put an attachment to a document
   */
  async putAttachment(
    docId: string,
    attachmentName: string,
    rev: string,
    attachmentData: ArrayBuffer,
    contentType: string
  ): Promise<PouchDB.Core.Response> {
    if (!this._isInitialized || !this.dbIdentifier) {
      throw new Error('Database not initialized or dbIdentifier not set for putAttachment.');
    }

    if (this._isElectron) {
      if (!(window as any).electronAPI?.database?.putAttachment) {
        console.warn('[DBv4.putAttachment] electronAPI.database.putAttachment is not available in Electron - attachment methods may not be implemented in main process');
        throw new Error('CouchDB attachments not yet supported in Electron mode. Please use browser mode for image attachments.');
      }
      console.log(`[DBv4.putAttachment] Electron path for docId: ${docId}, attachment: ${attachmentName}`);
      
      try {
        // Convert ArrayBuffer to base64 string for Electron IPC
        const buffer = Buffer.from(attachmentData);
        const base64Data = buffer.toString('base64');
        
        // Note: Electron putAttachment doesn't use rev parameter - it handles revision internally
        const response = await (window as any).electronAPI.database.putAttachment(
          this.dbIdentifier,
          docId,
          attachmentName,
          base64Data,
          contentType
        );
        
        // Check if response is valid
        if (!response || typeof response !== 'object') {
          console.error('[DBv4.putAttachment] Invalid response from Electron main:', response);
          throw new Error('Invalid response from Electron main process - attachment methods may not be implemented');
        }
        
        if (!response.ok) {
          const error: any = new Error(response.error?.message || 'Failed to put attachment');
          error.status = response.error?.status || 500;
          error.name = response.error?.name || 'attachment_error';
          console.error(`[DBv4.putAttachment] Error from Electron main:`, response.error);
          throw error;
        }
        return response as PouchDB.Core.Response;
      } catch (ipcError: any) {
        console.error('[DBv4.putAttachment] IPC error:', ipcError);
        if (ipcError.message?.includes('JSON')) {
          throw new Error('CouchDB attachments not yet implemented in Electron main process. Please use browser mode for image attachments.');
        }
        throw ipcError;
      }
    } else if (this.db) {
      return await this.db.putAttachment(docId, attachmentName, rev, attachmentData, contentType);
    }
    throw new Error('Database not available.');
  }

  /**
   * Get an attachment from a document
   */
  async getAttachment(docId: string, attachmentName: string): Promise<Blob> {
    if (!this._isInitialized || !this.dbIdentifier) {
      throw new Error('Database not initialized or dbIdentifier not set for getAttachment.');
    }

    if (this._isElectron) {
      if (!(window as any).electronAPI?.database?.getAttachment) {
        console.warn('[DBv4.getAttachment] electronAPI.database.getAttachment is not available in Electron');
        throw new Error('CouchDB attachments not yet supported in Electron mode. Please use browser mode for image attachments.');
      }
      console.log(`[DBv4.getAttachment] Electron path for docId: ${docId}, attachment: ${attachmentName}`);
      
      try {
        const response = await (window as any).electronAPI.database.getAttachment(
          this.dbIdentifier,
          docId,
          attachmentName
        );
        
        if (!response || typeof response !== 'object') {
          console.error('[DBv4.getAttachment] Invalid response from Electron main:', response);
          throw new Error('Invalid response from Electron main process');
        }
        
        if (!response.ok) {
          const error: any = new Error(response.error?.message || 'Failed to get attachment');
          error.status = response.error?.status || 404;
          error.name = response.error?.name || 'not_found';
          console.error(`[DBv4.getAttachment] Error from Electron main:`, response.error);
          throw error;
        }
        
        // Convert base64 back to Blob
        const base64Data = response.data;
        const contentType = response.content_type || 'application/octet-stream';
        const binaryString = atob(base64Data);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        return new Blob([bytes], { type: contentType });
      } catch (ipcError: any) {
        console.error('[DBv4.getAttachment] IPC error:', ipcError);
        if (ipcError.message?.includes('JSON')) {
          throw new Error('CouchDB attachments not yet implemented in Electron main process');
        }
        throw ipcError;
      }
    } else if (this.db) {
      return await this.db.getAttachment(docId, attachmentName) as Blob;
    }
    throw new Error('Database not available.');
  }

  /**
   * Remove an attachment from a document
   */
  async removeAttachment(docId: string, attachmentName: string, rev: string): Promise<PouchDB.Core.Response> {
    if (!this._isInitialized || !this.dbIdentifier) {
      throw new Error('Database not initialized or dbIdentifier not set for removeAttachment.');
    }

    if (this._isElectron) {
      if (!(window as any).electronAPI?.database?.removeAttachment) {
        console.warn('[DBv4.removeAttachment] electronAPI.database.removeAttachment is not available in Electron');
        throw new Error('CouchDB attachments not yet supported in Electron mode. Please use browser mode for image attachments.');
      }
      console.log(`[DBv4.removeAttachment] Electron path for docId: ${docId}, attachment: ${attachmentName}`);
      
      try {
        const response = await (window as any).electronAPI.database.removeAttachment(
          this.dbIdentifier,
          docId,
          attachmentName,
          rev
        );
        
        if (!response || typeof response !== 'object') {
          console.error('[DBv4.removeAttachment] Invalid response from Electron main:', response);
          throw new Error('Invalid response from Electron main process');
        }
        
        if (!response.ok) {
          const error: any = new Error(response.error?.message || 'Failed to remove attachment');
          error.status = response.error?.status || 500;
          error.name = response.error?.name || 'attachment_error';
          console.error(`[DBv4.removeAttachment] Error from Electron main:`, response.error);
          throw error;
        }
        return response as PouchDB.Core.Response;
      } catch (ipcError: any) {
        console.error('[DBv4.removeAttachment] IPC error:', ipcError);
        if (ipcError.message?.includes('JSON')) {
          throw new Error('CouchDB attachments not yet implemented in Electron main process');
        }
        throw ipcError;
      }
    } else if (this.db) {
      return await this.db.removeAttachment(docId, attachmentName, rev);
    }
    throw new Error('Database not available.');
  }

  /**
   * Delete a document
   */
  async deleteDoc(docId: string, rev: string): Promise<PouchDB.Core.Response> {
    if (!this._isInitialized || !this.dbIdentifier) {
      console.error('[DBv4.deleteDoc] PRECONDITION_FAILED: Database not initialized or dbIdentifier not set. isInitialized:', this._isInitialized, 'dbIdentifier:', this.dbIdentifier);
      throw new Error('Database not initialized or dbIdentifier not set for deleteDoc.');
    }

    if (this._isElectron) {
      if (!(window as any).electronAPI?.database?.remove) {
        throw new Error('electronAPI.database.remove is not available.');
      }
      if (!rev) {
        // In Electron mode, rev is strictly required by the IPC call.
        // Fetching it here would involve another IPC call, better to enforce it from the caller.
        console.error('[DBv4.deleteDoc] Electron path: Revision is strictly required for remove IPC call.');
        const err: any = new Error('Document revision is required for delete in Electron mode');
        err.status = 400;
        err.name = 'bad_request';
        throw err;
      }
      console.log(`[DBv4.deleteDoc] Electron path for docId: ${docId}, rev: ${rev} in ${this.dbIdentifier}`);
      const response = await (window as any).electronAPI.database.remove(this.dbIdentifier, docId, rev);
      if (!response.ok) {
        const error: any = new Error(response.error.message || 'Failed to delete document');
        error.status = response.error.status || 500;
        error.name = response.error.name || 'delete_error';
        console.error(`[DBv4.deleteDoc] Error from Electron main:`, response.error);
        throw error;
      }
      return response.data as PouchDB.Core.Response;
    } else if (this.db) {
      if (!rev) {
        // PouchDB in browser also requires rev for delete. Fetch if not provided.
        console.warn(`[DBv4.deleteDoc] Browser path: rev not provided for ${docId}. Fetching document first.`);
        try {
          const docToDel = await this.db.get(docId);
          rev = docToDel._rev!;
        } catch (getError) {
          console.error(`[DBv4.deleteDoc] Browser path: Failed to fetch doc ${docId} to get its revision for delete.`, getError);
          throw getError; // Propagate the get error
        }
      }
      return await this.db.remove(docId, rev);
    }
    throw new Error('Database not available.');
  }

  /**
   * Getter for initialization state.
   * In Electron mode, true means IPC is ready for the current DB.
   * In Browser mode, true means local PouchDB is instantiated and ready.
   */
  get isInitialized(): boolean {
    if (this._isElectron) {
      return this._isInitialized; // For Electron, _isInitialized means IPC setup for the DB is done.
    }
    // For browser, also check if the db object itself is created.
    return this._isInitialized && !!this.db;
  }

  /**
   * Internal setter for _isInitialized flag. Should only be used by methods within this class.
   * This setter is problematic and typically a class manages its ready state internally.
   * CONSIDER REMOVING THIS SETTER if possible and manage _isInitialized strictly within initialize/cleanup.
   */
  set isInitialized(value: boolean) {
    // console.warn("[DBv4.setIsInitialized] Directly setting _isInitialized. This should be carefully managed.", value);
    this._isInitialized = value;
    if (!value) {
        // If being set to false, perhaps also clear this.db and this.dbIdentifier?
        // This depends on the desired behavior when externally forced to uninitialized.
    }
  }

  /**
   * Ensure database is initialized
   */
  ensureInitialized(): void {
    const currentId = this.getCurrentRestaurantId();
    console.log(`[DBv4.ensureInitialized] Checking state. CurrentId: ${currentId}, isInitialized (getter): ${this.isInitialized}, dbIdentifier: ${this.dbIdentifier}, _isElectron: ${this._isElectron}`);

    if (!currentId) {
      console.warn('[DBv4.ensureInitialized] No current restaurant ID. Database cannot be considered initialized.');
      // If _isInitialized was true, this state is inconsistent.
      if (this._isInitialized) {
        console.error('[DBv4.ensureInitialized] Inconsistency: _isInitialized is true but no currentRestaurantId.');
        // Consider calling cleanup or setting _isInitialized = false here.
      }
      throw new Error('No current restaurant ID available for database operations');
    }

    const targetDbIdentifier = `resto-${cleanRestaurantId(currentId)}`;
    if (this.isInitialized && this.dbIdentifier === targetDbIdentifier) {
      console.log(`[DBv4.ensureInitialized] Already initialized for ${currentId}.`);
      return;
    }

    // If we reach here, the database is not properly initialized
    console.error(`[DBv4.ensureInitialized] Database not properly initialized. Expected: ${targetDbIdentifier}, Current: ${this.dbIdentifier}, isInitialized: ${this.isInitialized}`);
    throw new Error(`Database not initialized for restaurant ${currentId}. Expected: ${targetDbIdentifier}, Current: ${this.dbIdentifier || 'none'}`);
  }

  /**
   * Wait for database initialization
   * This is a helper method that can be used by components to wait for database initialization
   * @param restaurantId The restaurant ID to wait for
   * @param timeoutMs Maximum time to wait in milliseconds (default: 30000ms)
   * @returns Promise that resolves when database is initialized, or rejects on timeout
   */
  waitForInitialization(restaurantId: string, timeoutMs: number = 30000): Promise<void> {
    return new Promise((resolve, reject) => {
      // First check: If already initialized for this restaurant, resolve immediately
      if (this._isInitialized && this.currentRestaurantId === restaurantId) {
        console.log(`[DBv4.waitForInitialization] Already initialized for ${restaurantId}, resolving immediately`);
        resolve();
        return;
      }

      // Second check: If the database name matches what we expect for this restaurant ID
      const expectedDbIdentifier = `resto-${cleanRestaurantId(restaurantId)}`;
      if (this._isInitialized && this.dbIdentifier === expectedDbIdentifier) {
        console.log(`[DBv4.waitForInitialization] Database is initialized with the expected identifier ${expectedDbIdentifier}, resolving immediately`);
        resolve();
        return;
      }
      
      console.log(`[DBv4.waitForInitialization] Starting wait with timeout of ${timeoutMs}ms for restaurant ${restaurantId}`);

      // Set up timeout
      const timeoutId = setTimeout(() => {
        document.removeEventListener('v4-pouchdb-initialized', handleInitEvent);
        
        // Even if we timeout, check if it's actually initialized now (could have missed the event)
        if (this._isInitialized && (this.currentRestaurantId === restaurantId || this.dbIdentifier === expectedDbIdentifier)) {
          console.log(`[DBv4.waitForInitialization] Despite timeout, database is actually initialized for ${restaurantId}, resolving`);
          resolve();
        } else {
          console.error(`[DBv4.waitForInitialization] TIMEOUT: Database initialization timeout after ${timeoutMs}ms for restaurant ${restaurantId}`);
          reject(new Error(`Database initialization timeout after ${timeoutMs}ms for restaurant ${restaurantId}`));
        }
      }, timeoutMs);

      // Listen for initialization event
      const handleInitEvent = (event: Event) => {
        const customEvent = event as CustomEvent;
        const { success, restaurantId: initRestaurantId } = customEvent.detail;

        console.log(`[DBv4.waitForInitialization] Received initialization event: success=${success}, restaurantId=${initRestaurantId}, waiting for ${restaurantId}`);

        // Only resolve for the requested restaurant ID
        if (success && initRestaurantId === restaurantId) {
          clearTimeout(timeoutId);
          document.removeEventListener('v4-pouchdb-initialized', handleInitEvent);
          console.log(`[DBv4.waitForInitialization] Successfully initialized for ${restaurantId}, resolving promise`);
          resolve();
        } else if (!success && initRestaurantId === restaurantId) {
          clearTimeout(timeoutId);
          document.removeEventListener('v4-pouchdb-initialized', handleInitEvent);
          console.error(`[DBv4.waitForInitialization] Initialization failed for ${restaurantId}, rejecting promise`);
          reject(new Error(`Database initialization failed for restaurant ${restaurantId}`));
        }
      };

      document.addEventListener('v4-pouchdb-initialized', handleInitEvent);

      // Extra safety check: Check again after setting up listeners in case it was initialized in between
      if (this._isInitialized && (this.currentRestaurantId === restaurantId || this.dbIdentifier === expectedDbIdentifier)) {
        clearTimeout(timeoutId);
        document.removeEventListener('v4-pouchdb-initialized', handleInitEvent);
        console.log(`[DBv4.waitForInitialization] Database became initialized after listener setup, resolving immediately`);
        resolve();
        return;
      }

      // Log that we're waiting
      console.log(`[DBv4.waitForInitialization] Waiting for database initialization for restaurant ${restaurantId}`);
    });
  }

  // Method to explicitly destroy a database (useful for testing or full resets)
  async destroyDatabase(restaurantId: string): Promise<void> {
    const cleanId = cleanRestaurantId(restaurantId);
    const dbName = `resto-${cleanId}`;
    console.log(`[DBv4.destroyDatabase] Attempting to destroy database: ${dbName}`);

    if (this._isElectron) {
      if (!(window as any).electronAPI?.database?.destroy) {
        console.error('[DBv4.destroyDatabase] electronAPI.database.destroy is not available.');
        throw new Error('electronAPI.database.destroy is not available.');
      }
      try {
        const response = await (window as any).electronAPI.database.destroy(dbName);
        if (!response.ok) {
          console.error(`[DBv4.destroyDatabase] Error from main process while destroying ${dbName}:`, response.error);
          let message = `Failed to destroy database ${dbName} in main process`;
          let status = 500;
          let name = 'destroy_error';
          if (typeof response.error === 'object' && response.error !== null) {
            if ('message' in response.error && typeof (response.error as any).message === 'string') message = (response.error as any).message;
            if ('status' in response.error) status = (response.error as any).status;
            if ('name' in response.error && typeof (response.error as any).name === 'string') name = (response.error as any).name;
          }
          const mainError: any = new Error(message);
          mainError.status = status;
          mainError.name = name;
          throw mainError;
        }
        console.log(`[DBv4.destroyDatabase] Electron: Database ${dbName} destroyed via IPC. Response:`, response.message);
      } catch (error) {
        console.error(`[DBv4.destroyDatabase] Error from main process while destroying ${dbName}:`, error);
        let message = `Failed to destroy database ${dbName} in main process`;
        let status = 500;
        let name = 'destroy_error';
        if (typeof error === 'object' && error !== null) {
          if ('message' in error && typeof (error as any).message === 'string') message = (error as any).message;
          if ('status' in error) status = (error as any).status;
          if ('name' in error && typeof (error as any).name === 'string') name = (error as any).name;
        }
        const mainError: any = new Error(message);
        mainError.status = status;
        mainError.name = name;
        throw mainError;
      }
    } else {
      // Browser mode: close the local PouchDB instance
      if (this.db && typeof this.db.close === 'function') {
        try {
          console.log(`[DBv4.destroyDatabase] BROWSER_MODE: Closing local PouchDB instance for '${dbName}'.`);
          await this.db.close();
          console.log(`[DBv4.destroyDatabase] BROWSER_MODE: Local PouchDB instance '${dbName}' closed.`);
        } catch (error) {
          console.error(`[DBv4.destroyDatabase] BROWSER_MODE: Error closing database '${dbName}':`, error);
        }
      }
    }

    this.db = null;
    this.currentRestaurantId = null;
    this.dbIdentifier = '';
    this._isInitialized = false;

    // Notify listeners that the DB for the previous restaurant is no longer active/initialized.
    // Important: Pass the previousRestaurantId or null if it was already null.
    if (restaurantId) { // Only notify if there was a previous restaurant context being cleaned up
        this.restaurantChangeListeners.forEach(listener => listener(null));
    }
    console.log(`[DBv4.destroyDatabase] State reset. isInitialized: ${this._isInitialized}, dbIdentifier: '${this.dbIdentifier}', currentRestaurantId: '${this.currentRestaurantId}'.`);
  }

  /**
   * Start a local sync between this database and CouchDB server
   * Only works in Electron environment
   */
  async startLocalSync(): Promise<{ success: boolean; message: string }> {
    console.log(`[DBv4.startLocalSync] Attempting to start local sync for ${this.dbIdentifier}`);
    
    // Only works in Electron
    if (!this._isElectron) {
      console.warn('[DBv4.startLocalSync] Local sync only works in Electron environment');
      return { 
        success: false, 
        message: 'Local sync only works in Electron environment' 
      };
    }
    
    // Ensure database is initialized
    if (!this._isInitialized || !this.dbIdentifier) {
      console.warn('[DBv4.startLocalSync] Database not initialized');
      return { 
        success: false, 
        message: 'Database not initialized' 
      };
    }
    
    try {
      // Call the IPC method to start the sync
      const result = await (window as any).electronAPI.database.startLocalSync(this.dbIdentifier);
      
      if (result && result.ok) {
        return { 
          success: true, 
          message: result.message || `Sync started for ${this.dbIdentifier}` 
        };
      } else {
        const errorMsg = result?.error?.message || 'Unknown error';
        return { 
          success: false, 
          message: `Failed to start sync: ${errorMsg}` 
        };
      }
    } catch (error: any) {
      console.error('[DBv4.startLocalSync] Error starting local sync:', error);
      return { 
        success: false, 
        message: `Error: ${error.message || String(error)}` 
      };
    }
  }
  
  /**
   * Stop a local sync between this database and CouchDB server
   * Only works in Electron environment
   */
  async stopLocalSync(): Promise<{ success: boolean; message: string }> {
    console.log(`[DBv4.stopLocalSync] Attempting to stop local sync for ${this.dbIdentifier}`);
    
    // Only works in Electron
    if (!this._isElectron) {
      console.warn('[DBv4.stopLocalSync] Local sync only works in Electron environment');
      return { 
        success: false, 
        message: 'Local sync only works in Electron environment' 
      };
    }
    
    // Ensure database is initialized
    if (!this._isInitialized || !this.dbIdentifier) {
      console.warn('[DBv4.stopLocalSync] Database not initialized');
      return { 
        success: false, 
        message: 'Database not initialized' 
      };
    }
    
    try {
      // Call the IPC method to stop the sync
      const result = await (window as any).electronAPI.database.stopLocalSync(this.dbIdentifier);
      
      if (result && result.ok) {
        return { 
          success: true, 
          message: result.message || `Sync stopped for ${this.dbIdentifier}` 
        };
      } else {
        const errorMsg = result?.error?.message || 'Unknown error';
        return { 
          success: false, 
          message: `Failed to stop sync: ${errorMsg}` 
        };
      }
    } catch (error: any) {
      console.error('[DBv4.stopLocalSync] Error stopping local sync:', error);
      return { 
        success: false, 
        message: `Error: ${error.message || String(error)}` 
      };
    }
  }
  
  /**
   * Get the status of all local syncs
   * Only works in Electron environment
   */
  async getLocalSyncStatus(): Promise<any> {
    // Only works in Electron
    if (!this._isElectron) {
      console.warn('[DBv4.getLocalSyncStatus] Local sync only works in Electron environment');
      return { 
        success: false, 
        message: 'Local sync only works in Electron environment' 
      };
    }
    
    try {
      // Call the IPC method to get sync status
      const result = await (window as any).electronAPI.database.getLocalSyncStatus();
      
      if (result && result.ok) {
        return { 
          success: true, 
          data: result.data || [] 
        };
      } else {
        const errorMsg = result?.error?.message || 'Unknown error';
        return { 
          success: false, 
          message: `Failed to get sync status: ${errorMsg}` 
        };
      }
    } catch (error: any) {
      console.error('[DBv4.getLocalSyncStatus] Error getting local sync status:', error);
      return { 
        success: false, 
        message: `Error: ${error.message || String(error)}` 
      };
    }
  }

  /**
   * Initialize default documents that are required for the application to function
   * Uses safe initialization to prevent concurrent access conflicts
   */
  private async initializeDefaultDocuments(): Promise<void> {
    console.log('📄 [initializeDefaultDocuments] Documents will be initialized on-demand to prevent sync conflicts');
    
    // 🚀 ENHANCEMENT: Don't create default documents on startup
    // Let operations create them when needed using safeEnsureDocument
    // This prevents conflicts with documents that exist from sync
    
    console.log('✅ [initializeDefaultDocuments] Initialization strategy set to on-demand');
  }

  /**
   * Safe document initialization with locking to prevent concurrent access
   */
  async safeInitializeDocument<T extends { _id: string }>(
    docId: string,
    defaultFactory: () => T,
    operationName: string = 'safeInitializeDocument'
  ): Promise<T> {
    // Check if there's already a lock for this document
    const existingLock = this.documentInitLocks.get(docId);
    if (existingLock) {
      console.log(`[${operationName}] Document ${docId} already being initialized, waiting...`);
      return existingLock;
    }

    // Create a new lock for this document
    const initPromise = this.performDocumentInitialization(docId, defaultFactory, operationName);
    this.documentInitLocks.set(docId, initPromise);

    try {
      const result = await initPromise;
      return result;
    } finally {
      // Always clean up the lock when done
      this.documentInitLocks.delete(docId);
    }
  }

  /**
   * Internal method to perform the actual document initialization
   */
  private async performDocumentInitialization<T extends { _id: string }>(
    docId: string,
    defaultFactory: () => T,
    operationName: string
  ): Promise<T> {
    let retries = 3;
    let lastError: any = null;

    while (retries > 0) {
      try {
        // First, try to get the document
        const doc = await this.getDoc<T>(docId);
        console.log(`[${operationName}] Document ${docId} already exists`);
        return doc;
      } catch (error: any) {
        if (error.status === 404 || error.name === 'not_found') {
          // Document doesn't exist, try to create it
          try {
            const defaultDoc = defaultFactory();
            console.log(`[${operationName}] Creating default document ${docId}`);
            
            // Ensure no _rev on new documents
            const cleanDoc = { ...defaultDoc };
            delete (cleanDoc as any)._rev;
            
            await this.putDoc(cleanDoc);
            
            // Return the created document
            return cleanDoc;
          } catch (createError: any) {
            lastError = createError;
            
            // If it's a conflict, another process might have created it, retry
            if (createError.status === 409 && retries > 1) {
              console.warn(`[${operationName}] Conflict creating ${docId}, retrying... (${retries - 1} attempts left)`);
              retries--;
              await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 100));
              continue;
            }
            
            throw createError;
          }
        } else {
          // Some other error occurred
          throw error;
        }
      }
    }
    
    throw lastError;
  }

  /**
   * Utility function for robust retry logic with exponential backoff
   */
  static async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    operationName: string = 'operation'
  ): Promise<T> {
    let lastError: any = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error: any) {
        lastError = error;
        
        // If it's a 409 conflict, retry with exponential backoff
        if (error.status === 409 && attempt < maxRetries) {
          const delay = Math.min(1000, 100 * Math.pow(2, attempt - 1)) + Math.random() * 50; // Exponential backoff with jitter
          console.warn(`[${operationName}] Document conflict, retrying in ${delay.toFixed(0)}ms... (${maxRetries - attempt} attempts left)`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
        
        // For other errors or no retries left, throw
        throw error;
      }
    }
    
    throw lastError;
  }
}

// knowledge: Export the singleton instance for app-wide DB access
export const databaseV4 = new DatabaseV4();