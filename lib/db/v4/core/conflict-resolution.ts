/**
 * Universal Conflict Resolution Utilities for V4 Database Operations
 * 
 * This module provides robust conflict resolution patterns that can be used
 * across all operation files to handle PouchDB document conflicts.
 */

import { databaseV4 } from './db-instance';

/**
 * 🚀 PRODUCTION MONITORING UTILITIES
 * Enhanced logging and performance tracking for production environments
 */
interface OperationMetrics {
  operationName: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  success: boolean;
  attempts: number;
  errorType?: string;
  documentId?: string;
}

class ProductionMonitor {
  private metrics: OperationMetrics[] = [];
  private readonly maxMetrics = 1000; // Keep last 1000 operations
  
  startOperation(operationName: string, documentId?: string): string {
    const operationId = `${operationName}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const metric: OperationMetrics = {
      operationName,
      startTime: Date.now(),
      success: false,
      attempts: 0,
      documentId
    };
    
    // Keep metrics array bounded
    if (this.metrics.length >= this.maxMetrics) {
      this.metrics.shift();
    }
    
    this.metrics.push(metric);
    return operationId;
  }
  
  recordAttempt(operationName: string): void {
    const metric = this.metrics.find(m => 
      m.operationName === operationName && !m.endTime
    );
    if (metric) {
      metric.attempts++;
    }
  }
  
  endOperation(operationName: string, success: boolean, error?: any): void {
    const metric = this.metrics.find(m => 
      m.operationName === operationName && !m.endTime
    );
    
    if (metric) {
      metric.endTime = Date.now();
      metric.duration = metric.endTime - metric.startTime;
      metric.success = success;
      
      if (error) {
        metric.errorType = error.status ? `${error.status}` : error.name || 'unknown';
      }
      
      // Log performance warnings
      if (metric.duration > 5000) {
        console.warn(`🐌 [PERFORMANCE] Slow operation detected:`, {
          operation: metric.operationName,
          duration: metric.duration,
          attempts: metric.attempts,
          documentId: metric.documentId
        });
      }
      
      // Log high retry counts
      if (metric.attempts > 3) {
        console.warn(`🔄 [RELIABILITY] High retry count:`, {
          operation: metric.operationName,
          attempts: metric.attempts,
          success: metric.success,
          documentId: metric.documentId
        });
      }
    }
  }
  
  getHealthStats(): any {
    const recentMetrics = this.metrics.filter(m => 
      m.endTime && (Date.now() - m.endTime < 300000) // Last 5 minutes
    );
    
    if (recentMetrics.length === 0) return null;
    
    const successRate = recentMetrics.filter(m => m.success).length / recentMetrics.length;
    const avgDuration = recentMetrics.reduce((sum, m) => sum + (m.duration || 0), 0) / recentMetrics.length;
    const avgAttempts = recentMetrics.reduce((sum, m) => sum + m.attempts, 0) / recentMetrics.length;
    
    return {
      totalOperations: recentMetrics.length,
      successRate: Math.round(successRate * 100),
      avgDurationMs: Math.round(avgDuration),
      avgAttempts: Math.round(avgAttempts * 10) / 10,
      timestamp: new Date().toISOString()
    };
  }
}

const productionMonitor = new ProductionMonitor();

// Export monitoring functions
export function getSystemHealthStats() {
  return productionMonitor.getHealthStats();
}

/**
 * 🚀 DOCUMENT VALIDATION UTILITIES
 * Enhanced validation for critical document types
 */
export function validateDocument(doc: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Basic document structure validation
  if (!doc._id) {
    errors.push('Document must have an _id');
  }
  
  if (typeof doc._id !== 'string' || doc._id.trim() === '') {
    errors.push('Document _id must be a non-empty string');
  }
  
  // Type-specific validation
  switch (doc.type) {
    case 'menu_document':
      if (!Array.isArray(doc.categories)) {
        errors.push('Menu document must have categories array');
      }
      break;
      
    case 'inventory_document':
      if (!Array.isArray(doc.items)) {
        errors.push('Inventory document must have items array');
      }
      break;
      
    case 'cash_transaction':
      if (!doc.amount || typeof doc.amount !== 'number') {
        errors.push('Cash transaction must have a numeric amount');
      }
      if (!doc.type || !['manual_in', 'manual_out', 'expense', 'sale'].includes(doc.type)) {
        errors.push('Cash transaction must have a valid type');
      }
      break;
      
    case 'order':
      if (!Array.isArray(doc.items) || doc.items.length === 0) {
        errors.push('Order must have at least one item');
      }
      if (!doc.total || typeof doc.total !== 'number' || doc.total <= 0) {
        errors.push('Order must have a positive total');
      }
      break;
  }
  
  // Timestamp validation
  if (doc.createdAt && !isValidDate(doc.createdAt)) {
    errors.push('Invalid createdAt timestamp');
  }
  
  if (doc.updatedAt && !isValidDate(doc.updatedAt)) {
    errors.push('Invalid updatedAt timestamp');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

function isValidDate(dateString: string): boolean {
  const date = new Date(dateString);
  return !isNaN(date.getTime());
}

/**
 * 🚀 ENHANCED: Retry operation with intelligent error recovery and monitoring
 */
export async function retryWithConflictResolution<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  operationName: string = 'operation'
): Promise<T> {
  // 🚀 ENHANCED: Start monitoring
  const operationId = productionMonitor.startOperation(operationName);
  let lastError: any = null;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    // Record each attempt
    productionMonitor.recordAttempt(operationName);
    
    try {
      const result = await operation();
      // 🚀 ENHANCED: Record successful operation
      productionMonitor.endOperation(operationName, true);
      return result;
    } catch (error: any) {
      lastError = error;
      
      // 🚀 ENHANCED: Different retry strategies for different error types
      const shouldRetry = shouldRetryError(error, attempt, maxRetries);
      
      if (shouldRetry) {
        const delay = getRetryDelay(error, attempt);
        console.warn(`[${operationName}] ${getErrorDescription(error)}, retrying in ${delay.toFixed(0)}ms... (${maxRetries - attempt} attempts left)`);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      
      // No more retries or non-retryable error
      console.error(`[${operationName}] Operation failed after ${attempt} attempts:`, {
        error: error.message,
        status: error.status,
        name: error.name
      });
      // 🚀 ENHANCED: Record failed operation
      productionMonitor.endOperation(operationName, false, error);
      throw error;
    }
  }
  
  // 🚀 ENHANCED: Record failed operation if we exit the loop
  productionMonitor.endOperation(operationName, false, lastError);
  throw lastError;
}

/**
 * 🚀 ENHANCED: Smart error classification for retry logic
 */
function shouldRetryError(error: any, attempt: number, maxRetries: number): boolean {
  if (attempt >= maxRetries) return false;
  
  // Always retry conflicts
  if (error.status === 409) return true;
  
  // Retry network errors
  if (error.name === 'TypeError' && error.message?.includes('fetch')) return true;
  
  // Retry timeout errors
  if (error.name === 'timeout' || error.message?.includes('timeout')) return true;
  
  // Retry temporary server errors
  if (error.status >= 500 && error.status < 600) return true;
  
  // Don't retry client errors (400-499 except 409)
  if (error.status >= 400 && error.status < 500) return false;
  
  // Retry database connection errors
  if (error.message?.includes('database') || error.message?.includes('connection')) return true;
  
  return false;
}

/**
 * 🚀 ENHANCED: Intelligent retry delay calculation
 */
function getRetryDelay(error: any, attempt: number): number {
  // Faster retries for conflicts (they're usually quick to resolve)
  if (error.status === 409) {
    return Math.min(1000, 100 * Math.pow(2, attempt - 1)) + Math.random() * 50;
  }
  
  // Longer delays for network issues
  if (error.name === 'TypeError' || error.name === 'timeout') {
    return Math.min(5000, 500 * Math.pow(2, attempt - 1)) + Math.random() * 200;
  }
  
  // Default exponential backoff
  return Math.min(2000, 200 * Math.pow(2, attempt - 1)) + Math.random() * 100;
}

/**
 * 🚀 ENHANCED: Human-readable error descriptions
 */
function getErrorDescription(error: any): string {
  if (error.status === 409) return 'Document conflict detected';
  if (error.name === 'TypeError') return 'Network connection error';
  if (error.name === 'timeout') return 'Operation timed out';
  if (error.status >= 500) return 'Server error';
  if (error.status === 404) return 'Document not found';
  if (error.status === 401) return 'Authentication failed';
  if (error.status === 403) return 'Access denied';
  
  return `Unknown error (${error.status || error.name || 'unknown'})`;
}

/**
 * Get document with automatic retry on conflicts
 */
export async function safeGetDocument<T>(docId: string): Promise<T> {
  return retryWithConflictResolution(
    () => databaseV4.getDoc<T>(docId),
    3,
    `safeGetDocument(${docId})`
  );
}

/**
 * Update document with automatic conflict resolution using get-modify-put pattern
 */
export async function safeUpdateDocument<T extends { _id: string; _rev?: string }>(
  docId: string,
  updateFunction: (doc: T) => T,
  operationName: string = 'safeUpdateDocument'
): Promise<T> {
  return retryWithConflictResolution(async () => {
    // Get fresh document with latest revision
    const currentDoc = await databaseV4.getDoc<T>(docId);
    
    // Apply updates
    const updatedDoc = updateFunction(currentDoc);
    
    // Ensure we preserve the revision
    updatedDoc._rev = currentDoc._rev;
    updatedDoc._id = currentDoc._id;
    
    // Save the updated document
    const result = await databaseV4.putDoc(updatedDoc);
    
    // Return the updated document with new revision
    return { ...updatedDoc, _rev: result.rev };
  }, 3, operationName);
}

/**
 * Create or update document with conflict resolution and validation
 */
export async function safeUpsertDocument<T extends { _id: string; _rev?: string }>(
  doc: T,
  operationName: string = 'safeUpsertDocument'
): Promise<T> {
  // 🚀 ENHANCED: Validate document before saving
  const validation = validateDocument(doc);
  if (!validation.isValid) {
    const errorMessage = `Document validation failed: ${validation.errors.join(', ')}`;
    console.error(`[${operationName}] ${errorMessage}`, doc);
    throw new Error(errorMessage);
  }
  
  return retryWithConflictResolution(async () => {
    try {
      // Try to get existing document to get latest revision
      const existing = await databaseV4.getDoc<T>(doc._id);
      doc._rev = existing._rev;
    } catch (error: any) {
      // If document doesn't exist (404), that's fine - we'll create it
      if (error.status !== 404 && error.name !== 'not_found') {
        throw error;
      }
      // For new documents, ensure no _rev
      delete doc._rev;
    }
    
    // Save the document
    const result = await databaseV4.putDoc(doc);
    
    // Return with new revision
    return { ...doc, _rev: result.rev };
  }, 3, operationName);
}

/**
 * Array-based document update with conflict resolution
 * Useful for documents that contain arrays (like inventory items, menu categories, etc.)
 */
export async function safeUpdateArrayDocument<T extends { _id: string; _rev?: string }, K>(
  docId: string,
  arrayFieldName: keyof T,
  arrayUpdater: (items: K[]) => K[],
  operationName: string = 'safeUpdateArrayDocument'
): Promise<T> {
  return safeUpdateDocument<T>(
    docId,
    (doc) => {
      const currentArray = (doc[arrayFieldName] as K[]) || [];
      const updatedArray = arrayUpdater(currentArray);
      return {
        ...doc,
        [arrayFieldName]: updatedArray,
        updatedAt: new Date().toISOString()
      } as T;
    },
    operationName
  );
}

/**
 * Add item to array in document with conflict resolution
 */
export async function safeAddToArrayDocument<T extends { _id: string; _rev?: string }, K>(
  docId: string,
  arrayFieldName: keyof T,
  newItem: K,
  operationName: string = 'safeAddToArrayDocument'
): Promise<T> {
  return safeUpdateArrayDocument<T, K>(
    docId,
    arrayFieldName,
    (items) => [...items, newItem],
    operationName
  );
}

/**
 * Update item in array within document with conflict resolution
 */
export async function safeUpdateArrayItem<T extends { _id: string; _rev?: string }, K extends { id: string }>(
  docId: string,
  arrayFieldName: keyof T,
  itemId: string,
  itemUpdates: Partial<K>,
  operationName: string = 'safeUpdateArrayItem'
): Promise<T> {
  return safeUpdateArrayDocument<T, K>(
    docId,
    arrayFieldName,
    (items) => items.map(item => 
      item.id === itemId 
        ? { ...item, ...itemUpdates, updatedAt: new Date().toISOString() } as K
        : item
    ),
    operationName
  );
}

/**
 * Remove item from array in document with conflict resolution
 */
export async function safeRemoveFromArrayDocument<T extends { _id: string; _rev?: string }, K extends { id: string }>(
  docId: string,
  arrayFieldName: keyof T,
  itemId: string,
  operationName: string = 'safeRemoveFromArrayDocument'
): Promise<T> {
  return safeUpdateArrayDocument<T, K>(
    docId,
    arrayFieldName,
    (items) => items.filter(item => item.id !== itemId),
    operationName
  );
}

/**
 * Create default document if it doesn't exist, with conflict resolution
 * Now uses safe initialization to prevent concurrent access issues
 */
export async function safeEnsureDocument<T extends { _id: string; _rev?: string }>(
  docId: string,
  defaultDocumentFactory: () => T,
  operationName: string = 'safeEnsureDocument'
): Promise<T> {
  // Use the new safe initialization method to prevent concurrent conflicts
  return await databaseV4.safeInitializeDocument<T>(
    docId,
    defaultDocumentFactory,
    operationName
  );
}

/**
 * 🚀 ATOMIC BULK OPERATIONS - For truly atomic multi-document operations
 * Use this when you need to create/update multiple documents atomically
 */
export async function atomicBulkOperation<T extends { _id: string; _rev?: string }>(
  documents: T[],
  operationName: string = 'atomicBulkOperation'
): Promise<T[]> {
  return retryWithConflictResolution(async () => {
    console.log(`[${operationName}] Performing atomic bulk operation on ${documents.length} documents`);

    // Use PouchDB's bulk operation for atomicity
    const results = await databaseV4.bulkDocs(documents);

    // Check for any errors
    const errors = results.filter(result => 'error' in result);
    if (errors.length > 0) {
      console.error(`[${operationName}] Bulk operation had errors:`, errors);
      throw new Error(`Bulk operation failed: ${errors.map(e => (e as any).error).join(', ')}`);
    }

    // Return documents with updated revisions
    return documents.map((doc, index) => ({
      ...doc,
      _rev: results[index].rev
    }));
  }, 3, operationName);
}