"use client";

import { databaseV4 } from '../core/db-instance';

/**
 * Training Mode Operations
 * 
 * Functions to manage training data in the database
 */

/**
 * Check if training mode is active
 */
export function isTrainingModeActive(): boolean {
  if (typeof window === 'undefined') return false;
  return localStorage.getItem('training_mode') === 'true';
}

/**
 * Add training flag to document data if training mode is active
 */
export function addTrainingFlag<T extends Record<string, any>>(doc: T): T {
  if (isTrainingModeActive()) {
    return { ...doc, is_training: true };
  }
  return doc;
}

/**
 * Create query selector that excludes training data by default
 */
export function createNonTrainingSelector(baseSelector: any = {}): any {
  return {
    ...baseSelector,
    is_training: { $ne: true }
  };
}

/**
 * Create query selector that includes only training data
 */
export function createTrainingOnlySelector(baseSelector: any = {}): any {
  return {
    ...baseSelector,
    is_training: true
  };
}

/**
 * Count all training data documents across all types
 */
export async function countTrainingData(): Promise<number> {
  try {
    const result = await databaseV4.findDocs({
      selector: { is_training: true },
      fields: ['_id']
    });
    return result.docs.length;
  } catch (error) {
    console.error('Failed to count training data:', error);
    return 0;
  }
}

/**
 * Get all training data documents (for debugging/inspection)
 */
export async function getAllTrainingData(): Promise<any[]> {
  try {
    const result = await databaseV4.findDocs({
      selector: { is_training: true }
    });
    return result.docs;
  } catch (error) {
    console.error('Failed to get training data:', error);
    return [];
  }
}

/**
 * Delete all training data documents
 */
export async function deleteAllTrainingData(): Promise<void> {
  try {
    console.log('🧹 Starting training data cleanup...');
    
    // Get all training documents
    const result = await databaseV4.findDocs({
      selector: { is_training: true },
      fields: ['_id', '_rev']
    });

    if (result.docs.length === 0) {
      console.log('✅ No training data found to delete');
      return;
    }

    console.log(`🗑️ Found ${result.docs.length} training documents to delete`);

    // Prepare bulk delete operation
    const docsToDelete = result.docs.map(doc => ({
      ...doc,
      _deleted: true
    }));

    // Execute bulk delete
    const deleteResult = await databaseV4.bulkDocs(docsToDelete);
    
    // Check for errors in bulk operation
    const errors = deleteResult.filter(result => 'error' in result);
    if (errors.length > 0) {
      console.warn('⚠️ Some training documents failed to delete:', errors);
    }

    const successCount = deleteResult.filter(result => 'ok' in result && result.ok).length;
    console.log(`✅ Successfully deleted ${successCount}/${result.docs.length} training documents`);

  } catch (error) {
    console.error('❌ Failed to delete training data:', error);
    throw error;
  }
}

/**
 * Get training data summary by document type
 */
export async function getTrainingDataSummary(): Promise<Record<string, number>> {
  try {
    const result = await databaseV4.findDocs({
      selector: { is_training: true },
      fields: ['type']
    });

    const summary: Record<string, number> = {};
    
    result.docs.forEach(doc => {
      const type = doc.type || 'unknown';
      summary[type] = (summary[type] || 0) + 1;
    });

    return summary;
  } catch (error) {
    console.error('Failed to get training data summary:', error);
    return {};
  }
}