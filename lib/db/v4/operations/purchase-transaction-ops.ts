/**
 * 🚀 Clean Purchase Transaction Operations
 * 
 * Top-notch backend operations for purchase transactions.
 * Single source of truth for all purchase-related database operations.
 */

import { v4 as uuidv4 } from 'uuid';
import { databaseV4 } from '../core/db-instance';
import {
  PurchaseTransaction,
  PurchaseItem,
  PurchaseTransactionSummary,
  generateTransactionId,
  calculateTransactionTotals,
  createTransactionSummary,
  isPurchaseTransaction,
  DEFAULT_PURCHASE_TRANSACTION
} from '../schemas/purchase-transaction-schema';
import { StockItem } from '../schemas/inventory-schema';
import { getInventory, updateStockItem } from './inventory-ops';
import {
  retryWithConflictResolution,
  safeGetDocument,
  safeUpdateDocument
} from '../core/conflict-resolution';
import { updateSupplier } from './supplier-ops';

/**
 * Create database indexes for efficient purchase transaction queries
 */
export async function createPurchaseTransactionIndexes(): Promise<void> {
  console.log('[createPurchaseTransactionIndexes] Creating indexes for purchase transactions');
  
  const indexes = [
    // Core transaction indexes
    { fields: ['type', 'date'], name: 'purchase-type-date-idx' },
    { fields: ['type', 'supplierId'], name: 'purchase-type-supplier-idx' },
    { fields: ['type', 'performedBy'], name: 'purchase-type-user-idx' },
    { fields: ['date'], name: 'purchase-date-idx' },
    { fields: ['supplierId', 'date'], name: 'purchase-supplier-date-idx' },
    
    // Item-specific indexes for stock tracking
    { fields: ['type', 'items.stockItemId'], name: 'purchase-type-stockitem-idx' },
  ];

  for (const index of indexes) {
    try {
      await databaseV4.createIndex({
        index: {
          fields: index.fields,
          name: index.name,
          ddoc: index.name
        }
      });
      console.log(`[createPurchaseTransactionIndexes] ✅ Created index: ${index.name}`);
    } catch (error: any) {
      if (error.status !== 409) { // 409 = index already exists
        console.warn(`[createPurchaseTransactionIndexes] ⚠️ Failed to create index ${index.name}:`, error.message);
      }
    }
  }
}

/**
 * Create a new purchase transaction
 */
export async function createPurchaseTransaction(
  transactionData: Omit<PurchaseTransaction, '_id' | '_rev' | 'type' | 'schemaVersion' | 'createdAt' | 'updatedAt' | 'subtotalCost' | 'totalCost' | 'hasReceiptImage'>
): Promise<PurchaseTransaction> {
  return retryWithConflictResolution(async () => {
    console.log('[createPurchaseTransaction] Creating new purchase transaction');
    console.log('[createPurchaseTransaction] Input data:', JSON.stringify(transactionData, null, 2));
    
    // Validate items
    if (!transactionData.items || transactionData.items.length === 0) {
      throw new Error('Purchase transaction must contain at least one item');
    }

    // Calculate totals from items
    const { subtotalCost, totalCost } = calculateTransactionTotals(transactionData.items);
    
    // Create the transaction document
    const transaction: PurchaseTransaction = {
      _id: generateTransactionId(),
      type: 'purchase_transaction',
      schemaVersion: 'v1.0',
      ...transactionData,
      subtotalCost,
      totalCost,
      hasReceiptImage: false, // Will be updated when receipt is attached
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Save to database
    console.log('[createPurchaseTransaction] Saving to database...');
    console.log('[createPurchaseTransaction] Database instance:', !!databaseV4);
    const result = await databaseV4.putDoc(transaction);
    console.log('[createPurchaseTransaction] Database save result:', result);
    transaction._rev = result.rev;

    // Update stock quantities for each item
    console.log('[createPurchaseTransaction] Updating stock quantities...');
    try {
      await updateStockQuantitiesFromPurchase(transaction.items);
      console.log('[createPurchaseTransaction] Stock quantities updated successfully');
    } catch (stockError) {
      console.error('[createPurchaseTransaction] Stock update failed:', stockError);
      throw new Error(`Failed to update stock quantities: ${stockError instanceof Error ? stockError.message : 'Unknown error'}`);
    }

    // Update supplier balance if supplier specified
    if (transaction.supplierId) {
      console.log('[createPurchaseTransaction] Updating supplier balance...');
      try {
        await updateSupplierBalanceFromPurchase(transaction.supplierId, transaction.totalCost, transaction.amountPaid);
        console.log('[createPurchaseTransaction] Supplier balance updated successfully');
      } catch (supplierError) {
        console.error('[createPurchaseTransaction] Supplier balance update failed:', supplierError);
        // Don't throw here as the transaction was already saved
      }
    }

    console.log(`[createPurchaseTransaction] ✅ Created transaction: ${transaction._id}`);
    return transaction;
  }, 3, 'createPurchaseTransaction');
}

/**
 * Get a purchase transaction by ID
 */
export async function getPurchaseTransaction(transactionId: string): Promise<PurchaseTransaction> {
  try {
    const doc = await safeGetDocument<PurchaseTransaction>(transactionId);
    
    if (!isPurchaseTransaction(doc)) {
      throw new Error(`Document ${transactionId} is not a valid purchase transaction`);
    }
    
    return doc;
  } catch (error) {
    console.error(`[getPurchaseTransaction] Error retrieving transaction ${transactionId}:`, error);
    throw error;
  }
}

/**
 * Update a purchase transaction
 */
export async function updatePurchaseTransaction(
  transactionId: string,
  updates: Partial<Omit<PurchaseTransaction, '_id' | 'type' | 'schemaVersion' | 'createdAt'>>
): Promise<PurchaseTransaction> {
  return retryWithConflictResolution(async () => {
    console.log(`[updatePurchaseTransaction] Updating transaction: ${transactionId}`);
    
    const existingTransaction = await getPurchaseTransaction(transactionId);
    
    // If items are being updated, recalculate totals
    let finalUpdates = { ...updates };
    if (updates.items) {
      const { subtotalCost, totalCost } = calculateTransactionTotals(updates.items);
      finalUpdates.subtotalCost = subtotalCost;
      finalUpdates.totalCost = totalCost;
      
      // Revert previous stock changes and apply new ones
      await revertStockQuantitiesFromPurchase(existingTransaction.items);
      await updateStockQuantitiesFromPurchase(updates.items);
      
      // Update supplier balance if needed
      if (existingTransaction.supplierId) {
        try {
          // Revert previous balance change
          await revertSupplierBalanceFromPurchase(
            existingTransaction.supplierId, 
            existingTransaction.totalCost, 
            existingTransaction.amountPaid
          );
          
          // Apply new balance change if still has supplier
          if (finalUpdates.supplierId) {
            await updateSupplierBalanceFromPurchase(
              finalUpdates.supplierId, 
              finalUpdates.totalCost || existingTransaction.totalCost, 
              finalUpdates.amountPaid || existingTransaction.amountPaid
            );
          }
        } catch (supplierError) {
          console.error('[updatePurchaseTransaction] Supplier balance update failed:', supplierError);
        }
      } else if (finalUpdates.supplierId) {
        // New supplier assigned
        try {
          await updateSupplierBalanceFromPurchase(
            finalUpdates.supplierId, 
            finalUpdates.totalCost || existingTransaction.totalCost, 
            finalUpdates.amountPaid || existingTransaction.amountPaid
          );
        } catch (supplierError) {
          console.error('[updatePurchaseTransaction] Supplier balance update failed:', supplierError);
        }
      }
    }
    
    const updatedTransaction: PurchaseTransaction = {
      ...existingTransaction,
      ...finalUpdates,
      updatedAt: new Date().toISOString()
    };

    await databaseV4.putDoc(updatedTransaction);
    
    console.log(`[updatePurchaseTransaction] ✅ Updated transaction: ${transactionId}`);
    return updatedTransaction;
  }, 3, 'updatePurchaseTransaction');
}

/**
 * Delete a purchase transaction
 */
export async function deletePurchaseTransaction(transactionId: string): Promise<void> {
  return retryWithConflictResolution(async () => {
    console.log(`[deletePurchaseTransaction] Deleting transaction: ${transactionId}`);
    
    const transaction = await getPurchaseTransaction(transactionId);
    
    // Revert stock quantity changes
    await revertStockQuantitiesFromPurchase(transaction.items);
    
    // Revert supplier balance if supplier specified
    if (transaction.supplierId) {
      try {
        await revertSupplierBalanceFromPurchase(transaction.supplierId, transaction.totalCost, transaction.amountPaid);
      } catch (supplierError) {
        console.error('[deletePurchaseTransaction] Supplier balance revert failed:', supplierError);
      }
    }
    
    // Delete the document
    await databaseV4.deleteDoc(transaction._id, transaction._rev!);
    
    console.log(`[deletePurchaseTransaction] ✅ Deleted transaction: ${transactionId}`);
  }, 3, 'deletePurchaseTransaction');
}

/**
 * Get all purchase transactions with optional filtering
 */
export async function getAllPurchaseTransactions(options?: {
  startDate?: string;
  endDate?: string;
  supplierId?: string;
  limit?: number;
  skip?: number;
}): Promise<PurchaseTransaction[]> {
  try {
    console.log('[getAllPurchaseTransactions] Fetching purchase transactions', options);
    
    // Build query selector
    const selector: any = {
      type: 'purchase_transaction'
    };

    // Add date range filter
    if (options?.startDate || options?.endDate) {
      selector.date = {};
      if (options.startDate) selector.date.$gte = options.startDate;
      if (options.endDate) selector.date.$lte = options.endDate;
    }

    // Add supplier filter
    if (options?.supplierId) {
      selector.supplierId = options.supplierId;
    }

    const query: PouchDB.Find.FindRequest<PurchaseTransaction> = {
      selector,
      sort: [{ date: 'desc' }], // Most recent first
      limit: options?.limit || 100,
      skip: options?.skip || 0
    };

    const result = await databaseV4.findDocs<PurchaseTransaction>(query);
    
    console.log(`[getAllPurchaseTransactions] ✅ Retrieved ${result.docs.length} transactions`);
    return result.docs;
  } catch (error) {
    console.error('[getAllPurchaseTransactions] Error:', error);
    throw error;
  }
}

/**
 * Get purchase transactions for a specific stock item
 */
export async function getPurchaseTransactionsForItem(stockItemId: string): Promise<PurchaseTransaction[]> {
  try {
    console.log(`[getPurchaseTransactionsForItem] Fetching transactions for item: ${stockItemId}`);
    
    const result = await databaseV4.findDocs<PurchaseTransaction>({
      selector: {
        type: 'purchase_transaction',
        'items.stockItemId': stockItemId
      },
      sort: [{ date: 'desc' }]
    });
    
    console.log(`[getPurchaseTransactionsForItem] ✅ Retrieved ${result.docs.length} transactions`);
    return result.docs;
  } catch (error) {
    console.error(`[getPurchaseTransactionsForItem] Error for item ${stockItemId}:`, error);
    throw error;
  }
}

/**
 * Get purchase transaction summaries for efficient listing
 */
export async function getPurchaseTransactionSummaries(options?: {
  startDate?: string;
  endDate?: string;
  supplierId?: string;
  limit?: number;
}): Promise<PurchaseTransactionSummary[]> {
  const transactions = await getAllPurchaseTransactions(options);
  return transactions.map(createTransactionSummary);
}

/**
 * Safely attach receipt image to an existing transaction using CouchDB attachments
 */
export async function attachReceiptImage(
  transactionId: string,
  imageFile: File,
  restaurantId: string
): Promise<void> {
  return retryWithConflictResolution(async () => {
    console.log(`[attachReceiptImage] Attaching image to transaction: ${transactionId}`);
    
    try {
      // Get the transaction to ensure it exists and get current rev
      const transaction = await getPurchaseTransaction(transactionId);
      
      // Generate attachment name
      const timestamp = Date.now();
      const fileExtension = imageFile.name.split('.').pop() || 'webp';
      const attachmentName = `receipt_${timestamp}.${fileExtension}`;
      
      // Convert File to ArrayBuffer for CouchDB
      const arrayBuffer = await imageFile.arrayBuffer();
      
      console.log(`[attachReceiptImage] Adding attachment: ${attachmentName}`);
      
      try {
        // Add attachment to CouchDB document
        const result = await databaseV4.putAttachment(
          transactionId,
          attachmentName,
          transaction._rev!,
          arrayBuffer,
          imageFile.type
        );
        
        console.log(`[attachReceiptImage] Attachment added, new rev: ${result.rev}`);
        
        // Update transaction metadata (without _rev since it just changed)
        const updatedDoc = await databaseV4.getDoc<PurchaseTransaction>(transactionId);
        updatedDoc.receiptImage = attachmentName;
        updatedDoc.hasReceiptImage = true;
        updatedDoc.updatedAt = new Date().toISOString();
        
        await databaseV4.putDoc(updatedDoc);
        
        console.log(`[attachReceiptImage] ✅ Image attached to transaction: ${transactionId}`);
      } catch (attachmentError: any) {
        console.error(`[attachReceiptImage] Attachment error:`, attachmentError);
        
        // Only throw user-friendly error for genuine attachment issues, not JSON parsing errors
        if (attachmentError.message?.includes('not yet supported in Electron') ||
            attachmentError.message?.includes('require browser mode')) {
          console.warn('[attachReceiptImage] CouchDB attachments not supported in current environment, skipping image attachment');
          throw new Error('Image attachments require browser mode. The purchase transaction was saved successfully without the receipt image.');
        }
        
        throw attachmentError;
      }
    } catch (error) {
      console.error(`[attachReceiptImage] Failed to attach image:`, error);
      throw new Error(`Failed to attach receipt image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, 3, 'attachReceiptImage');
}

/**
 * Get receipt image as blob from CouchDB attachment
 */
export async function getReceiptImageBlob(transaction: PurchaseTransaction): Promise<Blob | null> {
  if (!transaction.hasReceiptImage || !transaction.receiptImage) {
    return null;
  }
  
  try {
    const attachment = await databaseV4.getAttachment(transaction._id, transaction.receiptImage);
    return attachment;
  } catch (error) {
    console.error('Failed to get receipt image:', error);
    return null;
  }
}

/**
 * Get receipt image URL for display (creates blob URL)
 */
export async function getReceiptImageUrl(transaction: PurchaseTransaction): Promise<string | null> {
  const blob = await getReceiptImageBlob(transaction);
  if (!blob) return null;
  
  // Create blob URL for display
  return URL.createObjectURL(blob);
}

/**
 * Update receipt image status for a transaction (legacy support)
 */
export async function updateTransactionReceiptStatus(
  transactionId: string,
  hasReceiptImage: boolean,
  receiptImage?: string
): Promise<void> {
  await updatePurchaseTransaction(transactionId, {
    hasReceiptImage,
    receiptImage
  });
}

/**
 * Helper: Update stock quantities from purchase items
 */
async function updateStockQuantitiesFromPurchase(items: PurchaseItem[]): Promise<void> {
  console.log('[updateStockQuantitiesFromPurchase] Updating stock quantities');
  
  const inventory = await getInventory();
  
  for (const item of items) {
    const stockItem = inventory.items?.find(si => si.id === item.stockItemId);
    if (!stockItem) {
      console.warn(`[updateStockQuantitiesFromPurchase] Stock item not found: ${item.stockItemId}`);
      continue;
    }
    
    const newQuantity = (stockItem.quantity || 0) + item.baseQuantity;
    const newCostPerUnit = item.costPerBaseUnit; // Update with latest cost
    
    await updateStockItem(item.stockItemId, {
      quantity: newQuantity,
      costPerUnit: newCostPerUnit
    });
    
    console.log(`[updateStockQuantitiesFromPurchase] Updated ${stockItem.name}: +${item.baseQuantity} ${stockItem.unit}`);
  }
}

/**
 * Helper: Revert stock quantities from purchase items (for updates/deletions)
 */
async function revertStockQuantitiesFromPurchase(items: PurchaseItem[]): Promise<void> {
  console.log('[revertStockQuantitiesFromPurchase] Reverting stock quantities');
  
  const inventory = await getInventory();
  
  for (const item of items) {
    const stockItem = inventory.items?.find(si => si.id === item.stockItemId);
    if (!stockItem) {
      console.warn(`[revertStockQuantitiesFromPurchase] Stock item not found: ${item.stockItemId}`);
      continue;
    }
    
    const newQuantity = Math.max(0, (stockItem.quantity || 0) - item.baseQuantity);
    
    await updateStockItem(item.stockItemId, {
      quantity: newQuantity
    });
    
    console.log(`[revertStockQuantitiesFromPurchase] Reverted ${stockItem.name}: -${item.baseQuantity} ${stockItem.unit}`);
  }
}

/**
 * Get total purchase amount for a date range (for analytics)
 */
export async function getTotalPurchaseAmount(startDate: string, endDate: string): Promise<number> {
  const transactions = await getAllPurchaseTransactions({ startDate, endDate });
  return transactions.reduce((total, tx) => total + tx.totalCost, 0);
}

/**
 * Update supplier balance when making a purchase
 */
async function updateSupplierBalanceFromPurchase(
  supplierId: string, 
  totalCost: number, 
  amountPaid: number
): Promise<void> {
  try {
    const { getSuppliers } = await import('./supplier-ops');
    const suppliersDoc = await getSuppliers();
    const supplier = suppliersDoc.suppliers.find(s => s.id === supplierId);
    
    if (!supplier) {
      console.warn(`[updateSupplierBalanceFromPurchase] Supplier ${supplierId} not found`);
      return;
    }
    
    // Calculate the amount owed to supplier (total cost minus what was paid)
    const amountOwed = totalCost - amountPaid;
    
    // Update supplier balance (positive means we owe them)
    const newBalance = (supplier.balance || 0) + amountOwed;
    
    await updateSupplier(supplierId, { balance: newBalance });
    
    console.log(`[updateSupplierBalanceFromPurchase] Updated supplier ${supplier.name} balance: ${supplier.balance || 0} + ${amountOwed} = ${newBalance}`);
  } catch (error) {
    console.error('[updateSupplierBalanceFromPurchase] Error updating supplier balance:', error);
    throw error;
  }
}

/**
 * Revert supplier balance when deleting/updating a purchase
 */
async function revertSupplierBalanceFromPurchase(
  supplierId: string, 
  totalCost: number, 
  amountPaid: number
): Promise<void> {
  try {
    const { getSuppliers } = await import('./supplier-ops');
    const suppliersDoc = await getSuppliers();
    const supplier = suppliersDoc.suppliers.find(s => s.id === supplierId);
    
    if (!supplier) {
      console.warn(`[revertSupplierBalanceFromPurchase] Supplier ${supplierId} not found`);
      return;
    }
    
    // Calculate the amount that was previously owed to supplier
    const amountOwed = totalCost - amountPaid;
    
    // Revert supplier balance (subtract what was previously added)
    const newBalance = (supplier.balance || 0) - amountOwed;
    
    await updateSupplier(supplierId, { balance: newBalance });
    
    console.log(`[revertSupplierBalanceFromPurchase] Reverted supplier ${supplier.name} balance: ${supplier.balance || 0} - ${amountOwed} = ${newBalance}`);
  } catch (error) {
    console.error('[revertSupplierBalanceFromPurchase] Error reverting supplier balance:', error);
    throw error;
  }
}

/**
 * Get purchase statistics for a supplier
 */
export async function getSupplierPurchaseStats(supplierId: string): Promise<{
  totalTransactions: number;
  totalAmount: number;
  averageTransactionAmount: number;
  lastPurchaseDate?: string;
}> {
  const transactions = await getAllPurchaseTransactions({ supplierId });
  
  const totalAmount = transactions.reduce((sum, tx) => sum + tx.totalCost, 0);
  const averageTransactionAmount = transactions.length > 0 ? totalAmount / transactions.length : 0;
  
  return {
    totalTransactions: transactions.length,
    totalAmount,
    averageTransactionAmount,
    lastPurchaseDate: transactions[0]?.date // Already sorted by date desc
  };
}