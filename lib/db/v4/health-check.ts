/**
 * 🚀 DATABASE HEALTH CHECK UTILITIES
 * Production-ready health monitoring for database operations
 */

import { getSystemHealthStats } from './core/conflict-resolution';
import { databaseV4 } from './core/db-instance';

export interface DatabaseHealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  database: {
    initialized: boolean;
    connectionStatus: 'connected' | 'disconnected' | 'unknown';
  };
  operations: {
    totalOperations: number;
    successRate: number;
    avgDurationMs: number;
    avgAttempts: number;
  } | null;
  alerts: string[];
}

/**
 * Perform comprehensive database health check
 */
export async function checkDatabaseHealth(): Promise<DatabaseHealthStatus> {
  const timestamp = new Date().toISOString();
  const alerts: string[] = [];
  
  // Check database initialization
  const isInitialized = databaseV4.isInitialized;
  
  // Get operation metrics
  const operationStats = getSystemHealthStats();
  
  // Determine overall health status
  let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
  
  // Check database initialization
  if (!isInitialized) {
    status = 'unhealthy';
    alerts.push('Database not initialized');
  }
  
  // Check operation success rate
  if (operationStats) {
    if (operationStats.successRate < 80) {
      status = 'unhealthy';
      alerts.push(`Low success rate: ${operationStats.successRate}%`);
    } else if (operationStats.successRate < 95) {
      if (status === 'healthy') status = 'degraded';
      alerts.push(`Degraded success rate: ${operationStats.successRate}%`);
    }
    
    // Check average duration
    if (operationStats.avgDurationMs > 3000) {
      if (status === 'healthy') status = 'degraded';
      alerts.push(`Slow operations detected: ${operationStats.avgDurationMs}ms average`);
    }
    
    // Check retry rate
    if (operationStats.avgAttempts > 2) {
      if (status === 'healthy') status = 'degraded';
      alerts.push(`High retry rate: ${operationStats.avgAttempts} average attempts`);
    }
  } else {
    // No recent operations - could be good or concerning
    if (isInitialized) {
      alerts.push('No recent database operations');
    }
  }
  
  return {
    status,
    timestamp,
    database: {
      initialized: isInitialized,
      connectionStatus: isInitialized ? 'connected' : 'disconnected'
    },
    operations: operationStats,
    alerts
  };
}

/**
 * Get simple health status for quick checks
 */
export async function quickHealthCheck(): Promise<boolean> {
  try {
    const health = await checkDatabaseHealth();
    return health.status !== 'unhealthy';
  } catch (error) {
    console.error('Health check failed:', error);
    return false;
  }
}

/**
 * Log health status (for background monitoring)
 */
export async function logHealthStatus(): Promise<void> {
  try {
    const health = await checkDatabaseHealth();
    
    if (health.status === 'unhealthy') {
      console.error('🚨 [HEALTH] Database is UNHEALTHY:', health);
    } else if (health.status === 'degraded') {
      console.warn('⚠️ [HEALTH] Database performance degraded:', health);
    } else {
      console.log('✅ [HEALTH] Database is healthy:', {
        successRate: health.operations?.successRate,
        avgDuration: health.operations?.avgDurationMs
      });
    }
  } catch (error) {
    console.error('❌ [HEALTH] Health check failed:', error);
  }
}

/**
 * Start periodic health monitoring
 */
export function startHealthMonitoring(intervalMs: number = 300000): () => void {
  console.log(`🏥 [HEALTH] Starting health monitoring every ${intervalMs}ms`);
  
  const interval = setInterval(() => {
    logHealthStatus();
  }, intervalMs);
  
  // Initial check
  setTimeout(() => logHealthStatus(), 1000);
  
  // Return cleanup function
  return () => {
    clearInterval(interval);
    console.log('🏥 [HEALTH] Health monitoring stopped');
  };
}