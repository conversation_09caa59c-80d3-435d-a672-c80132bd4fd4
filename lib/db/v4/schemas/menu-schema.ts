"use client";

/**
 * Menu Schema for V4 Database
 * 
 * This file defines the schema and interfaces for menu-related documents
 * in the v4 database implementation.
 */

// Interface for packaging consumption items
export interface PackagingItem {
  stockItemId: string;
  quantity: number;
}

// Interface for packaging configuration by order type
export interface PackagingConfigByOrderType {
  'dine-in'?: PackagingItem[];
  'takeaway'?: PackagingItem[];
  'delivery'?: PackagingItem[];
}

// NEW: Supplement Interface - completely separate from MenuItem
export interface Supplement {
  id: string;
  name: string;
  description?: string;
  // Stock consumption configuration for supplements
  stockConsumption?: {
    stockItemId: string; // ONE stock item for all sizes
    quantities: { [sizeName: string]: number }; // Different quantities per size
  };
  // Optional metadata
  color?: string;
  image?: string;
  isActive?: boolean; // Whether this supplement is available
}

// Menu Document Schema
export const menuDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'schemaVersion', 'createdAt', 'updatedAt', 'categories'],
  properties: {
    _id: {
      type: 'string',
      enum: ['menu']
    },
    _rev: {
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: {
      type: 'string',
      enum: ['menu_document']
    },
    schemaVersion: {
      type: 'string',
      enum: ['v4.0']
    },
    createdAt: {
      type: 'string',
      format: 'date-time'
    },
    updatedAt: {
      type: 'string',
      format: 'date-time'
    },
    categories: {
      type: 'array',
      items: {
        type: 'object',
        required: ['id', 'name', 'items'],
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          emoji: { type: 'string' },
          color: { type: 'string' },
          sizes: {
            type: 'array',
            items: { type: 'string' }
          },
          packaging: {
            type: 'object',
            additionalProperties: {
              type: 'object',
              properties: {
                'dine-in': {
                  type: 'array',
                  items: {
                    type: 'object',
                    required: ['stockItemId', 'quantity'],
                    properties: {
                      stockItemId: { type: 'string' },
                      quantity: { type: 'number' }
                    }
                  }
                },
                'takeaway': {
                  type: 'array',
                  items: {
                    type: 'object',
                    required: ['stockItemId', 'quantity'],
                    properties: {
                      stockItemId: { type: 'string' },
                      quantity: { type: 'number' }
                    }
                  }
                },
                'delivery': {
                  type: 'array',
                  items: {
                    type: 'object',
                    required: ['stockItemId', 'quantity'],
                    properties: {
                      stockItemId: { type: 'string' },
                      quantity: { type: 'number' }
                    }
                  }
                }
              }
            }
          },
          items: {
            type: 'array',
            items: {
              type: 'object',
              required: ['id', 'name', 'prices'],
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                description: { type: 'string' },
                prices: {
                  type: 'object',
                  additionalProperties: { type: 'number' }
                },
                image: { type: 'string' },
              }
            }
          },
          // NEW: Category-specific supplements
          supplements: {
            type: 'array',
            items: {
              type: 'object',
              required: ['id', 'name'],
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                description: { type: 'string' },
                stockConsumption: {
                  type: 'object',
                  properties: {
                    stockItemId: { type: 'string' },
                    quantities: {
                      type: 'object',
                      additionalProperties: { type: 'number' }
                    }
                  }
                },
                color: { type: 'string' },
                image: { type: 'string' },
                isActive: { type: 'boolean' }
              }
            }
          },
          // NEW: Category-specific supplement configuration
          supplementConfig: {
            type: 'object',
            properties: {
              globalPricing: {
                type: 'object',
                additionalProperties: { type: 'number' }
              },
              isEnabled: { type: 'boolean' }
            }
          },
          // 🍕 NEW: Custom Pizza Configuration
          isQuarterable: { type: 'boolean' },
          quarterPricingMethod: {
            type: 'string',
            enum: ['max', 'average', 'fixed']
          },
          fixedPricing: {
            type: 'object',
            additionalProperties: { type: 'number' }
          }
        }
      }
    }
    // Supplements are now category-specific, not global
  }
};

// Menu Document Interface
export interface MenuDocument {
  _id: string;
  _rev?: string; // PouchDB revision field - optional for new docs, required for updates
  type: 'menu_document';
  schemaVersion: 'v4.0';
  createdAt: string;
  updatedAt: string;
  categories: MenuCategory[];
  // Supplements are now category-specific, not global
}

// Menu Category Interface
export interface MenuCategory {
  id: string;
  name: string;
  emoji?: string;
  color?: string;
  sizes?: string[];
  packaging?: { [sizeName: string]: PackagingConfigByOrderType };
  items: MenuItem[];
  // NEW: Category-specific supplements
  supplements?: Supplement[];
  // NEW: Category-specific supplement configuration
  supplementConfig?: {
    globalPricing: { [sizeName: string]: number };
    isEnabled?: boolean;
  };
  // 🍕 NEW: Custom Pizza Configuration
  isQuarterable?: boolean;           // Enable quarter split for this category
  quarterPricingMethod?: 'max' | 'average' | 'fixed'; // Pricing calculation method
  fixedPricing?: { [sizeName: string]: number }; // Fixed prices per size
}

// Menu Item Interface - CLEANED UP, no more supplement fields
export interface MenuItem {
  id: string;
  name: string;
  description?: string;
  prices: { [key: string]: number };
  image?: string;
  color?: string;
  // No more type field or stockConsumption - those are for supplements only
}

// Default empty menu document
export const DEFAULT_MENU_DOCUMENT: MenuDocument = {
  _id: 'menu',
  type: 'menu_document',
  schemaVersion: 'v4.0',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  categories: []
  // Supplements are now category-specific, not global
};
