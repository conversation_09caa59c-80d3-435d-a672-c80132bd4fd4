// knowledge: v4 production batch schema (match v3)
export const productionBatchSchema = {
  type: 'object',
  required: ['_id', 'type', 'subRecipeId', 'batchSize', 'producedQuantity', 'date', 'performedBy', 'createdAt', 'updatedAt'],
  properties: {
    _id: { type: 'string' },
    _rev: {
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: { type: 'string', enum: ['production-batch'] },
    subRecipeId: { type: 'string' },
    batchSize: { type: 'number' },
    producedQuantity: { type: 'number' },
    date: { type: 'string' },
    performedBy: { type: 'string' },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' },
    costPerUnit: { type: 'number' },
    // Training mode support
    is_training: { type: 'boolean' }
  },
  additionalProperties: false
};

export interface ProductionBatch {
  _id: string;
  _rev?: string; // PouchDB revision field - optional for new docs, required for updates
  type: 'production-batch';
  subRecipeId: string;
  batchSize: number;
  producedQuantity: number;
  date: string;
  performedBy: string;
  createdAt: string;
  updatedAt: string;
  costPerUnit?: number;
  // Training mode support
  is_training?: boolean;
}

export const DEFAULT_PRODUCTION_BATCH: ProductionBatch = {
  _id: '',
  type: 'production-batch',
  subRecipeId: '',
  batchSize: 0,
  producedQuantity: 0,
  date: '',
  performedBy: '',
  createdAt: '',
  updatedAt: '',
  costPerUnit: 0
};
// endknowledge 