"use client";

import {
  getMenu,
  addCategory as addCategoryOp,
  updateCategory as updateCategoryOp,
  deleteCategory as deleteCategoryOp,
  addMenuItem as addMenuItemOp,
  updateMenuItem as updateMenuItemOp,
  deleteMenuItem as deleteMenuItemOp,
  addSizeToCategory as addSizeToCategoryOp,
  renameSizeInCategory as renameSizeInCategoryOp,
  deleteSizeFromCategory as deleteSizeFromCategoryOp,
} from './v4/index';
import type { MenuCategory, MenuItem } from './v4/schemas/menu-schema';
import { v4 as uuidv4 } from 'uuid';

// Export Category type with the same interface as the v3 version
export interface Category extends MenuCategory {}

// Re-export other types
export type { MenuItem };

/**
 * V4 MenuService class - provides functionality for the useMenu hook
 * This class implements the same interface as the v3 MenuService
 */
export class MenuServiceV4 {
  private db: any;

  constructor(db: any) {
    this.db = db;
  }

  /**
   * Get all categories
   */
  async getCategories(): Promise<Category[]> {
    const menu = await getMenu();
    return menu.categories;
  }

  /**
   * Add a new category
   */
  async addCategory(category: Omit<Category, 'createdAt' | 'updatedAt'>): Promise<Category> {
    const menuDoc = await addCategoryOp(category);
    // Find and return the newly added category
    const addedCategory = menuDoc.categories.find(c => c.name === category.name);
    if (!addedCategory) {
      throw new Error('Failed to add category');
    }
    return addedCategory;
  }

  /**
   * Update a category
   */
  async updateCategory(id: string, data: Partial<Category>): Promise<Category> {
    const menuDoc = await updateCategoryOp(id, data);
    // Find and return the updated category
    const updatedCategory = menuDoc.categories.find(c => c.id === id);
    if (!updatedCategory) {
      throw new Error('Failed to update category');
    }
    return updatedCategory;
  }

  /**
   * Delete a category
   */
  async deleteCategory(id: string): Promise<void> {
    await deleteCategoryOp(id);
  }

  /**
   * Add a menu item to a category
   */
  async addMenuItem(categoryId: string, item: Partial<MenuItem>): Promise<MenuItem> {
    // Validate required fields
    if (!item.name) {
      throw new Error('Item name is required');
    }
    
    if (!item.prices || Object.keys(item.prices).length === 0) {
      throw new Error('Item must have at least one price');
    }
    
    // Get the category to check sizes
    const categories = await getMenu();
    const category = categories.categories.find(c => c.id === categoryId);
    
    if (!category) {
      throw new Error(`Category with ID ${categoryId} not found`);
    }
    
    // Validate that the prices match category sizes
    if (category.sizes && category.sizes.length > 0) {
      // Check if prices include all sizes
      const sizesSet = new Set(category.sizes);
      const priceKeys = Object.keys(item.prices);
      
      // Check if every price key is a valid size
      const invalidSizes = priceKeys.filter(size => !sizesSet.has(size));
      if (invalidSizes.length > 0) {
        throw new Error(`Invalid sizes in prices: ${invalidSizes.join(', ')}. Valid sizes are: ${category.sizes.join(', ')}`);
      }
      
      // Check if all sizes have prices
      const missingSizes = category.sizes.filter(size => !priceKeys.includes(size));
      if (missingSizes.length > 0) {
        throw new Error(`Missing prices for sizes: ${missingSizes.join(', ')}. All sizes must have prices.`);
      }
    }
    
    // Create the new item with ID
    const newItem: MenuItem = {
      id: uuidv4(),
      name: item.name,
      prices: item.prices,
      image: item.image || '',
      color: item.color || ''
    };
    
    // Add the item to the category
    const menuDoc = await addMenuItemOp(categoryId, newItem);
    
    // Find and return the newly added item
    const updatedCategory = menuDoc.categories.find(c => c.id === categoryId);
    if (!updatedCategory) {
      throw new Error('Category not found after adding item');
    }
    
    const addedItem = updatedCategory.items.find(i => i.id === newItem.id);
    if (!addedItem) {
      throw new Error('Failed to add menu item');
    }
    
    return addedItem;
  }

  /**
   * Update a menu item
   */
  async updateMenuItem(categoryId: string, itemId: string, data: Partial<MenuItem>): Promise<MenuItem> {
    const menuDoc = await updateMenuItemOp(categoryId, itemId, data);
    
    // Find and return the updated item
    const category = menuDoc.categories.find(c => c.id === categoryId);
    if (!category) {
      throw new Error('Category not found after updating item');
    }
    
    const updatedItem = category.items.find(i => i.id === itemId);
    if (!updatedItem) {
      throw new Error('Failed to update menu item');
    }
    
    return updatedItem;
  }

  /**
   * Delete a menu item
   */
  async deleteMenuItem(categoryId: string, itemId: string): Promise<void> {
    await deleteMenuItemOp(categoryId, itemId);
  }

  /**
   * Add a size to a category
   */
  async addSizeToCategory(categoryId: string, size: string): Promise<string[]> {
    try {
      return await addSizeToCategoryOp(categoryId, size);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Rename a size in a category and update all menu items to use the new size name
   */
  async renameSizeInCategory(categoryId: string, oldSizeName: string, newSizeName: string): Promise<Category> {
    try {
      await renameSizeInCategoryOp(categoryId, oldSizeName, newSizeName);
      
      const menu = await getMenu();
      const category = menu.categories.find(c => c.id === categoryId);
      
      if (!category) {
        throw new Error(`Category with ID ${categoryId} not found after renaming size`);
      }
      
      return category;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete a size from a category
   */
  async deleteSizeFromCategory(categoryId: string, sizeName: string): Promise<string[]> {
    try {
      return await deleteSizeFromCategoryOp(categoryId, sizeName);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Fix categories structure to ensure sizes and addons exist
   */
  async fixCategoriesStructure(): Promise<void> {
    try {
      // This function is not exported from the index, so we'll skip it for now
      console.log('fixCategoriesStructure not available in v4 index');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Repair menu sizes
   */
  async repairMenuSizes(): Promise<void> {
    try {
      // This function is not exported from the index, so we'll skip it for now
      console.log('repairMenuSizes not available in v4 index');
    } catch (error) {
      throw error;
    }
  }
}

// Export singleton instance
export const menuServiceV4 = new MenuServiceV4(null);
