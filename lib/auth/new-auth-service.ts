import { hash, compare } from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { jwtDecode } from 'jwt-decode';

export interface User {
  id: string;
  name: string;
  email?: string;
  username?: string;
  role: string;
  restaurantId: string;
  permissions?: any;   // Added permissions field
  metadata?: any;      // Added metadata field
  restricted?: boolean; // Added restriction status
}

export interface JwtPayload {
  sub: string;         // User ID
  name: string;        // User name
  email?: string;      // User email (optional)
  role: string;        // User role
  restaurantId: string; // Restaurant ID
  permissions?: any;   // User permissions
  metadata?: any;      // User metadata
  restricted?: boolean; // Restriction status
  iat: number;         // Issued at
  exp: number;         // Expiration time
}

/**
 * Generate a JWT token for a user
 */
export function generateToken(user: User): string {
  // Use JWT_SECRET or fall back to NEXTAUTH_SECRET
  const secret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET;

  if (!secret) {
    throw new Error('JWT_SECRET or NEXTAUTH_SECRET is not defined');
  }

  // Validate permissions structure before including in token
  let validatedPermissions = user.permissions;

  // Only validate if permissions exist
  if (validatedPermissions && validatedPermissions.pages) {
    // Ensure all values are boolean
    const requiredPages = ['menu', 'orders', 'finance', 'inventory', 'staff', 'settings', 'suppliers'];

    // Create a normalized permissions object with all required pages
    const normalizedPages = {};

    for (const page of requiredPages) {
      // Convert to boolean, defaulting to false if not present
      normalizedPages[page] = validatedPermissions.pages[page] === true;
    }

    // Replace the original pages with the normalized ones
    validatedPermissions.pages = normalizedPages;

    console.log('TOKEN: Normalized permissions:', JSON.stringify(validatedPermissions));
  } else if (validatedPermissions && (!validatedPermissions.pages || typeof validatedPermissions.pages !== 'object')) {
    console.log('TOKEN: Warning - Permissions exist but have invalid structure');

    // Create a valid permissions structure
    validatedPermissions = {
      pages: {
        menu: false,
        orders: false,
        finance: false,
        inventory: false,
        staff: false,
        settings: false,
        suppliers: false
      }
    };

    console.log('TOKEN: Created valid permissions structure:', JSON.stringify(validatedPermissions));
  } else if (!validatedPermissions && (user.role === 'waiter' || user.role === 'staff')) {
    console.log('TOKEN: Warning - Staff user has no permissions, creating empty structure');

    // Create an empty permissions structure for staff users
    validatedPermissions = {
      pages: {
        menu: false,
        orders: false,
        finance: false,
        inventory: false,
        staff: false,
        settings: false,
        suppliers: false
      }
    };

    console.log('TOKEN: Created empty permissions structure for staff user:', JSON.stringify(validatedPermissions));
  }

  // Include permissions, metadata, and restriction status in the payload
  const payload: JwtPayload = {
    sub: user.id,
    name: user.name,
    email: user.email,
    role: user.role,
    restaurantId: user.restaurantId,
    permissions: validatedPermissions, // Include validated permissions
    metadata: user.metadata,           // Include metadata
    restricted: user.restricted || false, // Include restriction status
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 1 day
  };

  // Log the final permissions being included in the token
  console.log('TOKEN: Final permissions in token payload:', JSON.stringify(validatedPermissions));

  return jwt.sign(payload, secret);
}

/**
 * Generate a refresh token for a user (longer expiration)
 */
export function generateRefreshToken(user: User): string {
  // Use JWT_SECRET or fall back to NEXTAUTH_SECRET
  const secret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET;

  if (!secret) {
    throw new Error('JWT_SECRET or NEXTAUTH_SECRET is not defined');
  }

  // Validate permissions structure before including in token (same as in generateToken)
  let validatedPermissions = user.permissions;

  // Only validate if permissions exist
  if (validatedPermissions && validatedPermissions.pages) {
    // Ensure all values are boolean
    const requiredPages = ['menu', 'orders', 'finance', 'inventory', 'staff', 'settings', 'suppliers'];

    // Create a normalized permissions object with all required pages
    const normalizedPages = {};

    for (const page of requiredPages) {
      // Convert to boolean, defaulting to false if not present
      normalizedPages[page] = validatedPermissions.pages[page] === true;
    }

    // Replace the original pages with the normalized ones
    validatedPermissions.pages = normalizedPages;

    console.log('REFRESH TOKEN: Normalized permissions:', JSON.stringify(validatedPermissions));
  } else if (validatedPermissions && (!validatedPermissions.pages || typeof validatedPermissions.pages !== 'object')) {
    console.log('REFRESH TOKEN: Warning - Permissions exist but have invalid structure');

    // Create a valid permissions structure
    validatedPermissions = {
      pages: {
        menu: false,
        orders: false,
        finance: false,
        inventory: false,
        staff: false,
        settings: false,
        suppliers: false
      }
    };

    console.log('REFRESH TOKEN: Created valid permissions structure:', JSON.stringify(validatedPermissions));
  } else if (!validatedPermissions && (user.role === 'waiter' || user.role === 'staff')) {
    console.log('REFRESH TOKEN: Warning - Staff user has no permissions, creating empty structure');

    // Create an empty permissions structure for staff users
    validatedPermissions = {
      pages: {
        menu: false,
        orders: false,
        finance: false,
        inventory: false,
        staff: false,
        settings: false,
        suppliers: false
      }
    };

    console.log('REFRESH TOKEN: Created empty permissions structure for staff user:', JSON.stringify(validatedPermissions));
  }

  const payload: JwtPayload = {
    sub: `refresh_${user.id}`,
    name: user.name,
    email: user.email,
    role: user.role,
    restaurantId: user.restaurantId,
    permissions: validatedPermissions, // Include validated permissions
    metadata: user.metadata,           // Include metadata
    restricted: user.restricted || false, // Include restriction status
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
  };

  return jwt.sign(payload, secret);
}

/**
 * Refresh an access token using a valid refresh token
 */
export function refreshAccessToken(refreshToken: string): string | null {
  try {
    const decoded = verifyToken(refreshToken);
    
    if (!decoded || !decoded.sub.startsWith('refresh_')) {
      return null;
    }

    // Extract user ID from refresh token subject
    const userId = decoded.sub.replace('refresh_', '');
    
    // Create a new user object from the refresh token payload
    const user: User = {
      id: userId,
      name: decoded.name,
      email: decoded.email,
      role: decoded.role,
      restaurantId: decoded.restaurantId,
      permissions: decoded.permissions,
      metadata: decoded.metadata,
      restricted: decoded.restricted
    };

    // Generate a new access token
    return generateToken(user);
  } catch (error) {
    console.error('Error refreshing access token:', error);
    return null;
  }
}

/**
 * Check if a token is about to expire (within next 5 minutes)
 */
export function isTokenExpiringSoon(token: string): boolean {
  try {
    const decoded = verifyToken(token);
    if (!decoded) return true;

    const currentTime = Math.floor(Date.now() / 1000);
    const fiveMinutesFromNow = currentTime + (5 * 60);
    
    return decoded.exp <= fiveMinutesFromNow;
  } catch (error) {
    return true;
  }
}

/**
 * Verify a JWT token
 */
export function verifyToken(token: string): JwtPayload | null {
  try {
    // Check if we're in Edge Runtime or offline mode (Node.js crypto not available)
    if (typeof process === 'undefined' || process.env?.NEXT_RUNTIME === 'edge' || 
        process.env.BUILD_TARGET === 'static' || process.env.BUILD_TARGET === 'electron') {
      // Use jwtDecode for Edge Runtime and offline static builds
      const decoded = jwtDecode<JwtPayload>(token);

      // Basic validation (check expiry)
      const currentTime = Math.floor(Date.now() / 1000);
      if (decoded.exp && decoded.exp < currentTime) {
        console.log('Token expired in offline/edge runtime');
        return null;
      }

      return decoded;
    }

    // Use JWT_SECRET or fall back to NEXTAUTH_SECRET for server verification
    const secret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET;

    if (!secret) {
      throw new Error('JWT_SECRET or NEXTAUTH_SECRET is not defined');
    }

    // Full signature verification for server environments
    return jwt.verify(token, secret) as JwtPayload;
  } catch (error) {
    console.error('Error verifying token:', error);
    return null;
  }
}

/**
 * Generate a password hash
 */
export async function hashPassword(password: string): Promise<string> {
  return hash(password, 12);
}

/**
 * Verify a password against a hash
 */
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return compare(password, hashedPassword);
}