import { hash } from 'bcryptjs';
import clientPromise from '@/lib/mongodb';
import { AuthUser, DEFAULT_STAFF_PERMISSIONS, DEFAULT_ADMIN_PERMISSIONS } from '@/lib/db/v4/schemas/auth-schema'; // Assuming path is correct
import { v4 as uuidv4 } from 'uuid';

// Re-define or import User type similar to what new-auth-service expects for clarity if needed,
// or directly use parts of AuthUser for input. For now, we'll construct AuthUser internally.

export interface CreateMongoUserInput {
  name: string;
  email?: string; // Required for owners/admins, optional for staff
  username?: string; // Required for staff, optional for owners/admins
  plaintextPassword: string;
  role: AuthUser['role'];
  restaurantId: string; // Should be the MongoDB _id of the restaurant document if applicable, or your internal restaurant identifier
  staffIdToLink?: string; // The UUID that will also be the PouchDB staffDoc _id
  metadata?: Partial<AuthUser['metadata']>;
}

export async function createMongoUser(
  input: CreateMongoUserInput
): Promise<{ success: boolean; userId?: string; error?: string; user?: Omit<AuthUser, 'password'> }> {
  console.log('[MongoOps] Attempting to create user with input:', { name: input.name, email: input.email, username: input.username, role: input.role, restaurantId: input.restaurantId });

  // Wrap the MongoDB operation in a timeout to prevent hanging
  try {
    // Create a timeout promise
    const timeoutPromise = new Promise<{ success: false, error: string }>((resolve) => {
      setTimeout(() => {
        resolve({ success: false, error: "MongoDB operation timed out after 15 seconds" });
      }, 15000); // 15-second timeout to match MongoDB client settings
    });

    // The actual MongoDB operation
    const mongoOperation = async (): Promise<{ success: boolean; userId?: string; error?: string; user?: Omit<AuthUser, 'password'> }> => {
      try {
        const mongoClient = await clientPromise;
        console.log('[MongoOps] MongoDB client promise resolved.');
        const db = mongoClient.db('resto');
        console.log(`[MongoOps] Using database: ${db.databaseName}`);

        // Ensure the users collection exists
        const collections = await db.listCollections({ name: 'users' }).toArray();
        if (collections.length === 0) {
          console.log('[MongoOps] Creating users collection');
          await db.createCollection('users');
        }

        const usersCollection = db.collection<AuthUser>('users');

        // Log the role for debugging
        console.log(`[MongoOps] Processing user with role: '${input.role}'`);

        // Normalize role to lowercase for consistent comparison
        const normalizedRole = typeof input.role === 'string' ? input.role.toLowerCase() : input.role;

        if (normalizedRole === 'owner' || normalizedRole === 'admin') {
          if (!input.email) {
            console.error('[MongoOps] Email is required for owner/admin roles.');
            return { success: false, error: 'Email is required for owner/admin roles.' };
          }
          console.log(`[MongoOps] Checking for existing user with email: ${input.email}`);
          const existingEmailUser = await usersCollection.findOne({ email: input.email });
          if (existingEmailUser) {
            console.warn(`[MongoOps] User with email ${input.email} already exists.`);
            return { success: false, error: 'User with this email already exists.' };
          }
        } else if (normalizedRole === 'staff' || normalizedRole === 'manager' ||
                  normalizedRole === 'waiter' || normalizedRole === 'chef' ||
                  normalizedRole === 'cashier') {
          if (!input.username) {
            console.error('[MongoOps] Username is required for staff roles.');
            return { success: false, error: 'Username is required for staff roles.' };
          }
          console.log(`[MongoOps] Checking for existing staff with username: ${input.username} GLOBALLY`);
          const existingUsernameUser = await usersCollection.findOne({ username: input.username });
          if (existingUsernameUser) {
            console.warn(`[MongoOps] Staff with username ${input.username} already exists GLOBALLY.`);
            return { success: false, error: 'Staff with this username already exists.' };
          }
        } else if (!input.email && !input.username) {
          console.error('[MongoOps] Either email or username must be provided.');
          return { success: false, error: 'Either email or username must be provided.' };
        } else {
          console.log(`[MongoOps] Using default staff role handling for role: '${input.role}'`);
          if (!input.username) {
            console.error('[MongoOps] Username is required for this role.');
            return { success: false, error: 'Username is required for this role.' };
          }
          // Check for existing username GLOBALLY (no restaurant filter)
          const existingUsernameUser = await usersCollection.findOne({ username: input.username });
          if (existingUsernameUser) {
            console.warn(`[MongoOps] User with username ${input.username} already exists GLOBALLY.`);
            return { success: false, error: 'User with this username already exists.' };
          }
        }

        console.log('[MongoOps] Hashing password...');
        const hashedPassword = await hash(input.plaintextPassword, 12);
        console.log('[MongoOps] Password hashed.');

        const userId = input.staffIdToLink || uuidv4();

        // Map non-standard roles to standard roles
        let standardRole: AuthUser['role'] = 'staff'; // Default to staff

        if (normalizedRole === 'owner' || normalizedRole === 'admin') {
          standardRole = normalizedRole as AuthUser['role'];
        } else if (normalizedRole === 'manager') {
          standardRole = 'manager';
        } else if (['waiter', 'chef', 'cashier', 'bartender', 'host', 'kitchen_helper', 'cleaner', 'delivery'].includes(normalizedRole)) {
          standardRole = 'staff';
        } else if (normalizedRole === 'user') {
          standardRole = 'user';
        }

        console.log(`[MongoOps] Mapped role '${input.role}' to standard role '${standardRole}'`);

        // Assign permissions based on the standard role
        let permissions;
        if (standardRole === 'owner' || standardRole === 'admin') {
          permissions = DEFAULT_ADMIN_PERMISSIONS;
        } else if (standardRole === 'staff' || standardRole === 'manager') {
          permissions = DEFAULT_STAFF_PERMISSIONS;
        } else {
          permissions = { pages: [], tabs: {}, components: [] };
        }

        const newUserDoc: AuthUser = {
          _id: userId,
          type: 'user',
          name: input.name,
          email: input.email,
          username: input.username,
          password: hashedPassword,
          role: standardRole, // Use the standardized role
          restaurantId: input.restaurantId,
          permissions: permissions,
          metadata: {
            ...(input.metadata || {}),
            staffId: input.staffIdToLink || userId,
            originalRole: input.role // Store the original role for reference
          },
          schemaVersion: '1.0-mongo',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        console.log(`[MongoOps] Attempting to insert new user document with _id: ${newUserDoc._id}`);
        const result = await usersCollection.insertOne(newUserDoc);

        if (result.insertedId) {
          console.log(`[MongoOps] User successfully created with _id: ${result.insertedId.toString()}`);
          const { password, ...userWithoutPassword } = newUserDoc;
          return { success: true, userId: result.insertedId.toString(), user: userWithoutPassword };
        } else {
          console.error('[MongoOps] User creation failed in MongoDB insertOne operation.');
          return { success: false, error: 'User creation failed in MongoDB.' };
        }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        console.error('[MongoOps] Critical error in createMongoUser:', error);
        return { success: false, error: `Error creating user: ${errorMessage}` };
      }
    };

    // Race between the MongoDB operation and timeout
    return await Promise.race([mongoOperation(), timeoutPromise]);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('[MongoOps] Unhandled exception in createMongoUser:', error);
    return { success: false, error: `Unhandled error creating user: ${errorMessage}` };
  }
}

export async function getMongoUserByUsername(
  username: string,
  restaurantId?: string // Now optional
): Promise<AuthUser | null> {
  console.log(`[MongoOps] getMongoUserByUsername: Searching for username '${username}'${restaurantId ? ` in restaurant '${restaurantId}'` : ' (globally)'}.`);
  try {
    // Create a timeout promise
    const timeoutPromise = new Promise<AuthUser | null>((resolve) => {
      setTimeout(() => {
        console.error('[MongoOps] getMongoUserByUsername timed out after 15 seconds');
        resolve(null);
      }, 15000); // 15-second timeout to match MongoDB client settings
    });

    // The actual MongoDB operation
    const mongoOperation = async (): Promise<AuthUser | null> => {
      try {
        const mongoClient = await clientPromise;
        const db = mongoClient.db('resto'); // Use your actual database name
        console.log(`[MongoOps] getMongoUserByUsername: Querying database: '${db.databaseName}' and collection: 'users'.`);

        // Ensure the users collection exists
        const collections = await db.listCollections({ name: 'users' }).toArray();
        if (collections.length === 0) {
          console.log('[MongoOps] Creating users collection');
          await db.createCollection('users');
        }

        const usersCollection = db.collection<AuthUser>('users');
        if (restaurantId) {
          // If restaurantId is provided, search by both username and restaurantId
          console.log(`[MongoOps] getMongoUserByUsername: Executing findOne({ username: "${username}", restaurantId: "${restaurantId}" })`);
          return await usersCollection.findOne({ username, restaurantId });
        } else {
          // Otherwise, search by username only
          // If multiple users exist with the same username, return the first one
          console.log(`[MongoOps] getMongoUserByUsername: Executing findOne({ username: "${username}" })`);
          const user = await usersCollection.findOne({ username });
          console.log(`[MongoOps] getMongoUserByUsername: findOne result for '${username}':`, user ? { _id: user._id, username: user.username, name: user.name } : null);
          return user;
        }
      } catch (error) {
        console.error('[MongoOps] Error fetching user by username from MongoDB:', error);
        return null;
      }
    };

    // Race between the MongoDB operation and timeout
    return await Promise.race([mongoOperation(), timeoutPromise]);
  } catch (error) {
    console.error('[MongoOps] Error fetching user by username from MongoDB:', error);
    return null;
  }
}

export async function getMongoUserByEmail(
  email: string
): Promise<AuthUser | null> {
  try {
    // Create a timeout promise
    const timeoutPromise = new Promise<AuthUser | null>((resolve) => {
      setTimeout(() => {
        console.error('[MongoOps] getMongoUserByEmail timed out after 15 seconds');
        resolve(null);
      }, 15000); // 15-second timeout to match MongoDB client settings
    });

    // The actual MongoDB operation
    const mongoOperation = async (): Promise<AuthUser | null> => {
      try {
        const mongoClient = await clientPromise;
        const db = mongoClient.db('resto'); // Use your actual database name

        // Ensure the users collection exists
        const collections = await db.listCollections({ name: 'users' }).toArray();
        if (collections.length === 0) {
          console.log('[MongoOps] Creating users collection');
          await db.createCollection('users');
        }

        const usersCollection = db.collection<AuthUser>('users');
        const user = await usersCollection.findOne({ email });
        return user;
      } catch (error) {
        console.error('Error fetching user by email from MongoDB:', error);
        return null;
      }
    };

    // Race between the MongoDB operation and timeout
    return await Promise.race([mongoOperation(), timeoutPromise]);
  } catch (error) {
    console.error('Error fetching user by email from MongoDB:', error);
    return null;
  }
}

export async function getMongoUserById(
  userId: string
): Promise<AuthUser | null> {
  try {
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');

    // Ensure the users collection exists
    const collections = await db.listCollections({ name: 'users' }).toArray();
    if (collections.length === 0) {
      console.log('[MongoOps] Creating users collection');
      await db.createCollection('users');
    }

    const usersCollection = db.collection<AuthUser>('users');
    const user = await usersCollection.findOne({ _id: userId });
    return user;
  } catch (error) {
    console.error('Error fetching user by ID from MongoDB:', error);
    return null;
  }
}

export interface UpdateMongoUserCredentialsInput {
  username?: string;
  plaintextPassword?: string;
  // Add other updatable fields here if needed, e.g., role, email, etc.
  // For now, focusing on credentials for the PUT route.
}

export async function updateMongoUserCredentials(
  userId: string,
  updates: UpdateMongoUserCredentialsInput
): Promise<{ success: boolean; error?: string }> {
  try {
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');

    // Ensure the users collection exists
    const collections = await db.listCollections({ name: 'users' }).toArray();
    if (collections.length === 0) {
      console.log('[MongoOps] Creating users collection');
      await db.createCollection('users');
    }

    const usersCollection = db.collection<AuthUser>('users');

    const updateFields: Partial<AuthUser> & { $set?: any } = {};
    if (updates.username) {
      // Potentially check for username uniqueness again if it's being changed, depending on rules.
      // For simplicity, assuming direct update here.
      updateFields.username = updates.username;
    }
    if (updates.plaintextPassword) {
      updateFields.password = await hash(updates.plaintextPassword, 12);
    }

    if (Object.keys(updateFields).length === 0) {
      return { success: true }; // No actual updates to perform
    }

    updateFields.updatedAt = new Date().toISOString();

    const result = await usersCollection.updateOne(
      { _id: userId },
      { $set: updateFields }
    );

    if (result.matchedCount === 0) {
      return { success: false, error: 'User not found for update.' };
    }
    // modifiedCount could be 0 if the data provided was the same as existing, which is fine.
    return { success: true };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error(`Error updating MongoDB user credentials for ${userId}:`, error);
    return { success: false, error: `Error updating credentials: ${errorMessage}` };
  }
}