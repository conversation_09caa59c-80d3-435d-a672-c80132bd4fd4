/**
 * Simple Restaurant Credentials
 * 
 * Provides simple admin:restaurantId credentials for CouchDB access.
 * No complex credential management - just returns restaurant-specific auth.
 */

/**
 * Get restaurant credentials for CouchDB access
 * Always returns admin:restaurantId
 */
export function getRestaurantCredentials(restaurantId: string): { username: string; password: string } {
  return {
    username: 'admin',
    password: restaurantId
  };
}

/**
 * Get credentials for direct CouchDB access (used by desktop app)
 */
export function getDeviceCredentialsForDirectAccess(
  deviceId: string,
  restaurantId: string
): { username: string; password: string } {
  return getRestaurantCredentials(restaurantId);
}