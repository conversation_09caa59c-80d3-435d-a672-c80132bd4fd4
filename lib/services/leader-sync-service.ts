/**
 * Leader Sync Service 👑
 * 
 * Single-leader sync system that:
 * - Enforces one leader per restaurant (prevents split-brain)
 * - Followers ONLY sync with verified leader
 * - Leader handles both LAN + Internet sync authority
 * - Auto-elects new leader on failure
 */

import { discoverCouchDBServers, type DiscoveredServer } from './ip-discovery';
import { internetDiscoveryService, type InternetDiscoveryConfig } from './internet-discovery';
import { internetSyncService, type InternetSyncConfig } from './internet-sync';
import { nativeSyncService, type SyncServer, type SyncStatus } from './native-sync';
import type { DiscoveredPeer } from './internet-sync';
import { multiUserSessionManager } from '@/lib/auth/multi-user-session-manager';
import { getCurrentRestaurantId } from '@/lib/db/v4/utils/restaurant-id';

interface LeaderSyncServer extends SyncServer {
  responseTime?: number;
  lastSeen?: Date;
  syncType: 'local' | 'internet';
  peerId?: string;
  isLeader?: boolean;
  leaderEpoch?: number;
}

interface LeaderConfig {
  // Local sync settings
  localDiscoveryInterval: number;
  localReconnectInterval: number;
  
  // Internet sync settings
  internetDiscoveryInterval: number;
  internetReconnectInterval: number;
  vpsBaseUrl: string;
  authToken: string;
  deviceId: string;
  deviceType: 'desktop' | 'mobile';
  
  // General settings
  maxReconnectAttempts: number;
  autoStart: boolean;
  preferLocalSync: boolean; // Prefer local over internet when both available
  
  // Device registration for internet sync
  deviceRegistration?: {
    ipAddress: string;
    couchdbPort?: number;
  };
}

interface LeaderStatus {
  isRunning: boolean;
  
  // Local sync status
  localDiscovering: boolean;
  localServers: LeaderSyncServer[];
  localConnected: boolean;
  
  // Internet sync status  
  internetDiscovering: boolean;
  internetPeers: LeaderSyncServer[];
  currentLeader: LeaderSyncServer | null;
  internetConnected: boolean;
  internetRegistered: boolean;
  
  // Current connection
  currentConnection: {
    server: LeaderSyncServer | null;
    type: 'local' | 'internet' | null;
  };
  
  // Discovery timestamps
  lastLocalDiscovery: Date | null;
  lastInternetDiscovery: Date | null;
  
  // Sync status
  syncStatus: SyncStatus;
  
  // Error states
  error: string | null;
}

class LeaderSyncService {
  private config: LeaderConfig | null = null;
  private status: LeaderStatus = {
    isRunning: false,
    localDiscovering: false,
    localServers: [],
    localConnected: false,
    internetDiscovering: false,
    internetPeers: [],
    currentLeader: null,
    internetConnected: false,
    internetRegistered: false,
    currentConnection: { server: null, type: null },
    lastLocalDiscovery: null,
    lastInternetDiscovery: null,
    syncStatus: nativeSyncService.getStatus(),
    error: null
  };

  private localDiscoveryTimer: NodeJS.Timeout | null = null;
  private internetDiscoveryTimer: NodeJS.Timeout | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private listeners: ((status: LeaderStatus) => void)[] = [];
  private isLeader = false;
  private leaderEpoch = 0;

  constructor() {
    // Listen to native sync status changes
    nativeSyncService.onStatusChange((syncStatus) => {
      this.status.syncStatus = syncStatus;
      
      // Update connection status
      if (this.status.currentConnection.type === 'local') {
        this.status.localConnected = syncStatus.connected;
      }
      
      this.emitStatusUpdate();
    });

    // Listen to internet sync status changes
    internetSyncService.onStatusChange((internetStatus) => {
      if (this.status.currentConnection.type === 'internet') {
        this.status.internetConnected = internetStatus.connected;
      }
      this.emitStatusUpdate();
    });
  }

  async initialize(config: LeaderConfig): Promise<void> {
    console.log('👑 [LeaderSync] Initializing leader sync service...');
    
    this.config = config;

    // Configure internet services
    internetDiscoveryService.configure({
      vpsBaseUrl: config.vpsBaseUrl,
      authToken: config.authToken,
      deviceId: config.deviceId,
      deviceType: config.deviceType
    });

    internetSyncService.configure({
      vpsBaseUrl: config.vpsBaseUrl,
      authToken: config.authToken,
      deviceId: config.deviceId
    });

    // Register device for internet sync if registration info provided
    if (config.deviceRegistration) {
      try {
        await internetDiscoveryService.registerDesktopServer({
          deviceId: config.deviceId,
          couchdbPort: config.deviceRegistration.couchdbPort || 5984
        });
        this.status.internetRegistered = true;
      } catch (error: any) {
        console.error('🔄 [HybridSync] Device registration failed:', error);
        this.status.error = `Device registration failed: ${error.message}`;
      }
    }

    // Auto-start if enabled
    if (config.autoStart) {
      await this.start();
    }

    console.log('🔄 [HybridSync] Initialized successfully');
  }

  async start(): Promise<void> {
    if (!this.config) {
      throw new Error('HybridSync not configured. Call initialize() first.');
    }

    if (this.status.isRunning) {
      console.log('🔄 [HybridSync] Already running');
      return;
    }

    console.log('🔄 [HybridSync] Starting hybrid sync...');
    this.status.isRunning = true;
    this.status.error = null;
    this.emitStatusUpdate();

    // Start discovery processes
    await this.performLocalDiscovery();
    await this.performInternetDiscovery();

    // Start periodic discovery
    this.startPeriodicDiscovery();

    // Start connection management
    this.startConnectionManagement();

    console.log('🔄 [HybridSync] Started successfully');
  }

  async stop(): Promise<void> {
    console.log('🔄 [HybridSync] Stopping hybrid sync...');
    
    this.status.isRunning = false;
    
    // Clear all timers
    if (this.localDiscoveryTimer) {
      clearInterval(this.localDiscoveryTimer);
      this.localDiscoveryTimer = null;
    }
    
    if (this.internetDiscoveryTimer) {
      clearInterval(this.internetDiscoveryTimer);
      this.internetDiscoveryTimer = null;
    }
    
    if (this.reconnectTimer) {
      clearInterval(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    // Disconnect current sync
    await this.disconnectCurrentSync();

    // Unregister device
    if (this.status.internetRegistered) {
      await internetDiscoveryService.unregisterDevice();
      this.status.internetRegistered = false;
    }

    // Reset status
    this.status.currentConnection = { server: null, type: null };
    this.status.localConnected = false;
    this.status.internetConnected = false;
    
    this.emitStatusUpdate();
    console.log('🔄 [HybridSync] Stopped');
  }

  private async performLocalDiscovery(): Promise<void> {
    if (!this.config || this.status.localDiscovering) return;

    this.status.localDiscovering = true;
    this.emitStatusUpdate();

    try {
      console.log('🏠 [LeaderSync] Discovering local servers...');
      const discovered = await discoverCouchDBServers({
        timeout: 2000,
        maxConcurrent: 10
      });

      this.status.localServers = discovered.map(server => ({
        ip: server.ip,
        port: server.port,
        url: server.url,
        syncType: 'local' as const,
        responseTime: server.responseTime,
        lastSeen: server.lastSeen,
        isLeader: server.isLeader,
        leaderEpoch: server.leaderEpoch
      }));

      // 👑 Find current leader or elect new one
      await this.handleLeaderElection();

      this.status.lastLocalDiscovery = new Date();
      console.log(`🏠 [LeaderSync] Found ${this.status.localServers.length} local server(s), leader: ${this.status.currentLeader?.ip || 'none'}`);
    } catch (error: any) {
      console.error('🏠 [LeaderSync] Local discovery failed:', error);
    } finally {
      this.status.localDiscovering = false;
      this.emitStatusUpdate();
    }
  }

  private async performInternetDiscovery(): Promise<void> {
    if (!this.config || this.status.internetDiscovering || !this.status.internetRegistered) return;

    this.status.internetDiscovering = true;
    this.emitStatusUpdate();

    try {
      console.log('🌐 [HybridSync] Discovering internet peers...');
      const peers = await internetDiscoveryService.discoverPeers();

      this.status.internetPeers = peers.map(peer => ({
        ip: peer.ipAddress,
        port: peer.couchdbPort,
        url: `${this.config!.vpsBaseUrl}/api/sync/proxy/${peer.id}`,
        syncType: 'internet' as const,
        peerId: peer.id,
        lastSeen: peer.lastSeen
      }));

      this.status.lastInternetDiscovery = new Date();
      console.log(`🌐 [HybridSync] Found ${this.status.internetPeers.length} internet peer(s)`);
    } catch (error: any) {
      console.error('🌐 [HybridSync] Internet discovery failed:', error);
    } finally {
      this.status.internetDiscovering = false;
      this.emitStatusUpdate();
    }
  }

  private startPeriodicDiscovery(): void {
    if (!this.config) return;

    // Local discovery
    this.localDiscoveryTimer = setInterval(() => {
      this.performLocalDiscovery();
    }, this.config.localDiscoveryInterval);

    // Internet discovery
    this.internetDiscoveryTimer = setInterval(() => {
      this.performInternetDiscovery();
    }, this.config.internetDiscoveryInterval);
  }

  private startConnectionManagement(): void {
    if (!this.config) return;

    // Check and manage connections every 10 seconds
    this.reconnectTimer = setInterval(() => {
      this.manageConnection();
    }, 10000);

    // Initial connection attempt
    setTimeout(() => this.manageConnection(), 1000);
  }

  private async manageConnection(): Promise<void> {
    if (!this.config || !this.status.isRunning) return;

    // If already connected and syncing, don't interfere
    if (this.status.syncStatus.connected && this.status.syncStatus.syncing) {
      return;
    }

    // Determine best connection option
    const bestServer = this.getBestServer();
    
    if (!bestServer) {
      // No servers available, ensure we're disconnected
      if (this.status.currentConnection.server) {
        await this.disconnectCurrentSync();
      }
      return;
    }

    // If already connected to the best server, don't reconnect
    if (this.status.currentConnection.server?.url === bestServer.url) {
      return;
    }

    // Switch to the best server
    console.log(`🔄 [HybridSync] Switching to ${bestServer.syncType} server: ${bestServer.url}`);
    await this.connectToServer(bestServer);
  }

  private getBestServer(): LeaderSyncServer | null {
    // 👑 LEADER-ONLY SYNC: Only connect to verified leader
    if (this.status.currentLeader) {
      console.log(`👑 [LeaderSync] Connecting to verified leader: ${this.status.currentLeader.ip}`);
      return this.status.currentLeader;
    }

    console.log('⚠️ [LeaderSync] No verified leader found - cannot sync');
    return null;
  }

  private async handleLeaderElection(): Promise<void> {
    const leaders = this.status.localServers.filter(s => s.isLeader);
    
    if (leaders.length === 1) {
      // 👑 Single leader found - perfect!
      this.status.currentLeader = leaders[0];
      console.log(`👑 [LeaderSync] Found leader: ${leaders[0].ip}:${leaders[0].port}`);
      
      // If this leader also handles internet sync, register it
      if (this.isLeader && this.config?.deviceRegistration) {
        await this.registerAsInternetLeader();
      }
    } else if (leaders.length > 1) {
      // 🚨 Multiple leaders - choose highest epoch (most recent)
      const validLeader = leaders.sort((a, b) => (b.leaderEpoch || 0) - (a.leaderEpoch || 0))[0];
      this.status.currentLeader = validLeader;
      console.log(`⚠️ [LeaderSync] Multiple leaders detected, choosing highest epoch: ${validLeader.ip}:${validLeader.port}`);
    } else if (leaders.length === 0) {
      // 🗳️ No leader found - elect new one if we're eligible
      await this.attemptLeaderElection();
    }
  }

  private async attemptLeaderElection(): Promise<void> {
    const isElectron = typeof (window as any).electronAPI !== 'undefined';
    
    if (!isElectron) {
      console.log('📱 [LeaderSync] Mobile device - cannot become leader');
      return;
    }

    // 🗳️ Desktop device can attempt leadership
    const myIP = await this.getMyLocalIP();
    const eligibleDevices = this.status.localServers.filter(s => 
      s.ip.startsWith('192.168.') || s.ip.startsWith('10.0.') || s.ip.startsWith('172.16.')
    );

    // Add ourselves to the eligible list
    eligibleDevices.push({
      ip: myIP || '127.0.0.1',
      port: 5984,
      url: `http://${myIP || '127.0.0.1'}:5984`,
      syncType: 'local',
      isLeader: false,
      leaderEpoch: 0
    });

    // 👑 Elect leader based on lowest IP (deterministic)
    const sortedDevices = eligibleDevices.sort((a, b) => {
      const aIP = a.ip.split('.').map(n => parseInt(n).toString().padStart(3, '0')).join('');
      const bIP = b.ip.split('.').map(n => parseInt(n).toString().padStart(3, '0')).join('');
      return aIP.localeCompare(bIP);
    });

    const shouldBeLeader = sortedDevices[0]?.ip === myIP;
    
    if (shouldBeLeader) {
      console.log('🎉 [LeaderSync] Elected as new leader!');
      await this.becomeLeader();
    } else {
      console.log(`👥 [LeaderSync] Not elected (leader should be: ${sortedDevices[0]?.ip})`);
    }
  }

  private async becomeLeader(): Promise<void> {
    this.isLeader = true;
    this.leaderEpoch = Date.now();
    
    // 👑 Set CouchDB vendor field to indicate leadership
    await this.setCouchDBLeaderStatus(true);
    
    // 🌐 Register as internet leader if configured
    if (this.config?.deviceRegistration) {
      await this.registerAsInternetLeader();
    }
    
    console.log(`👑 [LeaderSync] Now serving as leader (epoch: ${this.leaderEpoch})`);
  }

  private async setCouchDBLeaderStatus(isLeader: boolean): Promise<void> {
    try {
      // This would require access to local CouchDB instance
      // For now, we'll implement this by setting a vendor field
      const response = await fetch('http://127.0.0.1:5984/', {
        method: 'GET'
      });
      
      if (response.ok) {
        console.log(`👑 [LeaderSync] ${isLeader ? 'Set' : 'Unset'} leader status in CouchDB`);
        // Note: In real implementation, we'd need to modify CouchDB config
        // For now, leader status is tracked in memory
      }
    } catch (error) {
      console.warn('⚠️ [LeaderSync] Could not update CouchDB leader status:', error);
    }
  }

  private async registerAsInternetLeader(): Promise<void> {
    if (!this.config?.deviceRegistration) return;
    
    try {
      const response = await fetch(`${this.config.vpsBaseUrl}/api/sync/register-leader`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceId: this.config.deviceId,
          restaurantId: getCurrentRestaurantId(),
          leaderEpoch: this.leaderEpoch,
          couchdbPort: this.config.deviceRegistration.couchdbPort || 5984
        })
      });

      if (response.ok) {
        console.log('🌐 [LeaderSync] Registered as internet leader');
        this.status.internetRegistered = true;
      } else {
        throw new Error(`Registration failed: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ [LeaderSync] Internet leader registration failed:', error);
    }
  }

  private async getMyLocalIP(): Promise<string | null> {
    try {
      if (typeof (window as any).electronAPI?.getNetworkInfo === 'function') {
        const networkInfo = await (window as any).electronAPI.getNetworkInfo();
        return networkInfo?.localIP || null;
      }
    } catch (error) {
      console.warn('Could not detect local IP:', error);
    }
    return null;
  }

  private async connectToServer(server: LeaderSyncServer): Promise<void> {
    // Disconnect current connection first
    await this.disconnectCurrentSync();

    try {
      let success = false;

      if (server.syncType === 'local') {
        success = await nativeSyncService.startSync(server, {
          live: true,
          retry: true
        });
        
        if (success) {
          this.status.localConnected = true;
        }
      } else if (server.syncType === 'internet') {
        const peer = this.status.internetPeers.find(p => p.peerId === server.peerId);
        if (peer) {
          success = await nativeSyncService.startSync(server, {
            live: true,
            retry: true,
            isInternetProxy: true,
            authToken: this.config!.authToken
          });
          
          if (success) {
            this.status.internetConnected = true;
          }
        }
      }

      if (success) {
        this.status.currentConnection = { server, type: server.syncType };
        this.status.error = null;
        console.log(`✅ [HybridSync] Connected to ${server.syncType} server: ${server.url}`);
      } else {
        console.error(`❌ [HybridSync] Failed to connect to ${server.syncType} server: ${server.url}`);
      }
    } catch (error: any) {
      console.error(`❌ [HybridSync] Connection error:`, error);
      this.status.error = error.message;
    }

    this.emitStatusUpdate();
  }

  private async disconnectCurrentSync(): Promise<void> {
    if (this.status.currentConnection.server) {
      await nativeSyncService.stopSync();
      this.status.currentConnection = { server: null, type: null };
      this.status.localConnected = false;
      this.status.internetConnected = false;
    }
  }

  private emitStatusUpdate(): void {
    this.listeners.forEach(listener => listener({ ...this.status }));
  }

  // Public API
  onStatusChange(listener: (status: LeaderStatus) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) this.listeners.splice(index, 1);
    };
  }

  getStatus(): LeaderStatus {
    return { ...this.status };
  }

  async discover(): Promise<void> {
    await Promise.all([
      this.performLocalDiscovery(),
      this.performInternetDiscovery()
    ]);
  }

  updateConfig(config: Partial<LeaderConfig>): void {
    if (!this.config) return;
    
    this.config = { ...this.config, ...config };
    
    // Update service configurations
    if (config.vpsBaseUrl || config.authToken || config.deviceId || config.deviceType) {
      internetDiscoveryService.configure({
        vpsBaseUrl: this.config.vpsBaseUrl,
        authToken: this.config.authToken,
        deviceId: this.config.deviceId,
        deviceType: this.config.deviceType
      });

      internetSyncService.configure({
        vpsBaseUrl: this.config.vpsBaseUrl,
        authToken: this.config.authToken,
        deviceId: this.config.deviceId
      });
    }

    // Restart timers if running
    if (this.status.isRunning) {
      this.restartTimers();
    }
  }

  private restartTimers(): void {
    // Clear existing timers
    if (this.localDiscoveryTimer) clearInterval(this.localDiscoveryTimer);
    if (this.internetDiscoveryTimer) clearInterval(this.internetDiscoveryTimer);
    
    // Restart discovery
    this.startPeriodicDiscovery();
  }

  // Utility methods
  isConnected(): boolean {
    return this.status.syncStatus.connected;
  }

  isSyncing(): boolean {
    return this.status.syncStatus.syncing;
  }

  getCurrentConnectionType(): 'local' | 'internet' | null {
    return this.status.currentConnection.type;
  }

  hasLocalServers(): boolean {
    return this.status.localServers.length > 0;
  }

  hasInternetPeers(): boolean {
    return this.status.internetPeers.length > 0;
  }
}

export const leaderSyncService = new LeaderSyncService();
export type { LeaderConfig, LeaderStatus, LeaderSyncServer };