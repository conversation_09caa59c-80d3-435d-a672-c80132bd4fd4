'use client';

export interface BackupMetadata {
  timestamp: number;
  date: string;
  dbName: string;
  size: number;
  docCount: number;
}

export interface BackupStatus {
  isRunning: boolean;
  lastBackup?: number;
  googleDriveConnected: boolean;
}

export class ClientBackupService {
  private isElectron(): boolean {
    return typeof window !== 'undefined' && !!(window as any).electronAPI;
  }

  private async callElectronAPI(channel: string, ...args: any[]): Promise<any> {
    if (!this.isElectron()) {
      throw new Error('Backup functionality is only available in the desktop application');
    }

    const electronAPI = (window as any).electronAPI;
    if (!electronAPI?.ipcRenderer?.invoke) {
      throw new Error('Electron API not available');
    }

    const result = await electronAPI.ipcRenderer.invoke(channel, ...args);
    
    if (!result.ok) {
      throw new Error(result.error || `Failed to execute ${channel}`);
    }
    
    return result.data;
  }

  async createBackup(restaurantId: string): Promise<{ success: boolean; error?: string; metadata?: BackupMetadata }> {
    try {
      const metadata = await this.callElectronAPI('backup:create', restaurantId);
      console.log(`✅ Filesystem backup created: ${metadata.docCount} docs, ${metadata.size} bytes`);
      
      return { success: true, metadata };

    } catch (error) {
      console.error('❌ Filesystem backup failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async listLocalBackups(): Promise<BackupMetadata[]> {
    try {
      const backups = await this.callElectronAPI('backup:list-local');
      return backups.sort((a: BackupMetadata, b: BackupMetadata) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error('❌ Failed to list filesystem backups:', error);
      if (error instanceof Error && error.message.includes('only available in the desktop application')) {
        return []; // Gracefully return empty array for web
      }
      return [];
    }
  }

  async restoreFromBackup(backupTimestamp: number, restaurantId: string): Promise<{ success: boolean; error?: string }> {
    try {
      await this.callElectronAPI('backup:restore-local', backupTimestamp, restaurantId);
      console.log(`✅ Database restored from filesystem backup: ${backupTimestamp}`);
      return { success: true };

    } catch (error) {
      console.error('❌ Filesystem restore failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async getStatus(): Promise<BackupStatus> {
    try {
      const status = await this.callElectronAPI('backup:get-status');
      return {
        isRunning: status.isRunning || false,
        lastBackup: status.lastBackup,
        googleDriveConnected: false // Focusing on local filesystem backups only
      };
    } catch (error) {
      console.error('❌ Failed to get backup status:', error);
      return {
        isRunning: false,
        googleDriveConnected: false
      };
    }
  }
}

export const clientBackupService = new ClientBackupService();