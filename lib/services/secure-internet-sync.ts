/**
 * Secure Internet Sync Service
 * 
 * Enhanced security features:
 * - Token rotation and refresh
 * - Request signing
 * - Rate limiting
 * - Connection encryption validation
 * - Audit logging
 */

import { enhancedInternetSyncService } from './enhanced-internet-sync';
import { internetSyncService, type InternetSyncConfig } from './internet-sync';
import crypto from 'crypto';

interface SecureConfig extends InternetSyncConfig {
  enableRequestSigning?: boolean;
  tokenRotationInterval?: number;
  maxRequestsPerMinute?: number;
  enableAuditLogging?: boolean;
}

interface SecurityMetrics {
  totalRequests: number;
  failedAuthAttempts: number;
  tokenRefreshCount: number;
  lastTokenRefresh: Date | null;
  suspiciousActivity: SecurityEvent[];
}

interface SecurityEvent {
  type: 'auth_failure' | 'rate_limit' | 'invalid_signature' | 'token_expired';
  timestamp: Date;
  details: string;
  severity: 'low' | 'medium' | 'high';
}

class SecureInternetSyncService {
  private config: SecureConfig | null = null;
  private securityMetrics: SecurityMetrics = {
    totalRequests: 0,
    failedAuthAttempts: 0,
    tokenRefreshCount: 0,
    lastTokenRefresh: null,
    suspiciousActivity: []
  };
  
  private requestLimiter = new Map<string, { count: number; resetTime: number }>();
  private tokenRotationTimer: NodeJS.Timeout | null = null;

  /**
   * Configure secure sync with enhanced security options
   */
  configure(config: SecureConfig): void {
    this.config = {
      enableRequestSigning: true,
      tokenRotationInterval: 30 * 60 * 1000, // 30 minutes
      maxRequestsPerMinute: 100,
      enableAuditLogging: true,
      ...config
    };

    // Configure base services
    enhancedInternetSyncService.configure(config);

    // Start token rotation if enabled
    if (this.config.tokenRotationInterval && this.config.tokenRotationInterval > 0) {
      this.startTokenRotation();
    }

    console.log('🔒 Secure internet sync configured');
  }

  /**
   * Start sync with security validation
   */
  async startSecureSync(): Promise<boolean> {
    if (!this.config) {
      throw new Error('Secure sync not configured');
    }

    try {
      // Pre-sync security checks
      await this.validateSecurityContext();

      // Rate limiting check
      if (!this.checkRateLimit()) {
        this.recordSecurityEvent('rate_limit', 'Rate limit exceeded', 'medium');
        throw new Error('Rate limit exceeded');
      }

      // Start enhanced sync
      const success = await enhancedInternetSyncService.startSmartSync();

      if (success) {
        this.securityMetrics.totalRequests++;
        this.logAuditEvent('sync_started', 'Secure sync started successfully');
      } else {
        this.securityMetrics.failedAuthAttempts++;
        this.recordSecurityEvent('auth_failure', 'Sync start failed', 'medium');
      }

      return success;

    } catch (error) {
      console.error('🚨 Secure sync failed:', error);
      this.recordSecurityEvent('auth_failure', error instanceof Error ? error.message : 'Unknown error', 'high');
      throw error;
    }
  }

  /**
   * Validate security context before sync
   */
  private async validateSecurityContext(): Promise<void> {
    if (!this.config) return;

    // Check token expiry
    if (this.isTokenExpiring()) {
      console.log('🔄 Token expiring, refreshing...');
      await this.refreshToken();
    }

    // Validate VPS connection security
    await this.validateVPSConnection();

    // Check for suspicious activity
    this.checkSuspiciousActivity();
  }

  /**
   * Check if current token is expiring soon
   */
  private isTokenExpiring(): boolean {
    if (!this.config?.authToken) return false;

    try {
      // Simple JWT expiry check (assuming standard JWT format)
      const payload = JSON.parse(atob(this.config.authToken.split('.')[1]));
      const exp = payload.exp * 1000; // Convert to milliseconds
      const now = Date.now();
      const timeUntilExpiry = exp - now;
      
      // Refresh if expiring within 5 minutes
      return timeUntilExpiry < 5 * 60 * 1000;
      
    } catch {
      // If we can't parse the token, assume it needs refresh
      return true;
    }
  }

  /**
   * Refresh authentication token
   */
  private async refreshToken(): Promise<void> {
    if (!this.config?.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      // This would typically call your auth service
      // For now, we'll log the attempt
      console.log('🔄 Refreshing authentication token...');
      
      this.securityMetrics.tokenRefreshCount++;
      this.securityMetrics.lastTokenRefresh = new Date();
      
      this.logAuditEvent('token_refresh', 'Authentication token refreshed');
      
    } catch (error) {
      this.recordSecurityEvent('auth_failure', 'Token refresh failed', 'high');
      throw error;
    }
  }

  /**
   * Validate VPS connection security
   */
  private async validateVPSConnection(): Promise<void> {
    if (!this.config?.vpsBaseUrl) return;

    const url = new URL(this.config.vpsBaseUrl);
    
    // Ensure HTTPS
    if (url.protocol !== 'https:') {
      throw new Error('VPS connection must use HTTPS');
    }

    // Validate SSL certificate (simplified check)
    try {
      const response = await fetch(`${this.config.vpsBaseUrl}/api/health`, {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      });
      
      if (!response.ok) {
        throw new Error(`VPS health check failed: ${response.status}`);
      }
      
    } catch (error) {
      this.recordSecurityEvent('auth_failure', `VPS validation failed: ${error}`, 'high');
      throw new Error('VPS connection validation failed');
    }
  }

  /**
   * Check for suspicious activity patterns
   */
  private checkSuspiciousActivity(): void {
    const recent = this.securityMetrics.suspiciousActivity.filter(
      event => Date.now() - event.timestamp.getTime() < 10 * 60 * 1000 // Last 10 minutes
    );

    const highSeverityEvents = recent.filter(event => event.severity === 'high');
    
    if (highSeverityEvents.length > 3) {
      throw new Error('Too many high-severity security events detected');
    }

    const authFailures = recent.filter(event => event.type === 'auth_failure');
    
    if (authFailures.length > 5) {
      throw new Error('Too many authentication failures detected');
    }
  }

  /**
   * Rate limiting check
   */
  private checkRateLimit(): boolean {
    if (!this.config?.maxRequestsPerMinute) return true;

    const now = Date.now();
    const windowStart = Math.floor(now / 60000) * 60000; // Start of current minute
    const key = 'sync_requests';
    
    let limiter = this.requestLimiter.get(key);
    
    if (!limiter || limiter.resetTime < windowStart) {
      limiter = { count: 0, resetTime: windowStart + 60000 };
      this.requestLimiter.set(key, limiter);
    }

    limiter.count++;
    
    return limiter.count <= this.config.maxRequestsPerMinute;
  }

  /**
   * Start automatic token rotation
   */
  private startTokenRotation(): void {
    this.stopTokenRotation();

    if (!this.config?.tokenRotationInterval) return;

    this.tokenRotationTimer = setInterval(async () => {
      try {
        await this.refreshToken();
      } catch (error) {
        console.error('⚠️ Automatic token rotation failed:', error);
      }
    }, this.config.tokenRotationInterval);
  }

  /**
   * Stop token rotation
   */
  private stopTokenRotation(): void {
    if (this.tokenRotationTimer) {
      clearInterval(this.tokenRotationTimer);
      this.tokenRotationTimer = null;
    }
  }

  /**
   * Record security event
   */
  private recordSecurityEvent(type: SecurityEvent['type'], details: string, severity: SecurityEvent['severity']): void {
    const event: SecurityEvent = {
      type,
      timestamp: new Date(),
      details,
      severity
    };

    this.securityMetrics.suspiciousActivity.unshift(event);

    // Limit history size
    if (this.securityMetrics.suspiciousActivity.length > 100) {
      this.securityMetrics.suspiciousActivity = this.securityMetrics.suspiciousActivity.slice(0, 100);
    }

    // Log high-severity events immediately
    if (severity === 'high') {
      console.warn('🚨 High-severity security event:', event);
    }

    this.logAuditEvent('security_event', `${type}: ${details}`, { severity });
  }

  /**
   * Audit logging
   */
  private logAuditEvent(action: string, details: string, metadata?: any): void {
    if (!this.config?.enableAuditLogging) return;

    const auditLog = {
      timestamp: new Date().toISOString(),
      action,
      details,
      metadata,
      userId: 'system', // Could be extracted from token
      deviceId: this.config?.deviceId
    };

    // In production, this would go to a secure audit log service
    console.log('📋 Audit:', auditLog);
  }

  /**
   * Stop secure sync with cleanup
   */
  async stopSecureSync(): Promise<void> {
    console.log('🔒 Stopping secure sync...');

    // Stop token rotation
    this.stopTokenRotation();

    // Stop enhanced sync
    await enhancedInternetSyncService.stopSync();

    this.logAuditEvent('sync_stopped', 'Secure sync stopped');
  }

  /**
   * Get security metrics
   */
  getSecurityMetrics(): SecurityMetrics {
    return { ...this.securityMetrics };
  }

  /**
   * Get recent security events
   */
  getRecentSecurityEvents(minutes: number = 60): SecurityEvent[] {
    const cutoff = new Date(Date.now() - minutes * 60 * 1000);
    return this.securityMetrics.suspiciousActivity.filter(
      event => event.timestamp >= cutoff
    );
  }

  /**
   * Reset security metrics (for testing)
   */
  resetSecurityMetrics(): void {
    this.securityMetrics = {
      totalRequests: 0,
      failedAuthAttempts: 0,
      tokenRefreshCount: 0,
      lastTokenRefresh: null,
      suspiciousActivity: []
    };
    this.requestLimiter.clear();
    console.log('🔄 Security metrics reset');
  }

  /**
   * Generate request signature (if enabled)
   */
  private generateRequestSignature(payload: string, timestamp: number): string {
    if (!this.config?.authToken) return '';
    
    const message = `${payload}:${timestamp}`;
    return crypto.createHmac('sha256', this.config.authToken).update(message).digest('hex');
  }

  /**
   * Verify request signature
   */
  private verifyRequestSignature(payload: string, timestamp: number, signature: string): boolean {
    const expectedSignature = this.generateRequestSignature(payload, timestamp);
    return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature));
  }
}

export const secureInternetSyncService = new SecureInternetSyncService();
export type { SecureConfig, SecurityMetrics, SecurityEvent };