import { BackupService } from './backup-service';
import { SimpleGoogleDriveService } from './google-drive-simple';

export interface BackupSchedulerConfig {
  restaurantId: string;
  googleDriveConfig?: {
    accessToken: string;
    refreshToken: string;
    clientId: string;
    clientSecret: string;
  };
  autoUploadToCloud: boolean;
}

export class BackupScheduler {
  private backupService: BackupService;
  private driveService: SimpleGoogleDriveService;
  private config: BackupSchedulerConfig | null = null;
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;

  constructor() {
    this.backupService = new BackupService();
    this.driveService = new SimpleGoogleDriveService();
  }

  async initialize(config: BackupSchedulerConfig): Promise<boolean> {
    this.config = config;

    // Initialize Google Drive if configured
    if (config.googleDriveConfig && config.autoUploadToCloud) {
      const driveInitialized = await this.driveService.initialize(config.googleDriveConfig);
      if (!driveInitialized) {
        console.warn('⚠️ Google Drive initialization failed, backups will be local only');
      }
    }

    return true;
  }

  start(): void {
    if (this.isRunning) return;

    this.isRunning = true;
    console.log('🔄 Backup scheduler started');

    // Check immediately on start
    this.checkAndBackup();

    // Then check every 2 hours
    this.intervalId = setInterval(() => {
      this.checkAndBackup();
    }, 2 * 60 * 60 * 1000); // 2 hours
  }

  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isRunning = false;
    console.log('⏹️ Backup scheduler stopped');
  }

  private async checkAndBackup(): Promise<void> {
    if (!this.config) return;

    try {
      const needsBackup = await this.backupService.isBackupNeeded();
      if (!needsBackup) {
        console.log('ℹ️ Backup not needed yet');
        return;
      }

      const now = new Date();
      const hour = now.getHours();

      // Preferred time: 3 AM
      // Fallback times: 9 AM, 2 PM, 8 PM
      const preferredHours = [3, 9, 14, 20];
      const isPreferredTime = preferredHours.includes(hour);

      if (!isPreferredTime) {
        console.log(`ℹ️ Not optimal backup time (${hour}:xx), waiting for preferred window`);
        return;
      }

      console.log(`⏰ Starting backup at ${now.toLocaleTimeString()}`);
      await this.performBackup();

    } catch (error) {
      console.error('❌ Backup check failed:', error);
    }
  }

  async performBackup(): Promise<{ success: boolean; error?: string }> {
    if (!this.config) {
      return { success: false, error: 'Scheduler not configured' };
    }

    try {
      // Create local backup
      const backupResult = await this.backupService.createBackup(this.config.restaurantId);
      
      if (!backupResult.success) {
        return { success: false, error: backupResult.error };
      }

      // Clean up old local backups
      await this.backupService.cleanupOldBackups();

      // Upload to Google Drive if configured
      if (this.config.autoUploadToCloud && this.driveService.isInitialized() && backupResult.metadata) {
        await this.uploadToCloud(backupResult.metadata);
      }

      console.log('✅ Backup cycle completed successfully');
      return { success: true };

    } catch (error) {
      console.error('❌ Backup failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  private async uploadToCloud(metadata: any): Promise<void> {
    try {
      const fs = await import('fs/promises');
      const path = await import('path');
      const { app } = await import('electron');

      const userDataPath = app.getPath('userData');
      const backupDir = path.join(userDataPath, 'backups');
      const fileName = `${metadata.dbName}_${metadata.date}_${metadata.timestamp}.json`;
      const filePath = path.join(backupDir, fileName);

      const backupData = await fs.readFile(filePath, 'utf8');
      
      const uploadResult = await this.driveService.uploadBackup(
        backupData,
        fileName,
        this.config!.restaurantId
      );

      if (uploadResult.success) {
        console.log('☁️ Backup uploaded to Google Drive successfully');
        await this.cleanupCloudBackups();
      } else {
        console.warn('⚠️ Failed to upload backup to Google Drive:', uploadResult.error);
      }

    } catch (error) {
      console.error('❌ Cloud upload failed:', error);
    }
  }

  private async cleanupCloudBackups(): Promise<void> {
    try {
      const listResult = await this.driveService.listBackups(this.config!.restaurantId);
      
      if (!listResult.success || !listResult.files) return;

      const now = Date.now();
      const filesToDelete = listResult.files.filter(file => {
        const createdTime = new Date(file.createdTime).getTime();
        const ageInDays = (now - createdTime) / (24 * 60 * 60 * 1000);
        return ageInDays > 30; // Delete files older than 30 days
      });

      for (const file of filesToDelete) {
        await this.driveService.deleteBackup(file.id);
        console.log(`🗑️ Deleted old cloud backup: ${file.name}`);
      }

    } catch (error) {
      console.error('❌ Cloud cleanup failed:', error);
    }
  }

  async forceBackup(): Promise<{ success: boolean; error?: string }> {
    console.log('🔧 Force backup triggered');
    return await this.performBackup();
  }

  async getStatus(): Promise<{
    isRunning: boolean;
    lastBackup?: number;
    nextCheck?: number;
    googleDriveConnected: boolean;
  }> {
    const context = await this.backupService.getContext();
    
    return {
      isRunning: this.isRunning,
      lastBackup: context.lastBackupTime || undefined,
      nextCheck: this.intervalId ? Date.now() + (2 * 60 * 60 * 1000) : undefined,
      googleDriveConnected: this.driveService.isInitialized()
    };
  }

  getBackupService(): BackupService {
    return this.backupService;
  }

  getDriveService(): SimpleGoogleDriveService {
    return this.driveService;
  }
}