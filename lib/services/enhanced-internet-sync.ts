/**
 * Enhanced Internet Sync Service
 * 
 * Improvements over basic internet sync:
 * - Intelligent peer selection
 * - Connection health monitoring
 * - Automatic failover
 * - Resource cleanup
 * - Performance optimization
 */

import { internetSyncService, type InternetSyncConfig, type DiscoveredPeer } from './internet-sync';
import { mainDbInstance } from '@/lib/db/v4/core/db-instance';

interface PeerHealth {
  peerId: string;
  responseTime: number;
  successRate: number;
  lastSuccess: Date;
  consecutiveFailures: number;
  isHealthy: boolean;
}

interface EnhancedSyncStatus {
  connected: boolean;
  syncing: boolean;
  activePeer: DiscoveredPeer | null;
  peerHealth: Map<string, PeerHealth>;
  connectionHistory: ConnectionAttempt[];
  lastSync?: Date;
  error?: string;
  docsReceived: number;
  docsSent: number;
}

interface ConnectionAttempt {
  peerId: string;
  timestamp: Date;
  success: boolean;
  responseTime?: number;
  error?: string;
}

class EnhancedInternetSyncService {
  private status: EnhancedSyncStatus = {
    connected: false,
    syncing: false,
    activePeer: null,
    peerHealth: new Map(),
    connectionHistory: [],
    docsReceived: 0,
    docsSent: 0
  };

  private listeners: ((status: EnhancedSyncStatus) => void)[] = [];
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private connectionCleanupTimeout: NodeJS.Timeout | null = null;
  private maxConnectionHistory = 50;
  private maxConsecutiveFailures = 3;

  /**
   * Smart peer selection based on health metrics
   */
  private selectBestPeer(peers: DiscoveredPeer[]): DiscoveredPeer | null {
    if (peers.length === 0) return null;

    // Filter to only desktop peers (mobile can't host CouchDB)
    const desktopPeers = peers.filter(p => p.deviceType === 'desktop');
    if (desktopPeers.length === 0) return null;

    // Score peers based on health metrics
    const scoredPeers = desktopPeers.map(peer => {
      const health = this.status.peerHealth.get(peer.id);
      
      let score = 100; // Base score
      
      if (health) {
        // Factor in success rate (0-100)
        score *= (health.successRate / 100);
        
        // Penalize high response times (lower is better)
        score *= Math.max(0.1, (5000 - health.responseTime) / 5000);
        
        // Heavily penalize consecutive failures
        score *= Math.pow(0.5, health.consecutiveFailures);
        
        // Boost recently successful peers
        const timeSinceSuccess = Date.now() - health.lastSuccess.getTime();
        const recentBoost = Math.max(0.5, (300000 - timeSinceSuccess) / 300000); // 5 min window
        score *= (1 + recentBoost);
      }
      
      // Prefer more recently seen peers
      const timeSinceLastSeen = Date.now() - new Date(peer.lastSeen).getTime();
      const recencyBoost = Math.max(0.1, (600000 - timeSinceLastSeen) / 600000); // 10 min window
      score *= recencyBoost;

      return { peer, score };
    });

    // Sort by score and return best peer
    scoredPeers.sort((a, b) => b.score - a.score);
    
    console.log('🎯 Peer selection scores:', scoredPeers.map(p => ({
      id: p.peer.id,
      score: Math.round(p.score),
      hostname: p.peer.hostname
    })));

    return scoredPeers[0]?.peer || null;
  }

  /**
   * Start sync with intelligent peer selection and health monitoring
   */
  async startSmartSync(): Promise<boolean> {
    try {
      console.log('🧠 Starting smart internet sync...');

      // Discover peers
      const peers = await internetSyncService.discoverPeers();
      
      if (peers.length === 0) {
        this.status.error = 'No peers available for sync';
        this.emitStatusUpdate();
        return false;
      }

      // Select best peer using health metrics
      const bestPeer = this.selectBestPeer(peers);
      
      if (!bestPeer) {
        this.status.error = 'No healthy desktop peers found';
        this.emitStatusUpdate();
        return false;
      }

      console.log(`🎯 Selected best peer: ${bestPeer.hostname} (${bestPeer.id})`);

      // Attempt connection with timing
      const startTime = Date.now();
      const success = await internetSyncService.startSync(bestPeer);
      const responseTime = Date.now() - startTime;

      // Record connection attempt
      this.recordConnectionAttempt(bestPeer.id, success, responseTime);

      if (success) {
        this.status.connected = true;
        this.status.syncing = true;
        this.status.activePeer = bestPeer;
        this.status.error = undefined;
        
        // Start health monitoring
        this.startHealthMonitoring();
        
        // Set connection cleanup timeout
        this.scheduleConnectionCleanup();
        
        console.log('✅ Smart sync established successfully');
      } else {
        this.status.error = 'Failed to establish sync connection';
      }

      this.emitStatusUpdate();
      return success;

    } catch (error) {
      console.error('❌ Smart sync failed:', error);
      this.status.error = error instanceof Error ? error.message : 'Unknown error';
      this.emitStatusUpdate();
      return false;
    }
  }

  /**
   * Stop sync with proper resource cleanup
   */
  async stopSync(): Promise<void> {
    console.log('🛑 Stopping enhanced internet sync...');

    // Stop base sync service
    await internetSyncService.stopSync();

    // Clean up health monitoring
    this.stopHealthMonitoring();

    // Clean up connection timeout
    if (this.connectionCleanupTimeout) {
      clearTimeout(this.connectionCleanupTimeout);
      this.connectionCleanupTimeout = null;
    }

    // Reset status
    this.status.connected = false;
    this.status.syncing = false;
    this.status.activePeer = null;
    this.status.error = undefined;

    this.emitStatusUpdate();
    console.log('✅ Enhanced sync stopped and cleaned up');
  }

  /**
   * Record connection attempt for health tracking
   */
  private recordConnectionAttempt(peerId: string, success: boolean, responseTime?: number, error?: string): void {
    // Add to connection history
    const attempt: ConnectionAttempt = {
      peerId,
      timestamp: new Date(),
      success,
      responseTime,
      error
    };

    this.status.connectionHistory.unshift(attempt);

    // Limit history size
    if (this.status.connectionHistory.length > this.maxConnectionHistory) {
      this.status.connectionHistory = this.status.connectionHistory.slice(0, this.maxConnectionHistory);
    }

    // Update peer health
    this.updatePeerHealth(peerId, success, responseTime);
  }

  /**
   * Update peer health metrics
   */
  private updatePeerHealth(peerId: string, success: boolean, responseTime?: number): void {
    let health = this.status.peerHealth.get(peerId);

    if (!health) {
      health = {
        peerId,
        responseTime: responseTime || 0,
        successRate: success ? 100 : 0,
        lastSuccess: success ? new Date() : new Date(0),
        consecutiveFailures: success ? 0 : 1,
        isHealthy: success
      };
    } else {
      // Update response time (weighted average)
      if (responseTime) {
        health.responseTime = (health.responseTime * 0.7) + (responseTime * 0.3);
      }

      // Update success rate (based on last 10 attempts)
      const recentAttempts = this.status.connectionHistory
        .filter(a => a.peerId === peerId)
        .slice(0, 10);
      
      if (recentAttempts.length > 0) {
        const successCount = recentAttempts.filter(a => a.success).length;
        health.successRate = (successCount / recentAttempts.length) * 100;
      }

      // Update consecutive failures
      if (success) {
        health.consecutiveFailures = 0;
        health.lastSuccess = new Date();
      } else {
        health.consecutiveFailures++;
      }

      // Determine if peer is healthy
      health.isHealthy = health.consecutiveFailures < this.maxConsecutiveFailures && 
                       health.successRate > 50;
    }

    this.status.peerHealth.set(peerId, health);
  }

  /**
   * Start health monitoring
   */
  private startHealthMonitoring(): void {
    this.stopHealthMonitoring();

    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, 30000); // Check every 30 seconds
  }

  /**
   * Stop health monitoring
   */
  private stopHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }

  /**
   * Perform health check on active connection
   */
  private async performHealthCheck(): Promise<void> {
    if (!this.status.activePeer || !this.status.connected) return;

    try {
      // Simple ping test via sync service status
      const syncStatus = internetSyncService.getStatus();
      
      if (syncStatus.error) {
        console.warn('⚠️ Health check detected error:', syncStatus.error);
        this.recordConnectionAttempt(this.status.activePeer.id, false, undefined, syncStatus.error);
        
        // If too many failures, attempt to switch peers
        const health = this.status.peerHealth.get(this.status.activePeer.id);
        if (health && !health.isHealthy) {
          console.log('🔄 Peer unhealthy, attempting to switch...');
          await this.attemptPeerSwitch();
        }
      } else if (syncStatus.connected) {
        this.recordConnectionAttempt(this.status.activePeer.id, true);
      }

    } catch (error) {
      console.error('❌ Health check failed:', error);
    }
  }

  /**
   * Attempt to switch to a healthier peer
   */
  private async attemptPeerSwitch(): Promise<void> {
    try {
      console.log('🔄 Attempting peer switch...');
      
      // Stop current connection
      await internetSyncService.stopSync();
      
      // Try to start with a new peer
      const success = await this.startSmartSync();
      
      if (!success) {
        console.warn('⚠️ Peer switch failed, no healthy alternatives');
      }
      
    } catch (error) {
      console.error('❌ Peer switch failed:', error);
    }
  }

  /**
   * Schedule connection cleanup to prevent resource leaks
   */
  private scheduleConnectionCleanup(): void {
    if (this.connectionCleanupTimeout) {
      clearTimeout(this.connectionCleanupTimeout);
    }

    // Auto-disconnect after 1 hour to refresh connection
    this.connectionCleanupTimeout = setTimeout(async () => {
      console.log('🧹 Auto-refreshing connection...');
      await this.stopSync();
      
      // Attempt to reconnect after brief delay
      setTimeout(() => {
        this.startSmartSync();
      }, 5000);
      
    }, 60 * 60 * 1000); // 1 hour
  }

  /**
   * Get enhanced status information
   */
  getEnhancedStatus(): EnhancedSyncStatus {
    return { ...this.status };
  }

  /**
   * Get peer health summary
   */
  getPeerHealthSummary(): Array<{peerId: string, health: PeerHealth}> {
    return Array.from(this.status.peerHealth.entries()).map(([peerId, health]) => ({
      peerId,
      health
    }));
  }

  /**
   * Status change listener
   */
  onStatusChange(listener: (status: EnhancedSyncStatus) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) this.listeners.splice(index, 1);
    };
  }

  private emitStatusUpdate(): void {
    this.listeners.forEach(listener => listener({ ...this.status }));
  }

  /**
   * Configure the enhanced sync service
   */
  configure(config: InternetSyncConfig): void {
    internetSyncService.configure(config);
  }

  /**
   * Force peer health reset (useful for testing)
   */
  resetPeerHealth(): void {
    this.status.peerHealth.clear();
    this.status.connectionHistory = [];
    console.log('🔄 Peer health metrics reset');
  }
}

export const enhancedInternetSyncService = new EnhancedInternetSyncService();
export type { EnhancedSyncStatus, PeerHealth, ConnectionAttempt };