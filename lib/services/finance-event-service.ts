'use client';

import { getAllCashTransactions, getAllOrders } from '@/lib/db/v4';
import { databaseV4 } from '@/lib/db/v4/core/db-instance';
import { FinanceEvent } from '@/app/components/finance/EventBasedFinanceHistory';

/**
 * Get all collection events from database
 */
async function getCollectionEvents(): Promise<any[]> {
  try {
    console.log('[getCollectionEvents] Fetching collection events...');
    
    const result = await databaseV4.findDocs({
      selector: {
        type: 'collection_event'
      }
    });

    console.log('[getCollectionEvents] Found collection events:', result.docs?.length || 0);
    
    if (result.docs?.length) {
      console.log('[getCollectionEvents] Sample collection event:', result.docs[0]);
    }

    return (result.docs || []).sort((a: any, b: any) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  } catch (error) {
    console.error('[getCollectionEvents] Error:', error);
    return [];
  }
}

/**
 * Service to convert current transaction data to event-based finance history
 */

async function getCaisseCalculations(): Promise<any[]> {
  try {
    const result = await databaseV4.findDocs({ selector: { type: 'caisse_calculation' } });
    return (result.docs || []).sort((a: any, b: any) => new Date(b.calculatedAt).getTime() - new Date(a.calculatedAt).getTime());
  } catch (e) {
    console.error('[getCaisseCalculations] Error:', e);
    return [];
  }
}

export async function getFinanceEvents(): Promise<FinanceEvent[]> {
  const [cashTransactions, orders, collectionEvents, caisseCalculations] = await Promise.all([
    getAllCashTransactions(),
    getAllOrders(),
    getCollectionEvents(),
    getCaisseCalculations()
  ]);

  const events: FinanceEvent[] = [];

  // 1. Process collection events FIRST (they are events, not transactions)
  for (const collectionEvent of collectionEvents) {
    const grossAmount = collectionEvent.actualAmount || 0;
    const driverFee = collectionEvent.freelancerTariff || 0;
    const netAmount = collectionEvent.netAmountForRestaurant || (grossAmount - driverFee);
    
    // Enhanced collection event with better tracking
    events.push({
      id: collectionEvent._id,
      type: 'collection',
      timestamp: collectionEvent.timestamp,
      netAmount: netAmount, // What actually went to drawer
      performedBy: collectionEvent.collectedBy,
      description: `Collection - ${collectionEvent.driverName} (${collectionEvent.orderCount} orders)${driverFee > 0 ? ` • Driver fee: ${driverFee} DA` : ''}`,
      collection: {
        kind: 'delivery',
        driverName: collectionEvent.driverName,
        orderCount: collectionEvent.orderCount,
        orders: collectionEvent.orders.map((order: any) => ({
          orderId: order.orderId,
          amount: order.expectedAmount,
          customer: order.customer,
          status: 'collected' // All orders in completed collection are collected
        })),
        grossAmount: grossAmount,
        driverFee: driverFee,
        netAmount: netAmount,
        // Additional breakdown for transparency
        collectionBreakdown: {
          totalOrders: collectionEvent.orderCount,
          grossCollected: grossAmount,
          driverFees: driverFee,
          netToRestaurant: netAmount,
          discrepancies: collectionEvent.discrepancies || [],
          manualExpenses: collectionEvent.manualExpenses || []
        }
      }
    });
  }

  // No mock data - collections will appear when processed through enhanced collection system

  // 2. Process all completed orders as sales events with enhanced calculation
  // Include both fully paid and partially paid orders
  const completedOrders = orders.filter(order => 
    order.status === 'completed' && 
    (order.paymentStatus === 'paid' || order.paymentStatus === 'partially_paid')
  );

  for (const order of completedOrders) {
    // 🔧 EDGE CASE FIX: Enhanced amount calculation handling voids, partial payments, and refunds
    let netAmount = order.total || 0;
    
    // Handle voided items - deduct from revenue
    if (order.hasVoids && order.totalVoidedAmount && order.totalVoidedAmount > 0) {
      console.log(`[getFinanceEvents] Order ${order._id} has voided items: ${order.totalVoidedAmount} DA`);
      // Void amount should already be subtracted from order.total, but let's verify
      netAmount = order.total || 0; // Trust the order.total after voids
    }
    
    // Handle partial payments - only count what was actually paid
    if (order.paymentStatus === 'partially_paid' && order.paymentDetails?.amountPaid) {
      netAmount = order.paymentDetails.amountPaid;
      console.log(`[getFinanceEvents] Order ${order._id} partially paid: ${netAmount} of ${order.total} DA`);
    }
    
    // Handle discounts if present (usually already applied to order.total)
    if (order.discount && order.discount > 0) {
      console.log(`[getFinanceEvents] Order ${order._id} has discount: ${order.discount} DA`);
    }
    
    // Handle delivery fees - they should be included in total for restaurant
    if (order.deliveryFee && order.deliveryFee > 0) {
      console.log(`[getFinanceEvents] Order ${order._id} has delivery fee: ${order.deliveryFee} DA`);
    }
    
    // For delivery orders, check if they've been collected
    // If not collected, this might be pending revenue
    const isDeliveryNotCollected = order.orderType === 'delivery' && 
      getCollectionStatus(order) === 'not_collected';
    
    if (isDeliveryNotCollected) {
      console.log(`[getFinanceEvents] Delivery order ${order._id} not yet collected`);
    }
    
    // Skip orders with no actual revenue (fully voided or zero amount)
    if (netAmount <= 0) {
      console.log(`[getFinanceEvents] Skipping order ${order._id} with zero/negative net amount: ${netAmount}`);
      continue;
    }

    events.push({
      id: `sale_${order._id}`,
      type: 'sale',
      timestamp: order.completedAt || order.updatedAt,
      netAmount: netAmount,
      performedBy: order.createdByName || 'System',
      description: `Sale - ${order.customer?.name || 'Customer'}${order.discount ? ` (Discount: ${order.discount} DA)` : ''}${order.hasVoids ? ` (Voided: ${order.totalVoidedAmount} DA)` : ''}${order.paymentStatus === 'partially_paid' ? ' (Partial Payment)' : ''}`,
      sale: {
        orderId: order._id,
        orderType: order.orderType,
        customer: order.customer?.name || order.customer?.phone || 'Customer',
        tableId: order.tableId,
        items: order.items?.map(item => `${item.name} x${item.quantity}`).join(', ') || 'Items',
        paymentMethod: order.paymentMethod || 'cash',
        // Only add collection status for delivery orders
        collectionStatus: order.orderType === 'delivery' 
          ? getCollectionStatus(order) 
          : undefined
      }
    });
  }

  // Add caisse calculation entries as neutral collection events
  for (const calc of caisseCalculations) {
    events.push({
      id: calc._id,
      type: 'collection',
      timestamp: calc.calculatedAt || calc.createdAt,
      netAmount: calc.countedAmount,
      performedBy: calc.calculatedByName || calc.calculatedBy,
      description: 'Caisse calculation',
      collection: {
        kind: 'caisse',
        netAmount: calc.countedAmount,
        validatedBy: calc.calculatedByName,
        comments: calc.notes
      }
    });
  }

  // Backward-compat: legacy delivery collections saved as cash transactions
  const legacyDeliveryCollections = cashTransactions.filter(tx => tx.metadata?.transactionCategory === 'delivery_collection');
  for (const tx of legacyDeliveryCollections) {
    events.push({
      id: tx._id,
      type: 'collection',
      timestamp: tx.time,
      netAmount: tx.amount,
      performedBy: tx.performedBy,
      description: `Collection - ${tx.metadata?.driverName || 'Driver'} (${tx.metadata?.orderCount || 0} orders)`,
      collection: {
        kind: 'delivery',
        driverName: tx.metadata?.driverName,
        orderCount: tx.metadata?.orderCount,
        grossAmount: tx.metadata?.actualAmount || tx.amount,
        driverFee: 0,
        netAmount: tx.amount
      }
    });
  }

  // Backward-compat: new system cash-in linked to collection event
  const netCollectionCashIns = cashTransactions.filter(tx => tx.metadata?.transactionCategory === 'collection_cash_in');
  for (const tx of netCollectionCashIns) {
    events.push({
      id: tx._id,
      type: 'collection',
      timestamp: tx.time,
      netAmount: tx.amount,
      performedBy: tx.performedBy,
      description: `Collection - ${tx.metadata?.driverName || 'Driver'} (${tx.metadata?.orderCount || 0} orders)`,
      collection: {
        kind: 'delivery',
        driverName: tx.metadata?.driverName,
        orderCount: tx.metadata?.orderCount,
        netAmount: tx.amount
      }
    });
  }

  // 3. Process manual transactions (excluding collection-linked transactions)
  const manualTransactions = cashTransactions.filter(tx =>
    (tx.transactionType === 'manual_in' || tx.transactionType === 'manual_out') &&
    tx.metadata?.transactionCategory !== 'delivery_collection' && // Old system already converted
    tx.metadata?.transactionCategory !== 'collection_cash_in' // New system already converted
  );

  for (const tx of manualTransactions) {
    events.push(convertToManualEvent(tx));
  }

  return events.sort((a, b) =>
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );
}

function getCollectionStatus(order: any): 'not_collected' | 'collected' | 'partial' | 'failed' {
  if (!order.collectionStatus) return 'not_collected';
  
  if (order.collectionStatus.isPending === false) {
    // Check if fully collected
    if (order.collectionStatus.actualAmount >= order.collectionStatus.expectedAmount) {
      return 'collected';
    } else if (order.collectionStatus.actualAmount > 0) {
      return 'partial';
    } else {
      return 'failed';
    }
  }
  
  return 'not_collected';
}


// Old collection conversion functions removed - now using collection events directly

function convertToManualEvent(transaction: any): FinanceEvent {
  return {
    id: transaction._id,
    type: transaction.transactionType === 'manual_in' ? 'manual_in' : 'manual_out',
    timestamp: transaction.time,
    netAmount: transaction.amount,
    performedBy: transaction.performedBy,
    description: transaction.description,
    manualTransaction: {
      reason: transaction.description,
      notes: transaction.metadata?.notes
    }
  };
}

export function useFinanceEvents() {
  const [events, setEvents] = React.useState<FinanceEvent[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  const refreshEvents = React.useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const financeEvents = await getFinanceEvents();
      setEvents(financeEvents);
    } catch (err) {
      console.error('[useFinanceEvents] Error:', err);
      setError('Failed to load finance events');
    } finally {
      setLoading(false);
    }
  }, []);

  React.useEffect(() => {
    refreshEvents();
  }, [refreshEvents]);

  return {
    events,
    loading,
    error,
    refreshEvents
  };
}

import React from 'react';
