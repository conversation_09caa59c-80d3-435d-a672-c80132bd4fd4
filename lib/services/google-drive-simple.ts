import { google } from 'googleapis';

export interface SimpleGoogleDriveConfig {
  accessToken: string;
  refreshToken: string;
  clientId: string;
  clientSecret: string;
}

export interface CloudBackupFile {
  id: string;
  name: string;
  createdTime: string;
  size: number;
}

export class SimpleGoogleDriveService {
  private drive: any = null;
  private auth: any = null;
  private isReady = false;

  async initialize(config: SimpleGoogleDriveConfig): Promise<boolean> {
    try {
      this.auth = new google.auth.OAuth2(
        config.clientId,
        config.clientSecret,
        'urn:ietf:wg:oauth:2.0:oob'
      );

      this.auth.setCredentials({
        access_token: config.accessToken,
        refresh_token: config.refreshToken
      });

      this.drive = google.drive({ version: 'v3', auth: this.auth });
      this.isReady = true;

      console.log('✅ Simple Google Drive service initialized');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Google Drive:', error);
      return false;
    }
  }

  async testConnection(): Promise<{ success: boolean; userEmail?: string; error?: string }> {
    if (!this.isReady) {
      return { success: false, error: 'Service not initialized' };
    }

    try {
      const response = await this.drive.about.get({
        fields: 'user(emailAddress)'
      });

      return {
        success: true,
        userEmail: response.data.user?.emailAddress
      };
    } catch (error: any) {
      console.error('❌ Google Drive test failed:', error);
      return {
        success: false,
        error: error.message || 'Connection test failed'
      };
    }
  }

  async uploadBackup(
    backupData: string,
    fileName: string,
    restaurantId: string
  ): Promise<{ success: boolean; fileId?: string; error?: string }> {
    if (!this.isReady) {
      return { success: false, error: 'Service not initialized' };
    }

    try {
      // Ensure Restaurant_Backups folder exists
      let folderId = await this.ensureBackupFolder();
      
      const fileMetadata = {
        name: fileName,
        parents: [folderId]
      };

      const media = {
        mimeType: 'application/json',
        body: backupData
      };

      const response = await this.drive.files.create({
        requestBody: fileMetadata,
        media: media,
        fields: 'id'
      });

      console.log(`✅ Backup uploaded to Google Drive: ${fileName}`);
      return {
        success: true,
        fileId: response.data.id
      };

    } catch (error: any) {
      console.error('❌ Failed to upload backup:', error);
      return {
        success: false,
        error: error.message || 'Upload failed'
      };
    }
  }

  async listBackups(restaurantId?: string): Promise<{ success: boolean; files?: CloudBackupFile[]; error?: string }> {
    if (!this.isReady) {
      return { success: false, error: 'Service not initialized' };
    }

    try {
      const folderId = await this.ensureBackupFolder();
      
      let query = `'${folderId}' in parents and name contains '.json'`;
      if (restaurantId) {
        query += ` and name contains 'resto-${restaurantId}'`;
      }

      const response = await this.drive.files.list({
        q: query,
        pageSize: 50,
        fields: 'files(id, name, createdTime, size)',
        orderBy: 'createdTime desc'
      });

      const files: CloudBackupFile[] = (response.data.files || []).map((file: any) => ({
        id: file.id,
        name: file.name,
        createdTime: file.createdTime,
        size: parseInt(file.size || '0')
      }));

      return { success: true, files };

    } catch (error: any) {
      console.error('❌ Failed to list backups:', error);
      return {
        success: false,
        error: error.message || 'Failed to list backups'
      };
    }
  }

  async downloadBackup(fileId: string): Promise<{ success: boolean; data?: string; error?: string }> {
    if (!this.isReady) {
      return { success: false, error: 'Service not initialized' };
    }

    try {
      const response = await this.drive.files.get({
        fileId: fileId,
        alt: 'media'
      });

      return {
        success: true,
        data: response.data
      };

    } catch (error: any) {
      console.error('❌ Failed to download backup:', error);
      return {
        success: false,
        error: error.message || 'Download failed'
      };
    }
  }

  async deleteBackup(fileId: string): Promise<{ success: boolean; error?: string }> {
    if (!this.isReady) {
      return { success: false, error: 'Service not initialized' };
    }

    try {
      await this.drive.files.delete({ fileId });
      console.log(`✅ Backup deleted from Google Drive: ${fileId}`);
      return { success: true };

    } catch (error: any) {
      console.error('❌ Failed to delete backup:', error);
      return {
        success: false,
        error: error.message || 'Delete failed'
      };
    }
  }

  private async ensureBackupFolder(): Promise<string> {
    try {
      // Search for existing folder
      const response = await this.drive.files.list({
        q: "name='Restaurant_Backups' and mimeType='application/vnd.google-apps.folder'",
        fields: 'files(id)'
      });

      if (response.data.files && response.data.files.length > 0) {
        return response.data.files[0].id;
      }

      // Create folder if it doesn't exist
      const createResponse = await this.drive.files.create({
        requestBody: {
          name: 'Restaurant_Backups',
          mimeType: 'application/vnd.google-apps.folder'
        },
        fields: 'id'
      });

      console.log('📁 Created Restaurant_Backups folder in Google Drive');
      return createResponse.data.id;

    } catch (error) {
      console.error('❌ Failed to ensure backup folder:', error);
      throw error;
    }
  }

  isInitialized(): boolean {
    return this.isReady;
  }

  reset(): void {
    this.drive = null;
    this.auth = null;
    this.isReady = false;
  }
}