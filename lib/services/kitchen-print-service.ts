import { Order, OrderItem } from '@/lib/db/v4/schemas/order-schema';
import { extractDailySequence } from '@/lib/db/v4/operations/order-ops';
import { barcodeService } from '@/lib/services/barcode-service';
import { kitchenQueueService } from '@/lib/services/kitchen-queue-service';
import { getOrderTypeLabel } from '@/lib/types/order-types';
import { simpleHTMLPrintService, SimplePrintJob } from './simple-html-print';
import { shouldShowPrintPreview, isElectronEnvironment, logPrintEnvironmentInfo } from '@/lib/utils/environment';

// 🍽️ Kitchen Printing Systems Service
// Implements the 3 systems from the implementation plan

export interface PrinterConfig {
  id: string;
  name: string;
  ipAddress?: string;
  status: 'online' | 'offline' | 'unknown';
  assignedCategories: string[]; // Categories assigned to this printer (station)
  type: 'thermal' | 'inkjet' | 'laser';
  simulated: boolean;
  isReceiptPrinter?: boolean; // 🆕 Flag to identify receipt printers
  enabled?: boolean; // 🆕 Flag to track if printer is enabled/selected
}

export interface PrintingFeatures {
  queueEnabled: boolean;        // Show queue coordination info on tickets
  barcodeEnabled: boolean;      // Generate barcodes for item completion tracking
}

export interface PrintJob {
  title: string;
  content: string;
  type: 'kitchen' | 'receipt' | 'report' | 'expo';
  printerId?: string;
  stationName?: string;
}

export interface PrintResult {
  success: boolean;
  printJob?: PrintJob;
  printJobs?: PrintJob[];  // For multi-station systems
  showPreview?: boolean;
  error?: string;
  actuallyPrinted?: boolean;
  printResults?: any[];  // Simple HTML print results
  printSuccessRate?: number;
}

export interface ItemStatus {
  orderId: string;
  itemId: string;
  itemName: string;
  status: 'pending' | 'done';
  scannedAt?: string;
  stationId?: string;
  createdAt?: string;
}

interface OSPrinterInput {
  id: string;
  name: string;
  ipAddress?: string;
  status?: 'online' | 'offline' | 'unknown';
  type?: 'thermal' | 'inkjet' | 'laser';
}

import { savePrinterSettings, loadPrinterSettings } from '@/lib/db/v4/operations/printer-settings-ops';

class KitchenPrintService {
  private printers: PrinterConfig[] = [];
  private printingFeatures: PrintingFeatures = { queueEnabled: true, barcodeEnabled: true };
  private itemStatuses: Map<string, ItemStatus> = new Map();

  // 🎯 Feature Configuration
  setPrintingFeatures(features: PrintingFeatures) {
    this.printingFeatures = features;
    localStorage.setItem('kitchen_printing_features', JSON.stringify(features));
  }

  getPrintingFeatures(): PrintingFeatures {
    const stored = localStorage.getItem('kitchen_printing_features');
    if (stored) {
      try {
        return JSON.parse(stored);
      } catch (error) {
        console.warn('Failed to parse stored printing features, using defaults');
      }
    }
    // Default features
    return { queueEnabled: true, barcodeEnabled: true };
  }

  async setPrinters(printers: PrinterConfig[]) {
    this.printers = printers;
    await savePrinterSettings(printers);
  }

  async getPrinters(): Promise<PrinterConfig[]> {
    try {
      const printers = await loadPrinterSettings();
      const isElectron = isElectronEnvironment();

      if (printers.length === 0) {
        if (isElectron) {
          console.log('🚀 [PRODUCTION] No printers found or configured. Use the settings page to discover and set up printers.');
        } else {
          console.log('🌐 [PRODUCTION WEB] No printers configured. Kitchen printing requires Electron app.');
        }
        return [];
      }

      // Update internal state
      this.printers = printers;

      // Auto-validate and fix category assignments
      try {
        const validation = await this.validateCategoryAssignments();
        if (validation.success && validation.fixed) {
          console.log('🔧 [getPrinters] Auto-fixed invalid printer category assignments');
          // Return the fixed printers
          return this.printers;
        }
      } catch (error) {
        console.warn('⚠️ [getPrinters] Failed to validate category assignments:', error);
      }

      return printers;
    } catch (error) {
      console.error('❌ [getPrinters] Failed to load printer settings:', error);
      return [];
    }
  }

  // 🆕 Get receipt printer ID from configuration
  getReceiptPrinterId(): string | null {
    const receiptPrinter = this.printers.find(p => p.isReceiptPrinter && p.status === 'online');
    return receiptPrinter?.id || null;
  }

  /**
   * Get printer name by ID for print execution
   */
  private getPrinterNameById(printerId?: string): string | undefined {
    if (!printerId) return undefined;
    const printer = this.printers.find(p => p.id === printerId);
    return printer?.name;
  }

  // 🆕 Set receipt printer by ID
  async setReceiptPrinter(printerId: string | null) {
    this.printers = this.printers.map(printer => ({
      ...printer,
      isReceiptPrinter: printer.id === printerId
    }));
    await this.setPrinters(this.printers);
  }



  // 🎯 Generate simple but reliable barcode ID for kitchen items
  private generateSimpleBarcodeId(orderId: string, itemIndex: number): string {
    // 🎯 COMPACT FORMAT requested by user
    // - Omit year and any non-numeric characters to shorten stripes
    // - Use the *daily sequence* of the order (3 digits) + a 3-digit 1-based
    //   item instance index. Example: dailySequence "002", item #5 ➜ "002005"
    //   This is at most 6 digits, fully numeric → fewer CODE128 bars.

    const dailySeq = extractDailySequence(orderId); // already 3-digit string
    const idxStr = itemIndex.toString().padStart(3, '0');
    return `${dailySeq}${idxStr}`; // e.g. 002005
  }

  // 🎨 Get standardized font sizes
  private getFontSizes() {
    return { header: 24, bold: 20, normal: 18 };
  }

  // 🖨️ Unified Multi-Ticket Print Function
  async printKitchenOrder(
    order: Order,
    tableId?: string,
    options: {} = {}
  ): Promise<PrintResult> {
    try {
      this.printingFeatures = this.getPrintingFeatures();
      this.printers = await this.getPrinters();

      console.log('🖨️ [printKitchenOrder] Features:', this.printingFeatures);
      console.log('🖨️ [printKitchenOrder] Available printers:', this.printers.length);

      // 🚨 SAFETY CHECK: No printers configured
      if (!this.printers || this.printers.length === 0) {
        console.warn('[KitchenPrintService] No printers configured. Aborting print.');

        return {
          success: false,
          error: 'No printers configured. Please add a kitchen printer in Settings.'
        };
      }

      // 🎯 Add order to queue system if queue feature is enabled
      if (this.printingFeatures.queueEnabled) {
        console.log(`🔄 [printKitchenOrder] Queue enabled - adding order ${order.id} to queue system...`);
        await this.addOrderToQueueSystem(order);
      }

      // 🎯 Generate multi-ticket print jobs based on category assignments
      const result = await this.generateMultiTicketPrintJobs(order, tableId, options);

      // 🖨️ EXECUTE ACTUAL PRINTING
      if (result.success && result.printJobs && result.printJobs.length > 0) {
        console.log(`🖨️ [printKitchenOrder] Executing ${result.printJobs.length} print jobs`);

        const printResults: any[] = [];

        for (const printJob of result.printJobs) {
          try {
            // Use simple HTML printing
            const htmlJob: SimplePrintJob = {
              id: simpleHTMLPrintService.generateJobId(),
              title: printJob.title,
              content: printJob.content,
              type: printJob.type as 'kitchen' | 'receipt' | 'report' | 'expo'
            };

            const printResult = await simpleHTMLPrintService.print(htmlJob);
            printResults.push(printResult);

            console.log(`🖨️ [printKitchenOrder] Print job ${htmlJob.id} result:`, {
              success: printResult.success,
              method: printResult.method,
              error: printResult.error
            });

          } catch (error) {
            console.error(`🖨️ [printKitchenOrder] Failed to execute print job:`, error);
            printResults.push({
              success: false,
              error: error instanceof Error ? error.message : 'Unknown print error'
            });
          }
        }

        // Update result with actual print execution status
        const successfulPrints = printResults.filter(r => r.success).length;
        const totalPrints = printResults.length;

        result.actuallyPrinted = successfulPrints > 0;
        result.printResults = printResults;
        result.printSuccessRate = totalPrints > 0 ? (successfulPrints / totalPrints) * 100 : 0;

        console.log(`🖨️ [printKitchenOrder] Print execution summary: ${successfulPrints}/${totalPrints} successful`);
      }

      // Use centralized environment utilities for preview logic
      const isElectron = isElectronEnvironment();
      const hasActualJobs = result.printJobs ? result.printJobs.length > 0 : false;
      result.showPreview = shouldShowPrintPreview(result.actuallyPrinted || false, hasActualJobs);

      // Log environment info in development
      logPrintEnvironmentInfo();

      return result;
    } catch (error) {
      console.error('Kitchen print error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown print error'
      };
    }
  }

  // 🎯 Unified Multi-Ticket Print Job Generator
  private async generateMultiTicketPrintJobs(
    order: Order,
    tableId?: string,
    options: {} = {}
  ): Promise<PrintResult> {
    const stationItems = await this.splitOrderByStation(order);
    const printJobs: PrintJob[] = [];

    // If only one printer or all items go to one printer, can still generate multiple tickets
    for (const [stationId, items] of Object.entries(stationItems)) {
      const printer = this.printers.find(p => p.id === stationId);
      if (!printer || !printer.enabled || items.length === 0) continue;

      let content: string;

      // Generate unified station ticket
      content = await this.generateSimpleTicket(
        order,
        items,
        printer,
        tableId,
        options
      );

      const dailySequence = extractDailySequence(order.id);
      printJobs.push({
        title: `${printer.name} - Order #${dailySequence}`,
        content,
        type: 'kitchen',
        printerId: printer.id,
        stationName: printer.name
      });
    }

    // Handle case where no station assignments exist - print to first available printer
    if (printJobs.length === 0 && this.printers.length > 0) {
      // 🔧 FIX: Use enabled printers instead of online status
      const enabledPrinters = this.printers.filter(p => p.enabled);

      if (enabledPrinters.length > 0) {
        // Try to find a kitchen printer first, then any enabled printer
        const fallbackPrinter = enabledPrinters.find(p =>
          p.name.toLowerCase().includes('kitchen') ||
          p.name.toLowerCase().includes('main') ||
          p.isReceiptPrinter === false
        ) || enabledPrinters[0];

        console.log('🔧 [generateMultiTicketPrintJobs] Using fallback printer:', fallbackPrinter.name, 'for order with no station assignments');

        const content = await this.generateSingleSystemTicket(order, tableId, options);

        const dailySequence = extractDailySequence(order.id);
        printJobs.push({
          title: `Kitchen Order #${dailySequence}`,
          content,
          type: 'kitchen',
          printerId: fallbackPrinter.id
        });
      } else {
        console.error('❌ [generateMultiTicketPrintJobs] No enabled printers available for fallback');
      }
    }

    // 🔍 FINAL DIAGNOSTIC: Log the result
    console.log('🔍 [generateMultiTicketPrintJobs] Generated print jobs:', printJobs.length);
    printJobs.forEach((job, index) => {
      console.log(`🔍 Job ${index + 1}: ${job.title} -> Printer: ${job.printerId}`);
    });

    return {
      success: printJobs.length > 0,
      printJobs: printJobs,
      printJob: printJobs[0], // Keep for backward compatibility
      showPreview: true,
      error: printJobs.length === 0 ? 'No print jobs could be generated. Check printer assignments.' : undefined
    };
  }

  // 🎯 NEW: Add order to queue system for proper tracking
  private async addOrderToQueueSystem(order: Order): Promise<void> {
    try {
      const stationItems = await this.splitOrderByStation(order);

      // Add order to each station's queue
      for (const [stationId, items] of Object.entries(stationItems)) {
        if (items.length > 0) {
          console.log(`📋 [addOrderToQueueSystem] Adding order ${order.id} to station ${stationId} with ${items.length} items`);
          await kitchenQueueService.addOrderToQueue(order, stationId, items);
        }
      }

      console.log(`✅ [addOrderToQueueSystem] Order ${order.id} successfully added to all relevant station queues`);
    } catch (error) {
      console.error(`❌ [addOrderToQueueSystem] Error adding order ${order.id} to queue:`, error);
      // Don't throw error here - printing should continue even if queue fails
    }
  }


  // 🏷️ Queue Context Functions
  async getStationQueueContext(stationId: string): Promise<{ totalPendingItems: number }> {
    try {
      // Use queue operations directly for more accurate queue information
      const { getStationQueue } = await import('@/lib/db/v4/operations/queue-ops');
      const stationQueue = await getStationQueue(stationId);

      // Calculate total individual items in pending and in-progress orders
      const totalPendingItems = stationQueue.items
        .filter(item => item.status === 'pending' || item.status === 'in-progress')
        .reduce((sum, queueItem) => {
          return sum + queueItem.items.reduce((itemSum, orderItem) => itemSum + orderItem.quantity, 0);
        }, 0);

      console.log(`📊 [getStationQueueContext] Station ${stationId} queue:`, {
        pendingOrders: stationQueue.pendingOrders,
        inProgressOrders: stationQueue.inProgressOrders,
        totalOrders: stationQueue.totalOrders,
        totalPendingItems: totalPendingItems,
        queueItems: stationQueue.items.map(item => ({
          orderId: item.orderId,
          status: item.status,
          itemCount: item.items.reduce((sum, orderItem) => sum + orderItem.quantity, 0),
          createdAt: item.createdAt
        }))
      });

      return {
        totalPendingItems: totalPendingItems
      };
    } catch (error) {
      console.error('❌ Error getting station queue context:', error);
      return { totalPendingItems: 0 };
    }
  }

  // 📊 Get other categories info for kitchen coordination
  private async getOtherCategoriesInfo(
    allStationItems: Record<string, OrderItem[]>,
    currentStationId: string
  ): Promise<Array<{ name: string; items: OrderItem[]; queueCount: number }>> {
    const otherCategories: Array<{ name: string; items: OrderItem[]; queueCount: number }> = [];

    console.log(`🔍 [getOtherCategoriesInfo] Checking other stations for current station: ${currentStationId}`);
    console.log(`🔍 [getOtherCategoriesInfo] Available printers:`, this.printers.map(p => ({ id: p.id, name: p.name, categories: p.assignedCategories })));
    console.log(`🔍 [getOtherCategoriesInfo] All station items:`, Object.keys(allStationItems).map(stationId => ({ stationId, itemCount: allStationItems[stationId]?.length || 0 })));

    // Get menu to resolve category names
    let menu;
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      menu = await getMenu();
    } catch (error) {
      console.warn('⚠️ Could not load menu for category names:', error);
    }

    // Check each printer/station (excluding current station)
    for (const printer of this.printers) {
      const stationId = printer.id;

      // Skip current station
      if (stationId === currentStationId) {
        console.log(`⏭️ [getOtherCategoriesInfo] Skipping current station: ${stationId}`);
        continue;
      }

      // Check if station has items from current order
      const hasCurrentOrderItems = allStationItems[stationId] && allStationItems[stationId].length > 0;

      // Get queue context for this station
      const queueContext = await this.getStationQueueContext(stationId);
      const hasQueueItems = queueContext.totalPendingItems > 0;

      console.log(`🔍 [getOtherCategoriesInfo] Station ${stationId} (${printer.name}):`, {
        hasCurrentOrderItems,
        currentOrderItemCount: allStationItems[stationId]?.length || 0,
        hasQueueItems,
        queueCount: queueContext.totalPendingItems
      });

      // Include station if it has current order items OR queue items
      if (hasCurrentOrderItems || hasQueueItems) {
        let categoryName = printer.name || stationId;

        // Clean up printer name (remove "Printer" suffix)
        categoryName = categoryName.replace(/ Printer$/, '');

        // Get real category name from menu if available
        if (menu && printer.assignedCategories?.[0]) {
          const category = menu.categories.find(c => c.id === printer.assignedCategories[0]);
          if (category) {
            categoryName = category.name;
          }
        }

        // Use current order items if available, otherwise empty array
        const items = allStationItems[stationId] || [];

        console.log(`✅ [getOtherCategoriesInfo] Adding station ${stationId} (${categoryName}) with ${items.length} current items and ${queueContext.totalPendingItems} queue items`);

        otherCategories.push({
          name: categoryName,
          items: items,
          queueCount: queueContext.totalPendingItems
        });
      } else {
        console.log(`⏭️ [getOtherCategoriesInfo] Skipping station ${stationId} - no current items or queue`);
      }
    }

    // Sort by priority: stations with current order items first, then by queue count
    otherCategories.sort((a, b) => {
      if (a.items.length > 0 && b.items.length === 0) return -1;
      if (a.items.length === 0 && b.items.length > 0) return 1;
      return b.queueCount - a.queueCount; // Higher queue count first
    });

    console.log(`🔍 [getOtherCategoriesInfo] Final other categories (sorted):`, otherCategories.map(c => ({ name: c.name, itemCount: c.items.length, queueCount: c.queueCount })));

    return otherCategories;
  }

  private async getPendingOrdersForStation(stationId: string): Promise<string[]> {
    try {
      // Import here to avoid circular dependency
      const { getPendingOrders } = await import('@/lib/db/v4/operations/order-completion-ops');
      const pendingOrders = await getPendingOrders();

      // Filter orders that have items for this station and are not served
      const stationOrders = pendingOrders.filter(order => {
        return order.stationItems[stationId] &&
               !Object.values(order.stationItems[stationId]).every(item => item.completed);
      });

      return stationOrders.map(order => order.orderId);
    } catch (error) {
      console.error('❌ Error getting pending orders for station:', error);
      return [];
    }
  }



  // 🔧 Helper method to get cached menu categories for fallback resolution
  private getCachedMenuCategories(): any[] | null {
    try {
      // Try to get menu from a global cache or import it synchronously if possible
      // This is a fallback mechanism and should not be the primary way to get category names
      if (typeof window !== 'undefined' && (window as any).__menuCache) {
        return (window as any).__menuCache.categories || null;
      }
      return null;
    } catch (error) {
      console.warn('⚠️ [getCachedMenuCategories] Failed to get cached menu categories:', error);
      return null;
    }
  }

  // 📊 Split Order by Station (Using REAL Categories) - IMPROVED
  private async splitOrderByStation(order: Order): Promise<Record<string, OrderItem[]>> {
    const stationItems: Record<string, OrderItem[]> = {};

    // Initialize enabled stations only
    const enabledPrinters = this.printers.filter(printer => printer.enabled);
    if (enabledPrinters.length === 0) {
      throw new Error('🖨️ No enabled printers configured. Configure printers in settings before printing.');
    }
    enabledPrinters.forEach(printer => {
      stationItems[printer.id] = [];
    });

    // 🔍 DIAGNOSTIC: Log printer and order information
    console.log('🔍 [splitOrderByStation] DIAGNOSTIC INFO:');
    console.log('🔍 Enabled printers:', enabledPrinters.map(p => ({
      id: p.id,
      name: p.name,
      assignedCategories: p.assignedCategories
    })));
    console.log('🔍 Order items:', order.items.map(item => ({
      name: item.name,
      menuItemId: item.menuItemId,
      categoryName: (item as any).categoryName,
      categoryId: item.categoryId
    })));

    // Load menu data once for fallback resolution
    let menuCategories: any[] | null = null;
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      menuCategories = menu.categories || [];
      console.log('🔍 [splitOrderByStation] Loaded menu categories for fallback resolution:', menuCategories.length);
    } catch (error) {
      console.warn('⚠️ [splitOrderByStation] Failed to load menu for category resolution:', error);
    }

    order.items.forEach(item => {
      console.log('🔍 [splitOrderByStation] Processing item:', item.name, 'categoryName:', (item as any).categoryName, 'categoryId:', item.categoryId, 'size:', item.size);

      // Try to get categoryName, with multiple fallback mechanisms
      let categoryName = (item as any).categoryName;

      // Fallback 1: Try to resolve categoryName from categoryId using menu data
      if (!categoryName && item.categoryId && menuCategories) {
        console.log('🔄 [splitOrderByStation] No categoryName found, attempting to resolve from categoryId:', item.categoryId);
        const category = menuCategories.find(cat => cat.id === item.categoryId);
        if (category) {
          categoryName = category.name;
          console.log('✅ [splitOrderByStation] Resolved categoryName from categoryId:', categoryName);
        }
      }


      if (!categoryName) {
        throw new Error(`🖨️ Item "${item.name}" has no categoryName and could not resolve from categoryId "${item.categoryId}". All items must have a valid categoryName for printing.`);
      }

      const assignedPrinter = enabledPrinters.find(printer =>
        printer.assignedCategories.includes(categoryName)
      );

      if (!assignedPrinter) {
        console.error('❌ [splitOrderByStation] No printer found for item:', item.name, '| size:', item.size);
        console.error('❌ Item categoryName:', categoryName, '| categoryId:', item.categoryId);
        console.error('❌ Available printers:', enabledPrinters.map(p => ({
          name: p.name,
          id: p.id,
          assignedCategories: p.assignedCategories
        })));
        console.error('❌ All order items:', order.items.map(i => ({ name: i.name, categoryName: (i as any).categoryName, size: i.size })));
        throw new Error(`🖨️ No printer assigned to category "${categoryName}" for item "${item.name}" (size: ${item.size}). Configure printer assignments in settings.`);
      }

      console.log('✅ [splitOrderByStation] Found printer by categoryName:', assignedPrinter.name, 'for item:', item.name);
      console.log('📋 [splitOrderByStation] Assigning item', item.name, 'to printer:', assignedPrinter.name);

      stationItems[assignedPrinter.id].push(item);
    });

    // Log the distribution for debugging
    console.log('📊 Station distribution:', Object.entries(stationItems).map(([id, items]) => ({
      station: this.printers.find(p => p.id === id)?.name,
      items: items.length,
      itemNames: items.map(i => i.name)
    })));

    return stationItems;
  }


  // 🎯 SINGLE SOURCE OF TRUTH - Master Print Content Generator
  private async generatePrintContent(order: Order, options: {
    type: 'single' | 'multi-station' | 'barcode' | 'expo' | 'receipt';
    stationId?: string;
    tableId?: string;
    printer?: PrinterConfig;
    stationItems?: OrderItem[];
    allStationItems?: Record<string, OrderItem[]>;
    currentStationIndex?: number;
    totalStations?: number;
    payment?: { method: string; received: number; change: number };
  }): Promise<string> {
    const fs = this.getFontSizes();

    switch (options.type) {
      case 'single':
        return await this.generateSingleSystemContent(order, fs, options.tableId);
      case 'multi-station':
        return await this.generateMultiStationContent(order, options.stationId!, fs, options.tableId);
      case 'barcode':
        return await this.generateBarcodeContent(order, options.stationId!, fs, options.tableId);
      case 'expo':
        return this.generateExpoContent(order, fs, options.tableId);
      case 'receipt':
        return await this.generateReceiptContent(order, fs, options.tableId, options.payment);
      default:
        throw new Error(`Unknown print type: ${options.type}`);
    }
  }

  /**
   * Helper to generate the common HTML wrapper for all print jobs.
   * This includes the base styling, header (title, order ID, table ID, timestamp),
   * and a slot for the main content and optional order notes.
   */
  private async generatePrintHtmlWrapper(
    jobType: 'kitchen' | 'receipt' | 'expo', // New parameter to differentiate print types
    order: Order,
    fs: { header: number; bold: number; normal: number },
    mainContentHtml: string,
    options?: { // Use an options object for clarity
      tableId?: string;
      printDate?: Date;
      categoryOrStationName?: string; // Specific for kitchen tickets
      receiptPaymentInfo?: { method: string; received: number; change: number }; // Specific for receipts
    }
  ): Promise<string> {
    // 🎯 FIX: Use order creation time, not current time
    const orderCreationDate = new Date(order.createdAt);
    const dailySequence = extractDailySequence(order.id);

    let headerHtml = '';

    if (jobType === 'kitchen') {
      const timeString = orderCreationDate.toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit'
      });
      const dateString = orderCreationDate.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit'
      });

      // Get table info
      let tableInfo = '';
      if (options?.tableId) {
        try {
          const { getTable } = await import('@/lib/db/v4/operations/table-ops');
          const table = await getTable(options.tableId);
          tableInfo = table?.name || options.tableId;
        } catch (error) {
          tableInfo = options.tableId;
        }
      }

      const orderTypeText = order.orderType ? getOrderTypeLabel(order.orderType) : 'Dine In';

      // Restructured kitchen header
      headerHtml = `
      <div style="border-bottom: 2px solid #000; padding-bottom: 2px; margin-bottom: 2px;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div>
            <div style="font-size: ${fs.normal - 1}px; line-height: 0.9;">${timeString} • ${dateString}</div>
            <div style="font-size: ${fs.normal - 1}px; line-height: 0.9;">${orderTypeText}${tableInfo ? ` • TABLE ${tableInfo}` : ''}</div>
          </div>
          <div style="font-size: ${fs.header}px; font-weight: bold; line-height: 0.9;">#${dailySequence}</div>
        </div>
      </div>
      `;
    } else if (jobType === 'receipt') {
      // For receipts, don't add any header here - it's handled by generateReceiptContent
      headerHtml = '';
    } else if (jobType === 'expo') {
      // 🎯 FIX: Also resolve table name for expo tickets
      let tableInfo = '';
      if (options?.tableId) {
        try {
          const { getTable } = await import('@/lib/db/v4/operations/table-ops');
          const table = await getTable(options.tableId);
          tableInfo = table?.name ? `Table ${table.name}` : `Table ${options.tableId}`;
        } catch (error) {
          console.warn('⚠️ Could not resolve table name for expo:', error);
          tableInfo = `Table ${options.tableId}`;
        }
      }

      headerHtml = `
      <div style="text-align: center; margin-bottom: 1px;">
        <div style="font-size: ${fs.bold}px; font-weight: bold; line-height: 0.9;">EXPO TICKET</div>
        <div style="font-size: ${fs.normal}px; line-height: 0.9;">ORDER #${dailySequence}</div>
        ${tableInfo ? `<div style="font-size: ${fs.normal - 1}px; line-height: 0.9;">${tableInfo}</div>` : ''}
        <div style="font-size: ${fs.normal - 2}px; line-height: 0.9;">${orderCreationDate.toLocaleString()}</div>
      </div>
      `;
    } else {
      // Fallback for any other unexpected jobType or generic title-based print (should not happen with defined types)
      headerHtml = `
      <div style="text-align: center; margin-bottom: 3px;">
        <div style="font-size: ${fs.bold}px; font-weight: bold;">${options?.categoryOrStationName || 'ORDER'}</div>
        <div style="font-size: ${fs.normal}px;">ORDER #${dailySequence}</div>
        ${options?.tableId ? `<div style="font-size: ${fs.normal - 1}px;">TABLE ${options.tableId}</div>` : ''}
        <div style="font-size: ${fs.normal - 2}px;">${orderCreationDate.toLocaleString()}</div>
      </div>
      `;
    }

    // Set container width and max-width to match paper size (optimize receipt for 80mm)
    const containerWidth = jobType === 'receipt' ? '80mm' : '80mm';
    const maxWidthPx = jobType === 'receipt' ? '304px' : '220px';

    // Use a highly legible system UI font for receipts; monospace for kitchen tickets
    const fontFamily = jobType === 'receipt'
      ? "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Noto Sans', 'Helvetica Neue', Arial, 'Liberation Sans', sans-serif"
      : "'Courier New', monospace";

    // Improve numeric alignment and smoothing for receipts
    const numericCSS = jobType === 'receipt'
      ? "font-variant-numeric: tabular-nums; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;"
      : "";

    let html = `
${jobType === 'receipt' ? `<style> .__rcpt { ${numericCSS} } </style>` : ''}
<div class="__rcpt" style="font-family: ${fontFamily}; width: ${containerWidth}; max-width: ${maxWidthPx}; margin: 0; padding: 0; line-height: 1.10;">
  ${headerHtml}
  ${mainContentHtml}
`;

    // Only show order notes for kitchen tickets, not receipts
    if (order.notes && jobType !== 'receipt') {
      html += `
  <div style="margin-top: 1px; padding: 1px; border: 1px solid #000;">
    <div style="font-size: ${fs.normal - 1}px; font-weight: bold; line-height: 0.9;">ORDER NOTES:</div>
    <div style="font-size: ${fs.normal - 1}px; line-height: 0.9;">${order.notes}</div>
  </div>`;
    }

    html += `
</div>`;

    return html;
  }

  // 🍕 Render Kitchen Item (handles both regular items and custom pizzas) - ULTRA COMPACT FORMAT
  private renderKitchenItem(item: OrderItem, fs: { header: number; bold: number; normal: number }): string {
    let itemContent = '';

    // 🍕 Handle Custom Pizza Display - ULTRA COMPACT WITH VISUAL SEPARATION
    if (item.compositeType === 'pizza_quarters' && item.quarters && item.quarters.length > 0) {
      // Main item line: include quantity multiplier when >1
      const qty = item.quantity && item.quantity > 1 ? `${item.quantity}x ` : '';
      itemContent += `<div style="font-size: ${fs.normal}px; font-weight: bold; margin-bottom: 0px; line-height: 0.9;">${qty}${item.name}${item.size ? ` (${item.size})` : ''}</div>`;

      // Show sections distinctively (determine if 2 or 4 sections)
      const totalQuarters = item.quarters.length;
      const sectionType = totalQuarters === 2 ? '2 sections' : '4 sections';
      itemContent += `<div style="font-size: ${fs.normal - 1}px; font-style: italic; margin-bottom: 1px; line-height: 0.9;">[${sectionType}]</div>`;

      // List each quarter individually without grouping
      item.quarters.forEach((quarter, index) => {
        const sectionNumber = index + 1;
        itemContent += `<div style="font-size: ${fs.normal - 2}px; margin-bottom: 0px; padding-left: 2px; border-left: 1px solid #000; line-height: 0.9;">▸ Section ${sectionNumber}: ${quarter.name}</div>`;
      });
    } else {
      // Regular item display - include quantity multiplier when >1
      const qty = item.quantity && item.quantity > 1 ? `${item.quantity}x ` : '';
      itemContent += `<div style="font-size: ${fs.normal}px; font-weight: bold; margin-bottom: 0px; line-height: 0.9;">${qty}${item.name}${item.size ? ` (${item.size})` : ''}</div>`;
    }

    // Show addons in compact format
    if (item.addons?.length) {
      const addonsNames = item.addons.map(a => a.name).join(', ');
      itemContent += `<div style="font-size: ${fs.normal - 2}px; margin-bottom: 0px; line-height: 0.9;">+ ${addonsNames}</div>`;
    }

    // Show notes in compact format
    if (item.notes) {
      itemContent += `<div style="font-size: ${fs.normal - 2}px; font-weight: bold; margin-bottom: 0px; line-height: 0.9;">⚠️ ${item.notes}</div>`;
    }

    return itemContent;
  }

  // 🍳 Generate Single System Content - ULTRA COMPACT
  private async generateSingleSystemContent(
    order: Order,
    fs: { header: number; bold: number; normal: number },
    tableId?: string
  ): Promise<string> {
    let mainContent = '';

    // Group items by category for better organization
    const itemsByCategory: Record<string, OrderItem[]> = {};
    order.items.forEach(item => {
      const category = item.categoryId || 'Uncategorized';
      if (!itemsByCategory[category]) {
        itemsByCategory[category] = [];
      }
      itemsByCategory[category].push(item);
    });

    // Get menu to resolve category names
    let menu;
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      menu = await getMenu();
    } catch (error) {
      console.warn('⚠️ Could not load menu for category names:', error);
    }

    // 🎯 NEW COMPACT FORMAT: Category directly followed by items
    Object.entries(itemsByCategory).forEach(([categoryId, items], index) => {
      // 🎯 FIX: Get actual category name instead of ID
      let categoryName = categoryId.toUpperCase();
      if (menu && categoryId !== 'Uncategorized') {
        const category = menu.categories.find(c => c.id === categoryId);
        if (category) {
          categoryName = category.name.toUpperCase();
        }
      }

      // Category header - ultra compact, no wasted space
      mainContent += `<div style="font-size: ${fs.normal}px; font-weight: bold; margin-top: ${index > 0 ? '1px' : '0'}; margin-bottom: 0px; line-height: 1.0;">${categoryName}:</div>`;

      // Items in ultra compact format
      items.forEach(item => {
        mainContent += `<div style="margin-bottom: 0px; margin-left: 1px;">${this.renderKitchenItem(item, fs)}</div>`;
      });
    });

    // Pass 'kitchen' jobType and the generic 'KITCHEN' as categoryOrStationName for single system
    return await this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: 'KITCHEN' });
  }

  // 🏪 Generate Multi-Station Content - ULTRA COMPACT
  private async generateMultiStationContent(
    order: Order,
    categoryId: string,
    fs: { header: number; bold: number; normal: number },
    tableId?: string
  ): Promise<string> {
    const categoryItems = order.items.filter(item => item.categoryId === categoryId);

    // 🎯 FIX: Get actual category name instead of ID
    let categoryName = categoryId.toUpperCase();
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      const category = menu.categories.find(c => c.id === categoryId);
      if (category) {
        categoryName = category.name.toUpperCase();
      }
    } catch (error) {
      console.warn('⚠️ Could not load menu for category name:', error);
    }

    let mainContent = '';

    categoryItems.forEach(item => {
      mainContent += `<div style="margin-bottom: 1px;">${this.renderKitchenItem(item, fs)}</div>`;
    });

    // Queue context for other stations - ultra compact display
    const otherCategoriesInfo = await this.getOtherCategoriesInfo(
      { [categoryId]: categoryItems },
      categoryId
    );

    if (otherCategoriesInfo.length > 0) {
      const otherStationsText = otherCategoriesInfo
        .map(info => `${info.name}: ${info.queueCount}`)
        .join(' • ');
      mainContent += `
      <div style="margin-top: 3px; padding: 1px; border-top: 1px solid #000; font-size: ${fs.normal - 2}px; text-align: center;">
        ${otherStationsText}
      </div>`;
    }

    return await this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: categoryName });
  }

  // 🎯 Generate Simple Ticket Content (no queue, no barcodes)
  private async generateSimpleTicket(
    order: Order,
    stationItems: OrderItem[],
    printer: PrinterConfig,
    tableId?: string,
    options: {} = {}
  ): Promise<string> {
    const fs = this.getFontSizes();
    const categoryId = printer.assignedCategories[0] || printer.name.replace(/ Printer$/, '');

    // Get actual category name instead of ID
    let categoryName = categoryId.toUpperCase();
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      const category = menu.categories.find(c => c.id === categoryId);
      if (category) {
        categoryName = category.name.toUpperCase();
      }
    } catch (error) {
      console.warn('⚠️ Could not load menu for category name:', error);
    }

    let mainContent = `<div style="font-size: ${fs.bold}px; font-weight: bold; margin-bottom: 2px;">${categoryName}:</div>`;

    // Simple item listing - no queue info or barcodes
    stationItems.forEach(item => {
      mainContent += `<div style="margin-bottom: 2px; margin-left: 4px;">${this.renderKitchenItem(item, fs)}</div>`;
    });

    return this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: categoryName });
  }

  // 🎯 Calculate global item index across entire order (not per-category)
  private calculateGlobalItemIndex(order: Order, targetCategoryId: string, targetItemIndex: number): number {
    let globalIndex = 1;

    // Iterate through all items in order to find the global position
    for (const item of order.items) {
      for (let i = 0; i < item.quantity; i++) {
        if (item.categoryId === targetCategoryId && globalIndex === targetItemIndex) {
          return globalIndex;
        }
        globalIndex++;
      }
    }

    return globalIndex;
  }

  // 🎯 Generate Barcode Content - ULTRA COMPACT with per-item barcodes
  private async generateBarcodeContent(
    order: Order,
    categoryId: string,
    fs: { header: number; bold: number; normal: number },
    tableId?: string
  ): Promise<string> {
    const categoryItems = order.items.filter(item => item.categoryId === categoryId);

    // 🎯 FIX: Get actual category name instead of ID
    let categoryName = categoryId.toUpperCase();
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      const category = menu.categories.find(c => c.id === categoryId);
      if (category) {
        categoryName = category.name.toUpperCase();
      }
    } catch (error) {
      console.warn('⚠️ Could not load menu for category name:', error);
    }

    let mainContent = '';
    let globalIndex = this.calculateGlobalItemIndex(order, categoryId, 0);

    // 🔧 Build barcode statuses for this order if not already done
    this.buildBarcodeItemStatuses(order);

    // Import barcode service for barcode generation
    const { barcodeService } = await import('@/lib/services/barcode-service');

    categoryItems.forEach(item => {
      for (let i = 0; i < item.quantity; i++) {
        const barcodeId = this.generateSimpleBarcodeId(order.id, globalIndex);
        const barcodeDataURL = barcodeService.generateKitchenBarcodeDataURL(barcodeId);

        // Create ultra-compact item block with barcode
        mainContent += `
        <div style="margin-bottom: 1px; padding: 1px; border: 1px solid #000;">
          ${this.renderKitchenItem({ ...item, quantity: 1 }, fs)}
          <div style="text-align: center; margin-top: 1px;">
            <img src="${barcodeDataURL}" style="max-width: 170px; height: 20px; display: block; margin: 0 auto;" />
            <div style="font-size: ${fs.normal - 4}px; margin-top: 0px; line-height: 1;">${barcodeId}</div>
          </div>
        </div>`;
        globalIndex++;
      }
    });

    // Queue context for other stations - ultra compact display
    const otherCategoriesInfo = await this.getOtherCategoriesInfo(
      { [categoryId]: categoryItems },
      categoryId
    );

    if (otherCategoriesInfo.length > 0) {
      const otherStationsText = otherCategoriesInfo
        .map(info => `${info.name}: ${info.queueCount}`)
        .join(' • ');
      mainContent += `
      <div style="margin-top: 3px; padding: 1px; border-top: 1px solid #000; font-size: ${fs.normal - 2}px; text-align: center;">
        ${otherStationsText}
      </div>`;
    }

    return await this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: categoryName });
  }

  // 🎯 Generate Expo Content - used for expo tickets that show overview of full order
  private async generateExpoContent(
    order: Order,
    fs: { header: number; bold: number; normal: number },
    tableId?: string
  ): Promise<string> {
    let mainContent = '';

    // 🔧 Render items in a compact format
    const renderItems = (itemsToRender: OrderItem[], fs: { header: number; bold: number; normal: number }) => {
      const renderedItems: string[] = [];
      itemsToRender.forEach(item => {
        // Render each item individually based on quantity
        for (let i = 0; i < item.quantity; i++) {
          renderedItems.push(`<div style="margin-bottom: 1px; font-size: ${fs.normal}px;">• ${item.name}${item.size ? ` (${item.size})` : ''}</div>`);
        }
      });
      return renderedItems.join('');
    };

    // Group items by category for better organization
    const itemsByCategory: Record<string, OrderItem[]> = {};
    order.items.forEach(item => {
      const category = item.categoryId || 'Uncategorized';
      if (!itemsByCategory[category]) {
        itemsByCategory[category] = [];
      }
      itemsByCategory[category].push(item);
    });

    // Get menu to resolve category names
    let menu;
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      menu = await getMenu();
    } catch (error) {
      console.warn('⚠️ Could not load menu for category names:', error);
    }

    Object.entries(itemsByCategory).forEach(([categoryId, items], index) => {
      // 🎯 FIX: Get actual category name instead of ID
      let categoryName = categoryId.toUpperCase();
      if (menu && categoryId !== 'Uncategorized') {
        const category = menu.categories.find(c => c.id === categoryId);
        if (category) {
          categoryName = category.name.toUpperCase();
        }
      }

      mainContent += `<div style="font-size: ${fs.normal}px; font-weight: bold; margin-top: ${index > 0 ? '2px' : '0'}; margin-bottom: 1px;">${categoryName}:</div>`;
      mainContent += `<div style="margin-left: 4px;">${renderItems(items, fs)}</div>`;
    });

    mainContent += `
    <div style="margin-top: 4px; padding: 2px; border: 1px solid #000; text-align: center;">
      <div style="font-size: ${fs.normal}px; font-weight: bold;">READY FOR PICKUP</div>
    </div>`;

    return await this.generatePrintHtmlWrapper('expo', order, fs, mainContent, { tableId });
  }

  // 🧾 Generate Receipt Content
  private async generateReceiptContent(
    order: Order,
    fs: { header: number; bold: number; normal: number },
    tableId?: string,
    payment?: { method: string; received: number; change: number }
  ): Promise<string> {
    const { getSettings } = await import('@/lib/db/v4/operations/settings-ops');
    const settings = await getSettings();

    const orderDate = new Date(order.createdAt);
    const timeString = orderDate.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' });
    const dateString = orderDate.toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: '2-digit' });

    const currency = (settings.currency || 'DA').toUpperCase();
    // Scale all receipt sizes down ~20% for 80mm readability
    const scale = 0.8;
    const rs = {
      header: Math.round(fs.header * scale),
      bold: Math.round(fs.bold * scale),
      normal: Math.round(fs.normal * scale)
    };


    // Header
    let html = `
<div style="text-align: center; padding: 6px 0 4px; border-bottom: 2px solid #000; margin-bottom: 6px;">
  <div style="font-size: ${rs.header + 2}px; font-weight: 800; letter-spacing: .3px; line-height: 1.05; text-transform: capitalize;">
    ${settings.restaurantName || 'Restaurant'}
  </div>
  ${settings.restaurantAddress ? `<div style=\"font-size: ${rs.normal - 2}px; line-height: 1.15;\">${settings.restaurantAddress}</div>` : ''}
  ${settings.restaurantPhone ? `<div style=\"font-size: ${rs.normal - 2}px; line-height: 1.15;\">${settings.restaurantPhone}</div>` : ''}
  <div style="display:flex; align-items:center; justify-content:space-between; gap:8px; margin-top: 6px; font-size: ${rs.normal - 2}px;">
    <span style="border:1px solid #000; padding:2px 8px; border-radius:12px; font-weight:700;">#${extractDailySequence(order.id)}</span>
    <span>${timeString} • ${dateString}${tableId ? ` • T${tableId}` : ''}</span>
  </div>
</div>
`;

    // Items section (grid for perfect alignment)
    html += `<div style="margin: 2px 0 4px;">`;
    order.items.forEach(item => {
      const qty = item.quantity || 1;
      const itemLineTotal = (item.price * qty);
      html += `
<div style="display:grid; grid-template-columns: 1fr auto; gap: 10px; font-size: ${rs.normal}px; margin: 2px 0; line-height: 1.15;">
  <div style="font-weight:600;">${qty > 1 ? `${qty}x ` : ''}${item.name}${item.size ? ` (${item.size})` : ''}</div>
  <div style="font-weight:700; text-align:right;">${itemLineTotal.toFixed(2)} ${currency}</div>
</div>`;

      if (item.addons && item.addons.length > 0) {
        const addonsNames = item.addons.map(addon => addon.name).join(', ');
        const addonsTotal = item.addons.reduce((sum, addon) => sum + addon.price, 0) * qty;
        html += `
<div style="display:grid; grid-template-columns: 1fr auto; gap: 10px; font-size: ${rs.normal - 2}px; margin: 0 0 2px 12px; line-height: 1.15;">
  <div>+ ${addonsNames}</div>
  <div style="text-align:right;">${addonsTotal.toFixed(2)} ${currency}</div>
</div>`;
      }
    });
    html += `</div>`;

    // Total section & payment
    html += `
<div style="border-top: 2px solid #000; padding-top: 8px; margin-top: 4px;">
  <div style="display:grid; grid-template-columns: 1fr auto; gap:10px; align-items:center; font-size: ${rs.bold + 1}px; font-weight: 800; line-height: 1.1;">
    <div style="letter-spacing:.3px;">TOTAL</div>
    <div style="padding:2px 8px; border:2px solid #000; border-radius:8px;">${order.total.toFixed(2)} ${currency}</div>
  </div>
</div>
`;

    if (payment) {
      html += `
<div style="margin-top: 4px; font-size: ${rs.normal - 2}px;">
  <div style="display: flex; justify-content: space-between; margin: 2px 0;">
    <span>Paid by</span>
    <span style="font-weight: 700; text-transform: uppercase;">${payment.method}</span>
  </div>
  <div style="display: flex; justify-content: space-between; margin: 2px 0;">
    <span>Received</span>
    <span>${payment.received.toFixed(2)} ${currency}</span>
  </div>
  <div style="display: flex; justify-content: space-between; margin: 2px 0;">
    <span>Change</span>
    <span>${payment.change.toFixed(2)} ${currency}</span>
  </div>
</div>
`;
    }

    // Customer info
    if (order.customer && (order.customer.phone || order.customer.address || order.customer.name)) {
      html += `
<div style="border-top: 1px dashed #000; padding-top: 6px; margin-top: 6px; font-size: ${rs.normal - 2}px;">
  ${order.customer.name ? `<div style="line-height: 1.2; font-weight: 600;">${order.customer.name}</div>` : ''}
  ${order.customer.phone ? `<div style=\"line-height: 1.2;\">${order.customer.phone}</div>` : ''}
  ${order.customer.address ? `<div style=\"line-height: 1.2;\">${order.customer.address}</div>` : ''}
</div>
`;
    }

    // Footer with tastefully dotted divider
    html += `
<div style="margin-top: 8px;">
  <div style="border-bottom: 1px dashed #444; margin: 6px 0 10px;"></div>
  <div style="text-align: center; font-size: ${rs.normal - 2}px; line-height: 1.15; opacity:.9;">${settings.restaurantFooter || 'Thanks for your visit!'}</div>
</div>
`;

    return await this.generatePrintHtmlWrapper('receipt', order, fs, html, { tableId, receiptPaymentInfo: payment });
  }

  // 🎟️ Generate Single System Print Ticket - COMPACT
  private async generateSingleSystemTicket(
    order: Order,
    tableId?: string,
    options: {} = {}
  ): Promise<string> {
    const fs = this.getFontSizes();

    // 🎯 USE THE NEW COMPACT SINGLE SYSTEM CONTENT
    const mainContent = await this.generateSingleSystemContent(order, fs, tableId);

    // 🎯 USE THE NEW COMPACT WRAPPER
    return this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: 'KITCHEN' });
  }

  // 🎟️ Generate Multi-Station Print Ticket (for actual printing)
    private async generateMultiStationTicket(
    order: Order,
    stationItems: OrderItem[],
    printer: PrinterConfig,
    allStationItems: Record<string, OrderItem[]>,
    tableId?: string,
    options: {} = {}
  ): Promise<string> {
    const fs = this.getFontSizes();
    const categoryId = printer.assignedCategories[0] || printer.name.replace(/ Printer$/, '');

    // 🎯 FIX: Get actual category name instead of ID
    let categoryName = categoryId.toUpperCase();
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      const category = menu.categories.find(c => c.id === categoryId);
      if (category) {
        categoryName = category.name.toUpperCase();
      }
    } catch (error) {
      console.warn('⚠️ Could not load menu for category name:', error);
    }

    // 🎯 USE THE SAME COMPACT FORMAT AS OTHER FUNCTIONS
    let mainContent = `<div style="font-size: ${fs.bold}px; font-weight: bold; margin-bottom: 2px;">${categoryName}:</div>`;

    // 🎯 COMPACT ITEM LISTING - NO BOXES, DIRECT LIST
    stationItems.forEach(item => {
      mainContent += `<div style="margin-bottom: 2px; margin-left: 4px;">${this.renderKitchenItem(item, fs)}</div>`;
    });

    // 🎯 COMPACT QUEUE DISPLAY: Show other stations with minimal space
    const otherCategoriesInfo = await this.getOtherCategoriesInfo(allStationItems, printer.id);
    console.log(`🖨️ [generateMultiStationTicket] Other categories info for ${printer.name}:`, otherCategoriesInfo);

    if (otherCategoriesInfo.length > 0) {
      const hasQueueItems = otherCategoriesInfo.some(info => info.queueCount > 0);

      if (hasQueueItems) {
        mainContent += `<div style="margin-top: 4px; padding-top: 2px; border-top: 1px solid #000;">`;

        otherCategoriesInfo.forEach(info => {
          if (info.queueCount > 0) {
            mainContent += `<div style="font-size: ${fs.normal - 1}px; margin-bottom: 1px;">${info.name.toUpperCase()}: ${info.queueCount}</div>`;
          }
        });

        mainContent += `</div>`;
      }
    }

    // 🎯 USE THE NEW COMPACT WRAPPER
    return this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: categoryName });
  }

  // 🎟️ Generate Barcode Station Print Ticket - ULTRA COMPACT
  private async generateBarcodeStationTicket(
    order: Order,
    stationItems: OrderItem[],
    printer: PrinterConfig,
    allStationItems: Record<string, OrderItem[]>,
    tableId?: string,
    options: {} = {}
  ): Promise<string> {
    const fs = this.getFontSizes();
    const categoryId = printer.assignedCategories[0] || printer.name.replace(/ Printer$/, '');

    // 🎯 FIX: Get actual category name instead of ID
    let categoryName = categoryId.toUpperCase();
    try {
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      const category = menu.categories.find(c => c.id === categoryId);
      if (category) {
        categoryName = category.name.toUpperCase();
      }
    } catch (error) {
      console.warn('⚠️ Could not load menu for category name:', error);
    }

    // 🎯 COMPACT HEADER: Category directly
    let mainContent = `<div style="font-size: ${fs.bold}px; font-weight: bold; margin-bottom: 2px;">${categoryName}:</div>`;

    for (const stationItem of stationItems) {
      for (let i = 0; i < stationItem.quantity; i++) {
        // 🎯 CRITICAL FIX: Calculate exact global position for this specific item instance
        let globalItemIndex = 1;

        // Count all item instances that come before this one in the order
        for (const orderItem of order.items) {
          if (orderItem.id === stationItem.id) {
            // Found our item, add the current instance index
            globalItemIndex += i;
            break;
          }
          // Add all quantities from items that come before this one
          globalItemIndex += orderItem.quantity;
        }

        // 🎯 Use exact global index for this item instance
        const simpleBarcodeId = this.generateSimpleBarcodeId(order.id, globalItemIndex);

        // 🎯 Generate barcode SVG and embed it directly
        let barcodeSvg = '';
        try {
          const barcodeDataURL = barcodeService.generateKitchenBarcodeDataURL(simpleBarcodeId);
          barcodeSvg = `<img src="${barcodeDataURL}" style="width: 100%; max-width: 180px; height: 25px; margin: 1px 0;" alt="" />`;
          console.log(`✅ [generateBarcodeContent] Barcode generated successfully for ${simpleBarcodeId}, data URL length: ${barcodeDataURL.length}`);
        } catch (error) {
          console.error('❌ Error generating barcode:', error);
          barcodeSvg = `<div style="text-align: center; font-size: 9px; border: 1px solid #000; padding: 1px;">${simpleBarcodeId}</div>`;
        }

        // 🎯 ULTRA COMPACT BARCODE ITEM
        mainContent += `<div style="margin-bottom: 2px; padding: 1px; border: 1px solid #000; margin-left: 4px;">`;
        mainContent += this.renderKitchenItem(stationItem, fs);
        mainContent += `<div style="text-align: center; margin: 1px 0;">${barcodeSvg}</div></div>`;
      }
    }

    // 🎯 COMPACT QUEUE DISPLAY: Show other stations with minimal space
    const otherCategoriesInfo = await this.getOtherCategoriesInfo(allStationItems, printer.id);
    console.log(`🖨️ [generateBarcodeStationTicket] Other categories info for ${printer.name}:`, otherCategoriesInfo);

    if (otherCategoriesInfo.length > 0) {
      const hasQueueItems = otherCategoriesInfo.some(info => info.queueCount > 0);

      if (hasQueueItems) {
        mainContent += `<div style="margin-top: 4px; padding-top: 2px; border-top: 1px solid #000;">`;

        otherCategoriesInfo.forEach(info => {
          if (info.queueCount > 0) {
            mainContent += `<div style="font-size: ${fs.normal - 1}px; margin-bottom: 1px;">${info.name.toUpperCase()}: ${info.queueCount}</div>`;
          }
        });

        mainContent += `</div>`;
      }
    }

    // Pass through the new compact wrapper
    return this.generatePrintHtmlWrapper('kitchen', order, fs, mainContent, { tableId, categoryOrStationName: categoryName });
  }

  private initializeItemStatuses(order: Order, stationItems: OrderItem[], stationId: string) {
    // Initialize status for each item in the order, specific to a station
    order.items.forEach(item => {
      if (item.categoryId && stationItems.some(si => si.id === item.id)) {
        const existingStatus = this.itemStatuses.get(item.id);
        if (!existingStatus) {
          this.itemStatuses.set(item.id, {
        orderId: order.id,
            itemId: item.id,
        itemName: item.name,
        status: 'pending',
            stationId: stationId,
        createdAt: new Date().toISOString()
      });
        }
      }
    });
  }

  scanItemBarcode(barcode: string): { success: boolean; message: string; shouldPrintExpo?: boolean } {
    // Direct lookup – key is the barcode itself
    const itemStatus = this.itemStatuses.get(barcode);

    if (!itemStatus) {
      return { success: false, message: 'Item not found or already completed.' };
    }

    // Mark as done
    itemStatus.status = 'done';
    itemStatus.scannedAt = new Date().toISOString();
    this.itemStatuses.set(barcode, itemStatus); // Update map

    // Check if all items for this order are done
    const allOrderItems = Array.from(this.itemStatuses.values()).filter(status => status.orderId === itemStatus.orderId);
    const pendingItemsCount = allOrderItems.filter(status => status.status === 'pending').length;

      return {
        success: true,
      message: `Item ${itemStatus.itemName} marked as done.`,
      shouldPrintExpo: pendingItemsCount === 0 // If no pending items, suggest printing expo ticket
    };
  }

  generateExpoTicket(orderId: string): PrintJob | null {
    const orderItems = Array.from(this.itemStatuses.values())
      .filter(status => status.orderId === orderId);

    if (!orderItems.every(status => status.status === 'done')) {
      return null;
    }

    const content = `
      <div class="text-base font-mono leading-tight">
        <div class="text-center font-bold text-xl mb-4 border-b-2 border-black pb-2">EXPO STATION</div>
        <div class="text-center font-bold text-lg mb-2 bg-green-200 p-2">READY FOR ASSEMBLY</div>
        <div class="text-center font-bold text-lg mb-2">ORDER #${orderId}</div>
        <div class="text-center text-sm mb-4">${new Date().toLocaleString()}</div>
        <hr class="my-2">

        <div class="mb-4 bg-green-50 p-3 border-2 border-green-500">
          <div class="font-bold mb-2 text-center">ALL STATION ITEMS COMPLETED</div>
          <div class="text-sm text-center">All kitchen stations have finished their items.</div>
          <div class="text-sm text-center font-bold">Order is ready for final assembly and service.</div>
        </div>

        <div class="mb-4">
          <div class="font-bold mb-2 border-b">COMPLETED ITEMS SUMMARY:</div>
          <div class="text-sm">Total items scanned: ${orderItems.length}</div>
          <div class="text-sm">All barcodes verified: YES</div>
          <div class="text-sm">Status: READY TO SERVE</div>
        </div>

        <hr class="my-2">
        <div class="text-center font-bold text-lg bg-green-600 text-white p-3">
          SERVE IMMEDIATELY
        </div>
        <div class="text-center text-xs mt-2">PRINTED: ${new Date().toLocaleString()}</div>
      </div>
    `;

    const dailySequence = extractDailySequence(orderId);
    return {
      title: `EXPO - ORDER #${dailySequence} READY`,
      content,
      type: 'expo'
    };
  }

  getOrderStatus(orderId: string): { pending: number; done: number; total: number } {
    const orderItems = Array.from(this.itemStatuses.values())
      .filter(status => status.orderId === orderId);

    const pending = orderItems.filter(status => status.status === 'pending').length;
    const done = orderItems.filter(status => status.status === 'done').length;

    return { pending, done, total: orderItems.length };
  }

  async printReceipt(
    order: Order,
    options: { fontSize?: 'small' | 'medium' | 'large'; printerId?: string } = {}
  ): Promise<PrintResult> {
    const fs = this.getFontSizes();

    const content = await this.generateReceiptContent(order, fs, undefined, undefined);

    const dailySequence = extractDailySequence(order.id);
    const printJob: PrintJob = {
      title: `Receipt - Order #${dailySequence}`,
      content: content,
      type: 'receipt',
      printerId: options.printerId || this.getReceiptPrinterId() || undefined,
    };

    // 🖨️ EXECUTE ACTUAL RECEIPT PRINTING
    let actuallyPrinted = false;
    let printResults: any[] = [];
    let printSuccessRate = 0;

    try {
      console.log(`🖨️ [printReceipt] Executing receipt print for order ${order.id}`);

      // Execute receipt print using simple HTML service
      const htmlJob: SimplePrintJob = {
        id: simpleHTMLPrintService.generateJobId(),
        title: `Receipt #${extractDailySequence(order.id)}`,
        content: printJob.content,
        type: 'receipt'
      };

      const printResult = await simpleHTMLPrintService.print(htmlJob);
      printResults.push(printResult);
      actuallyPrinted = printResult.success;
      printSuccessRate = printResult.success ? 100 : 0;

      console.log(`🖨️ [printReceipt] Receipt print result:`, {
        success: printResult.success,
        method: printResult.method,
        error: printResult.error
      });

    } catch (error) {
      console.error(`🖨️ [printReceipt] Failed to execute receipt print:`, error);
      printResults.push({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown print error'
      });
    }

    return {
      success: true,
      printJob: printJob,
      showPreview: shouldShowPrintPreview(actuallyPrinted, printJob !== null), // Use centralized logic
      actuallyPrinted,
      printResults,
      printSuccessRate
    };
  }

  async resetPrinters(): Promise<void> {
    await savePrinterSettings([]);
    this.printers = [];
    console.log('🖨️ Printers configuration reset.');
  }

  /**
   * Validate and fix printer category assignments
   * Ensures all assignedCategories contain only valid category names from the current menu
   */
  async validateCategoryAssignments(): Promise<{
    success: boolean;
    fixed: boolean;
    report: {
      validCategories: string[];
      invalidAssignments: Array<{ printerId: string; printerName: string; invalidCategories: string[] }>;
      fixedPrinters: Array<{ printerId: string; printerName: string; removedCategories: string[] }>;
    };
  }> {
    try {
      // Get current menu categories
      const { getMenu } = await import('@/lib/db/v4/operations/menu-ops');
      const menu = await getMenu();
      const validCategoryNames = menu.categories.map(c => c.name);

      const report = {
        validCategories: validCategoryNames,
        invalidAssignments: [] as Array<{ printerId: string; printerName: string; invalidCategories: string[] }>,
        fixedPrinters: [] as Array<{ printerId: string; printerName: string; removedCategories: string[] }>
      };

      let needsFixing = false;
      const fixedPrinters = this.printers.map(printer => {
        const invalidCategories = printer.assignedCategories.filter(catName =>
          !validCategoryNames.includes(catName)
        );

        if (invalidCategories.length > 0) {
          needsFixing = true;

          // Log invalid assignments
          report.invalidAssignments.push({
            printerId: printer.id,
            printerName: printer.name,
            invalidCategories
          });

          // Remove invalid categories
          const validAssignedCategories = printer.assignedCategories.filter(catName =>
            validCategoryNames.includes(catName)
          );

          report.fixedPrinters.push({
            printerId: printer.id,
            printerName: printer.name,
            removedCategories: invalidCategories
          });

          console.log(`🔧 [validateCategoryAssignments] Fixed printer "${printer.name}": removed invalid categories:`, invalidCategories);

          return {
            ...printer,
            assignedCategories: validAssignedCategories
          };
        }

        return printer;
      });

      if (needsFixing) {
        // Save the fixed printer settings
        await savePrinterSettings(fixedPrinters);
        this.printers = fixedPrinters;
        console.log('✅ [validateCategoryAssignments] Fixed printer category assignments');
      }

      return {
        success: true,
        fixed: needsFixing,
        report
      };

    } catch (error) {
      console.error('❌ [validateCategoryAssignments] Failed to validate printer categories:', error);
      return {
        success: false,
        fixed: false,
        report: {
          validCategories: [],
          invalidAssignments: [],
          fixedPrinters: []
        }
      };
    }
  }

  async forceRefreshPrinters(): Promise<void> {
    console.log('🔄 Forcing refresh of printers...');
    this.printers = []; // Clear existing printers
    await savePrinterSettings([]); // Clear from DB as well
    console.log('✅ Printers refreshed.');
  }

  // 🧹 Debug method to check and optionally reset queue
  async debugQueueStatus(): Promise<{
    stationQueues: Array<{
      stationId: string;
      stationName: string;
      totalPendingItems: number;
      queueItems: Array<{
        orderId: string;
        status: string;
        itemCount: number;
        createdAt: string;
      }>;
    }>;
    totalPendingItems: number;
  }> {
    console.log('🔍 [debugQueueStatus] Checking all station queues...');

    const stationQueues = [];
    let totalPendingItems = 0;

    for (const printer of this.printers) {
      try {
        const queueContext = await this.getStationQueueContext(printer.id);
        const { getStationQueue } = await import('@/lib/db/v4/operations/queue-ops');
        const stationQueue = await getStationQueue(printer.id);

        const queueInfo = {
          stationId: printer.id,
          stationName: printer.name,
          totalPendingItems: queueContext.totalPendingItems,
          queueItems: stationQueue.items.map(item => ({
            orderId: item.orderId,
            status: item.status,
            itemCount: item.items.reduce((sum, orderItem) => sum + orderItem.quantity, 0),
            createdAt: item.createdAt
          }))
        };

        stationQueues.push(queueInfo);
        totalPendingItems += queueContext.totalPendingItems;

      } catch (error) {
        console.error(`❌ Error checking queue for station ${printer.id}:`, error);
      }
    }

    console.log('🔍 [debugQueueStatus] Queue summary:', {
      totalStations: stationQueues.length,
      totalPendingItems,
      stationBreakdown: stationQueues.map(s => ({ name: s.stationName, pending: s.totalPendingItems }))
    });

    return { stationQueues, totalPendingItems };
  }

  // 🧹 Reset all queues (for debugging)
  async resetAllQueues(): Promise<void> {
    console.log('🧹 [resetAllQueues] Resetting all kitchen queues...');
    try {
      const { kitchenQueueService } = await import('@/lib/services/kitchen-queue-service');
      await kitchenQueueService.resetAllQueues();
      console.log('✅ [resetAllQueues] All queues have been reset');
    } catch (error) {
      console.error('❌ [resetAllQueues] Error resetting queues:', error);
      throw error;
    }
  }



  // 🎯 TESTING: Simulate order routing to validate printer assignments
  async testOrderRouting(order: Order): Promise<{
    success: boolean;
    routingReport: { itemName: string; categoryId?: string; assignedPrinter?: string; error?: string }[];
    errors: string[];
  }> {
    const routingReport: { itemName: string; categoryId?: string; assignedPrinter?: string; error?: string }[] = [];
    const errors: string[] = [];

    try {
      for (const item of order.items) {
        const categoryName = (item as any).categoryName;

        if (!categoryName) {
          const error = `Item "${item.name}" has no categoryName`;
          routingReport.push({
            itemName: item.name,
            categoryId: undefined,
            error
          });
          errors.push(error);
          continue;
        }

        const assignedPrinter = this.printers.find(printer =>
          printer.assignedCategories.includes(categoryName)
        );

        if (assignedPrinter) {
          routingReport.push({
            itemName: item.name,
            categoryId: categoryName,
            assignedPrinter: assignedPrinter.name
          });
        } else {
          const error = `No printer assigned to category "${categoryName}" for item: ${item.name}`;
          routingReport.push({
            itemName: item.name,
            categoryId: categoryName,
            error
          });
          errors.push(error);
        }
      }

      return {
        success: errors.length === 0,
        routingReport,
        errors
      };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown routing error';
      return {
        success: false,
        routingReport,
        errors: [errorMsg]
      };
    }
  }

  async addOSPrinter(osPrinter: OSPrinterInput): Promise<{ success: boolean; message: string }> {
    const existing = this.printers.find(p => p.id === osPrinter.id);
    if (existing) {
      return { success: false, message: `Printer with ID ${osPrinter.id} already exists.` };
    }

      const newPrinter: PrinterConfig = {
        id: osPrinter.id,
        name: osPrinter.name,
      ipAddress: osPrinter.ipAddress,
      status: osPrinter.status || 'online',
      assignedCategories: [], // No categories assigned by default for OS printers
      type: osPrinter.type || 'thermal', // Default to thermal
      simulated: false, // These are real OS printers
    };

    this.printers.push(newPrinter);
    await this.setPrinters(this.printers);
    return { success: true, message: `Printer ${newPrinter.name} added.` };
  }

  async removePrinter(printerId: string): Promise<{ success: boolean; message: string }> {
    const initialLength = this.printers.length;
    this.printers = this.printers.filter(p => p.id !== printerId);
    if (this.printers.length < initialLength) {
      await this.setPrinters(this.printers);
      return { success: true, message: `Printer ${printerId} removed.` };
    }
    return { success: false, message: `Printer ${printerId} not found.` };
  }

  getPrinterStatus(): { features: PrintingFeatures; printers: PrinterConfig[]; totalPrinters: number } {
    return {
      features: this.printingFeatures,
      printers: this.printers,
      totalPrinters: this.printers.length,
    };
  }

  // Helper: build itemStatuses for multi-barcode system (one entry per item instance)
  private buildBarcodeItemStatuses(order: Order): void {
    // Clear previous statuses related to the same order to avoid duplication if re-printed
    for (const key of Array.from(this.itemStatuses.keys())) {
      if (key.startsWith(`${order.id}-`)) {
        this.itemStatuses.delete(key);
      }
    }

    let globalIndex = 1;
    order.items.forEach(item => {
      for (let i = 0; i < item.quantity; i++) {
        const barcodeId = this.generateSimpleBarcodeId(order.id, globalIndex);
        this.itemStatuses.set(barcodeId, {
          orderId: order.id,
          itemId: barcodeId, // Store the barcode itself for direct lookup
          itemName: item.name,
          status: 'pending',
          createdAt: new Date().toISOString()
        });
        globalIndex++;
      }
    });
  }

  // 🎯 VERIFICATION: Validate print job content and formatting
  async verifyPrintJob(printJob: PrintJob, features: PrintingFeatures): Promise<{
    isValid: boolean;
    issues: string[];
    contentAnalysis: {
      hasOrderNumber: boolean;
      hasItems: boolean;
      hasTimestamp: boolean;
      hasTableInfo: boolean;
      hasBarcodes: boolean;
      hasQueueInfo: boolean;
      lineCount: number;
      estimatedPrintTime: number; // in seconds
    };
  }> {
    const issues: string[] = [];
    const content = printJob.content;
    const lines = content.split('\n');

    // Basic content validation
    const hasOrderNumber = /order\s*#?\d+/i.test(content);
    const hasItems = /qty|quantity|\d+x/i.test(content);
    const hasTimestamp = /\d{1,2}:\d{2}|\d{4}-\d{2}-\d{2}/.test(content);
    const hasTableInfo = /table|table\s*#?\d+/i.test(content);
    const hasBarcodes = content.includes('|||') || content.includes('▌') || content.includes('<img'); // Barcode patterns
    const hasQueueInfo = /queue|pending|station/i.test(content);

    // Feature-specific validation
    if (features.barcodeEnabled && !hasBarcodes) {
      issues.push('Barcode feature enabled but no barcodes found in print job');
    }

    if (!features.barcodeEnabled && hasBarcodes) {
      issues.push('Barcode feature disabled but barcodes found in print job');
    }

    if (features.queueEnabled && printJob.stationName && !hasQueueInfo) {
      issues.push('Queue feature enabled but no queue coordination info found');
    }

    // Content quality checks
    if (!hasOrderNumber) {
      issues.push('Print job missing order number');
    }

    if (!hasItems) {
      issues.push('Print job missing item information');
    }

    if (!hasTimestamp) {
      issues.push('Print job missing timestamp');
    }

    if (lines.length < 5) {
      issues.push('Print job content seems too short');
    }

    if (lines.length > 100) {
      issues.push('Print job content seems excessively long');
    }

    // Estimate print time (rough calculation)
    const estimatedPrintTime = Math.ceil(lines.length / 10); // ~10 lines per second for thermal printers

    return {
      isValid: issues.length === 0,
      issues,
      contentAnalysis: {
        hasOrderNumber,
        hasItems,
        hasTimestamp,
        hasTableInfo,
        hasBarcodes,
        hasQueueInfo,
        lineCount: lines.length,
        estimatedPrintTime
      }
    };
  }

  // 🎯 COMPREHENSIVE FEATURE TEST: Test all aspects of the printing system with different feature combinations
  async comprehensiveFeatureTest(features: PrintingFeatures, testOrder: Order): Promise<{
    success: boolean;
    features: PrintingFeatures;
    printResult: PrintResult;
    validationResult: any;
    routingResult: any;
    printJobVerifications: any[];
    overallScore: number; // 0-100
    recommendations: string[];
  }> {
    const recommendations: string[] = [];
    let score = 0;

    try {
      // Set features
      this.setPrintingFeatures(features);

      // Test validation
      const validationResult = await this.validateCategoryAssignments();
      if (validationResult.success) score += 25;
      else recommendations.push('Fix category assignment issues');

      // Test routing
      const routingResult = await this.testOrderRouting(testOrder);
      if (routingResult.success) score += 25;
      else recommendations.push('Resolve item routing problems');

      // Test printing
      const printResult = await this.printKitchenOrder(testOrder, testOrder.tableId, {});
      if (printResult.success) score += 25;
      else recommendations.push('Fix print job generation issues');

      // Verify print jobs
      const printJobVerifications = [];
      const printJobs = printResult.printJobs || (printResult.printJob ? [printResult.printJob] : []);

      for (const printJob of printJobs) {
        const verification = await this.verifyPrintJob(printJob, features);
        printJobVerifications.push({
          printJob: printJob.title,
          ...verification
        });

        if (verification.isValid) score += Math.floor(25 / printJobs.length);
        else recommendations.push(`Improve print job quality for ${printJob.title}`);
      }

      // Feature-specific recommendations
      if (!features.queueEnabled && !features.barcodeEnabled && printJobs.length > 1) {
        recommendations.push('Consider using queue feature for better coordination with multiple printers');
      }

      if (features.barcodeEnabled && printJobs.length > 1) {
        recommendations.push('Barcode system is working well with multiple stations');
      }

      return {
        success: score >= 75,
        features,
        printResult,
        validationResult,
        routingResult,
        printJobVerifications,
        overallScore: Math.min(100, score),
        recommendations
      };
    } catch (error) {
      return {
        success: false,
        features,
        printResult: { success: false, error: error instanceof Error ? error.message : 'Unknown error' },
        validationResult: null,
        routingResult: null,
        printJobVerifications: [],
        overallScore: 0,
        recommendations: ['Feature test failed - check error logs']
      };
    }
  }

  /**
   * Print kitchen cancel ticket when order is cancelled
   */
  async printKitchenCancel(order: Order, reason?: string): Promise<PrintResult> {
    console.log(`🚫 [printKitchenCancel] Printing cancel ticket for order ${order.id}`);

    try {
      console.log('🚫 [printKitchenCancel] Using same approach as regular kitchen printing...');

      // Use the same multi-ticket generation as regular kitchen printing
      const result = await this.generateMultiTicketPrintJobs(order, order.tableId, {});

      if (!result.success || !result.printJobs || result.printJobs.length === 0) {
        console.warn('🚫 [printKitchenCancel] No print jobs generated for cancellation');
        return { success: false, error: 'No print jobs could be generated for cancellation' };
      }

      const dailySequence = extractDailySequence(order.id);
      const cancelTime = new Date().toLocaleString();

      // Modify each print job to show cancellation content
      const cancelledPrintJobs = result.printJobs.map(job => {
        // Create cancellation content that replaces the original order content
        const cancellationContent = `
          <div style="font-family: 'Courier New', monospace; font-size: 12px; line-height: 1.4; color: #000; padding: 10px;">
            <div style="font-size: 18px; font-weight: bold; color: red; text-align: center; background: #ffebee; border: 3px solid #f44336; padding: 10px; margin: 5px 0;">
              🚫 ORDER CANCELLED 🚫
            </div>
            <div style="font-size: 16px; font-weight: bold; text-align: center; background: #ffcdd2; padding: 8px; margin: 5px 0; color: #c62828;">
              STOP PREPARATION IMMEDIATELY
            </div>
            <div style="text-align: center; font-size: 14px; font-weight: bold; margin: 10px 0;">
              ORDER #${dailySequence}
            </div>
            <div style="text-align: center; font-size: 10px; color: #666; margin-bottom: 15px;">
              ${cancelTime}
            </div>

            <div style="background: #ffebee; border: 2px solid #f44336; padding: 10px; margin: 10px 0; text-align: center;">
              <div style="font-size: 14px; font-weight: bold; color: #d32f2f; margin-bottom: 5px;">
                CANCEL ALL ITEMS BELOW
              </div>
              <div style="font-size: 12px; color: #c62828;">
                This order has been cancelled - stop all preparation
              </div>
            </div>

            ${reason ? `
            <div style="margin: 10px 0; padding: 8px; background: #fff3e0; border: 1px solid #ff9800;">
              <div style="font-size: 12px; font-weight: bold; margin-bottom: 5px; color: #e65100;">CANCELLATION REASON:</div>
              <div style="font-size: 11px; color: #bf360c;">${reason}</div>
            </div>
            ` : ''}

            <div style="margin: 10px 0; padding: 8px; background: #f5f5f5; border: 1px solid #ccc;">
              <div style="font-size: 12px; font-weight: bold; margin-bottom: 8px; border-bottom: 1px solid #999; padding-bottom: 3px;">
                ORIGINAL ITEMS (CANCELLED):
              </div>
              ${order.items.map(item =>
                `<div style="font-size: 11px; margin-bottom: 3px; color: #666; text-decoration: line-through;">
                  • ${item.quantity}x ${item.name} ${item.size ? `(${item.size})` : ''}
                  ${item.addons && item.addons.length > 0 ? `<br>&nbsp;&nbsp;+ ${item.addons.map(a => a.name).join(', ')}` : ''}
                  ${item.notes ? `<br>&nbsp;&nbsp;Notes: ${item.notes}` : ''}
                </div>`
              ).join('')}
            </div>

            <div style="margin-top: 20px; padding: 15px; background: #ffebee; border: 3px solid #f44336; text-align: center;">
              <div style="font-size: 18px; font-weight: bold; color: #d32f2f; margin-bottom: 8px;">
                🚫 CANCELLATION ALERT 🚫
              </div>
              <div style="font-size: 14px; font-weight: bold; color: #c62828; margin-bottom: 5px;">
                THIS ORDER HAS BEEN CANCELLED
              </div>
              <div style="font-size: 12px; color: #b71c1c; margin-bottom: 5px;">
                Stop all preparation immediately
              </div>
              <div style="font-size: 10px; color: #6c757d;">
                Time: ${cancelTime}
              </div>
            </div>
          </div>
        `;

        return {
          ...job,
          title: `🚫 CANCELLED - ${job.title}`,
          content: cancellationContent
        };
      });

      // Execute the cancelled print jobs using the same execution path
      const printResults: any[] = [];
      let successfulPrints = 0;

      for (const printJob of cancelledPrintJobs) {
        try {
          const htmlJob = {
            id: `cancel_${Date.now()}_${Math.random()}`,
            title: printJob.title,
            content: printJob.content,
            type: 'kitchen' as const
          };

          const printResult = await simpleHTMLPrintService.print(htmlJob);
          printResults.push({
            ...printResult,
            printerId: printJob.printerId,
            stationName: printJob.stationName || 'Unknown'
          });

          if (printResult.success) {
            successfulPrints++;
          }
        } catch (error) {
          console.error(`🚫 [printKitchenCancel] Failed to execute cancel print:`, error);
          printResults.push({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown print error'
          });
        }
      }

      const printSuccessRate = cancelledPrintJobs.length > 0 ? (successfulPrints / cancelledPrintJobs.length) * 100 : 0;
      const actuallyPrinted = successfulPrints > 0;

      console.log(`🚫 [printKitchenCancel] Cancel print summary: ${successfulPrints}/${cancelledPrintJobs.length} successful`);

      return {
        success: true,
        printJobs: cancelledPrintJobs,
        showPreview: shouldShowPrintPreview(actuallyPrinted, cancelledPrintJobs.length > 0),
        actuallyPrinted,
        printResults,
        printSuccessRate
      };

    } catch (error) {
      console.error(`🚫 [printKitchenCancel] Error printing cancel ticket:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Print kitchen update ticket when order items are modified
   */
  async printKitchenUpdate(order: Order, updateType: 'items_changed' | 'customer_changed' = 'items_changed'): Promise<PrintResult> {
    console.log(`🔄 [printKitchenUpdate] Printing update ticket for order ${order.id}, type: ${updateType}`);

    try {
      const dailySequence = extractDailySequence(order.id);
      const updateTime = new Date().toLocaleString();

      // For item changes, generate kitchen print jobs and add modification banner
      if (updateType === 'items_changed') {
        console.log('🔄 [printKitchenUpdate] Generating kitchen print jobs with modification banner...');

        // Initialize printing features and printers
        this.printingFeatures = this.getPrintingFeatures();
        this.printers = await this.getPrinters();

        // Generate normal kitchen print jobs (but don't execute them yet)
        const kitchenJobs = await this.generateMultiTicketPrintJobs(order, undefined, {});

        if (!kitchenJobs.success || !kitchenJobs.printJobs || kitchenJobs.printJobs.length === 0) {
          console.warn('🔄 [printKitchenUpdate] Failed to generate kitchen print jobs');
          return { success: false, error: 'Failed to generate kitchen print jobs' };
        }

        // Add modification banner to each kitchen print job
        const modifiedJobs = kitchenJobs.printJobs.map(job => ({
          ...job,
          content: `<div style="font-family: 'Courier New', monospace; text-align: center; font-size: 9px; margin: 3px 0 8px 0; line-height: 1.2; white-space: pre;">
┌───────────────────────┐
│      MODIFICATION     │
└───────────────────────┘
</div>${job.content}`
        }));

        // Execute all modified print jobs
        const printResults: any[] = [];
        let actuallyPrinted = false;

        for (const printJob of modifiedJobs) {
          try {
            const htmlJob: SimplePrintJob = {
              id: simpleHTMLPrintService.generateJobId(),
              title: printJob.title,
              content: printJob.content,
              type: printJob.type as 'kitchen' | 'receipt' | 'report' | 'expo'
            };

            const printResult = await simpleHTMLPrintService.print(htmlJob);
            printResults.push(printResult);

            if (printResult.success) {
              actuallyPrinted = true;
            }

            console.log(`🔄 [printKitchenUpdate] Print job ${htmlJob.id} result:`, {
              success: printResult.success,
              method: printResult.method,
              error: printResult.error
            });

          } catch (error) {
            console.error(`🔄 [printKitchenUpdate] Failed to execute print job:`, error);
            printResults.push({
              success: false,
              error: error instanceof Error ? error.message : 'Unknown print error'
            });
          }
        }

        const successfulPrints = printResults.filter(r => r.success).length;
        const totalPrints = printResults.length;
        const printSuccessRate = totalPrints > 0 ? (successfulPrints / totalPrints) * 100 : 0;

        return {
          success: true,
          printJobs: modifiedJobs,
          showPreview: shouldShowPrintPreview(actuallyPrinted, modifiedJobs.length > 0),
          actuallyPrinted,
          printResults,
          printSuccessRate
        };

      } else {
        // For customer changes, send simple notification to all enabled printers
        const enabledPrinters = this.printers.filter(printer => printer.enabled);
        if (enabledPrinters.length === 0) {
          console.warn('🔄 [printKitchenUpdate] No enabled printers found');
          return { success: false, error: 'No enabled printers found' };
        }

        const content = `
          <div class="text-base font-mono leading-tight">
            <div class="text-center font-bold text-xl mb-4 border-b-2 border-blue-600 pb-2 bg-blue-100">📝 ORDER UPDATED</div>
            <div class="text-center font-bold text-lg mb-2 bg-blue-200 p-2">CUSTOMER INFO CHANGED</div>
            <div class="text-center font-bold text-lg mb-2">ORDER #${dailySequence}</div>
            <div class="text-center text-sm mb-4">${updateTime}</div>
            <hr class="my-2">

            <div class="mb-4 bg-blue-50 p-3 border-2 border-blue-500">
              <div class="font-bold mb-2 text-center text-blue-700">CUSTOMER INFORMATION UPDATED</div>
              <div class="text-sm text-center">Order delivery/customer details have been modified.</div>
              <div class="text-sm text-center font-bold">Check updated order details in system.</div>
            </div>

            ${order.customer ? `
            <div class="mb-4">
              <div class="font-bold mb-2 border-b">CUSTOMER INFO:</div>
              <div class="text-sm">Name: ${order.customer.name || 'N/A'}</div>
              ${order.customer.phone ? `<div class="text-sm">Phone: ${order.customer.phone}</div>` : ''}
              ${order.customer.address ? `<div class="text-sm">Address: ${order.customer.address}</div>` : ''}
            </div>
            ` : ''}

            <hr class="my-2">
            <div class="text-center font-bold text-lg bg-blue-600 text-white p-3">
              CONTINUE PREPARATION
            </div>
            <div class="text-center text-xs mt-2">PRINTED: ${updateTime}</div>
          </div>
        `;

        const printJobs: PrintJob[] = [];
        const printResults: any[] = [];
        let successfulPrints = 0;

        for (const printer of enabledPrinters) {
          const updateJob: PrintJob = {
            title: `UPDATE ORDER #${dailySequence}`,
            content,
            type: 'kitchen',
            printerId: printer.id,
            stationName: printer.name
          };

          printJobs.push(updateJob);

          // Execute print
          try {
            const htmlJob: SimplePrintJob = {
              id: simpleHTMLPrintService.generateJobId(),
              title: updateJob.title,
              content: updateJob.content,
              type: 'kitchen'
            };

            const printResult = await simpleHTMLPrintService.print(htmlJob);
            printResults.push({
              ...printResult,
              printerId: printer.id,
              printerName: printer.name
            });

            if (printResult.success) {
              successfulPrints++;
            }

          } catch (error) {
            console.error(`🔄 [printKitchenUpdate] Failed to execute update print for ${printer.name}:`, error);
            printResults.push({
              success: false,
              error: error instanceof Error ? error.message : 'Unknown print error',
              printerId: printer.id,
              printerName: printer.name
            });
          }
        }

        const printSuccessRate = printJobs.length > 0 ? (successfulPrints / printJobs.length) * 100 : 0;
        const actuallyPrinted = successfulPrints > 0;

        console.log(`🔄 [printKitchenUpdate] Update print summary: ${successfulPrints}/${printJobs.length} successful`);

        return {
          success: true,
          printJobs: printJobs,
          showPreview: shouldShowPrintPreview(actuallyPrinted, printJobs.length > 0),
          actuallyPrinted,
          printResults,
          printSuccessRate
        };
      }

    } catch (error) {
      console.error(`🔄 [printKitchenUpdate] Error printing update ticket:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

}

// Export singleton instance
export const kitchenPrintService = new KitchenPrintService();
export default kitchenPrintService;
