/**
 * Simple HTML Print Service
 * 
 * Dead simple printing that just works - uses HTML + window.print()
 * No complex Electron nonsense, no silent failures, just reliable printing.
 */

export interface SimplePrintJob {
  id: string;
  title: string;
  content: string;
  type: 'kitchen' | 'receipt' | 'report' | 'expo';
}

export interface PrintResult {
  success: boolean;
  jobId: string;
  method: 'html-print';
  error?: string;
}

class SimpleHTMLPrintService {
  
  /**
   * Print using appropriate method based on environment
   * - Production Electron: Silent print via IPC (no preview)
   * - Development: HTML window with preview
   * - Web: HTML window with preview
   */
  async print(job: SimplePrintJob): Promise<PrintResult> {
    console.log(`🖨️ [HTML-PRINT] Printing job: ${job.id} (${job.type})`);

    try {
      // Check if we're in production Electron - use silent printing
      const isElectron = typeof window !== 'undefined' && (window as any).electronAPI;
      const isProduction = process.env.NODE_ENV === 'production';

      if (isElectron && isProduction) {
        console.log(`🖨️ [HTML-PRINT] Using silent print for production Electron`);

        try {
          const result = await (window as any).electronAPI.invoke('silent-print', {
            title: job.title,
            content: job.content
          });

          return {
            success: result.success,
            jobId: job.id,
            method: 'html-print',
            error: result.error
          };
        } catch (error) {
          console.error(`🖨️ [HTML-PRINT] Silent print failed, falling back to preview:`, error);
          // Fall through to preview method
        }
      }

      // Development or web - show preview window
      console.log(`🖨️ [HTML-PRINT] Using preview window for development/web`);
      const printWindow = this.createPrintWindow(job);

      if (!printWindow) {
        return {
          success: false,
          jobId: job.id,
          method: 'html-print',
          error: 'Could not open print window - please allow pop-ups'
        };
      }

      this.writePrintContent(printWindow, job);
      this.setupPrintHandlers(printWindow);

      return {
        success: true,
        jobId: job.id,
        method: 'html-print'
      };

    } catch (error) {
      console.error(`🖨️ [HTML-PRINT] Failed to print job ${job.id}:`, error);
      return {
        success: false,
        jobId: job.id,
        method: 'html-print',
        error: error instanceof Error ? error.message : 'Unknown print error'
      };
    }
  }

  private createPrintWindow(job: SimplePrintJob): Window | null {
    return window.open('', '_blank', 'width=400,height=600,scrollbars=yes,resizable=yes');
  }

  private writePrintContent(printWindow: Window, job: SimplePrintJob): void {
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${job.title}</title>
        <style>
          @media print {
            @page { margin: 0; }
            body { margin: 0; padding: 0; font-family: 'Courier New', monospace; font-size: 18px; line-height: 0.9; }
            .no-print { display: none !important; }
          }
          body { font-family: 'Courier New', monospace; margin: 0; padding: 10px; background: #f5f5f5; display: flex; justify-content: center; }
          .container { max-width: 300px; background: white; border: 1px solid #ddd; padding: 10px; font-size: 20px; }
          .controls { text-align: center; margin-top: 15px; padding: 10px; background: #f0f0f0; }
          .btn { background: #007cba; color: white; border: none; padding: 8px 15px; margin: 0 5px; cursor: pointer; }
          .btn:hover { background: #005a87; }
          .close { background: #666; }
          .close:hover { background: #444; }
        </style>
      </head>
      <body>
        <div class="container">
          ${job.content}
        </div>
        <div class="controls no-print">
          <button class="btn" onclick="window.print()">🖨️ Print</button>
          <button class="btn close" onclick="window.close()">✖️ Close</button>
        </div>
      </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
  }

  private setupPrintHandlers(printWindow: Window): void {
    printWindow.addEventListener('load', () => {
      setTimeout(() => {
        // Uncomment this line to auto-print immediately
        // printWindow.print();
      }, 1000);
    });

    printWindow.addEventListener('afterprint', () => {
      setTimeout(() => {
        printWindow.close();
      }, 1000);
    });

    printWindow.focus();
  }

  generateJobId(): string {
    return `html-print-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  async testPrint(): Promise<PrintResult> {
    const testJob: SimplePrintJob = {
      id: this.generateJobId(),
      title: 'Print Test',
      type: 'receipt',
      content: `
=====================
PRINT TEST
=====================

Date: ${new Date().toLocaleDateString()}
Time: ${new Date().toLocaleTimeString()}

This is a test print.
If you can see this clearly,
your printer setup is working!

✅ HTML Print System
✅ Window.print() Method
✅ Cross-platform Compatible

Test completed successfully.

=====================
      `
    };

    return this.print(testJob);
  }
}

// Export singleton instance
let simpleHTMLPrintServiceInstance: SimpleHTMLPrintService | null = null;

export const simpleHTMLPrintService = (() => {
  if (typeof window !== 'undefined' && !simpleHTMLPrintServiceInstance) {
    simpleHTMLPrintServiceInstance = new SimpleHTMLPrintService();
  }
  return simpleHTMLPrintServiceInstance || {
    print: async (job: SimplePrintJob) => ({
      success: false,
      jobId: job.id,
      method: 'html-print' as const,
      error: 'Printing not available during SSR'
    }),
    testPrint: async () => ({
      success: false,
      jobId: 'test',
      method: 'html-print' as const,
      error: 'Testing not available during SSR'
    }),
    generateJobId: () => 'dummy-id'
  } as SimpleHTMLPrintService;
})();