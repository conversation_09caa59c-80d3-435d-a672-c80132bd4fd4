/**
 * Leader Sync Auto-Initialization 👑
 * 
 * This module automatically starts the leader sync system when the app loads.
 * Import this in your main app component or layout to enable background sync.
 */

import { leaderSyncService } from './leader-sync-service';

let isInitialized = false;

/**
 * Initialize leader sync with default configuration
 * This should be called once when the app starts
 */
export async function initializeLeaderSync(): Promise<void> {
  if (isInitialized) {
    console.log('👑 [SyncAutoInit] Leader sync already initialized');
    return;
  }

  console.log('👑 [SyncAutoInit] Initializing leader sync system...');

  try {
    await leaderSyncService.initialize({
      autoStart: true,
      localDiscoveryInterval: 30000,
      localReconnectInterval: 60000,
      internetDiscoveryInterval: 120000,
      internetReconnectInterval: 180000,
      maxReconnectAttempts: 5,
      preferLocalSync: true,
      vpsBaseUrl: 'https://bistro.icu',
      authToken: '',
      deviceId: `device-${Date.now()}`,
      deviceType: 'mobile'
    });

    isInitialized = true;
    console.log('👑 [SyncAutoInit] Leader sync initialized successfully');
  } catch (error) {
    console.error('👑 [SyncAutoInit] Failed to initialize leader sync:', error);
  }
}

/**
 * Auto-initialize when this module is imported (if in browser)
 */
if (typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeLeaderSync);
  } else {
    // DOM is already ready
    setTimeout(initializeLeaderSync, 100);
  }
}

// Backward compatibility
export const initializeAutonomousSync = initializeLeaderSync;
export { leaderSyncService };
export { leaderSyncService as autonomousSyncManager };