"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BackupService = void 0;
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const electron_1 = require("electron");
class BackupService {
    constructor() {
        this.couchUrl = '*********************************';
        const userDataPath = electron_1.app.getPath('userData');
        this.backupDir = path_1.default.join(userDataPath, 'backups');
        this.contextFile = path_1.default.join(this.backupDir, 'backup-context.json');
    }
    async ensureBackupDir() {
        try {
            await promises_1.default.access(this.backupDir);
        }
        catch {
            await promises_1.default.mkdir(this.backupDir, { recursive: true });
        }
    }
    async getContext() {
        try {
            const data = await promises_1.default.readFile(this.contextFile, 'utf8');
            return JSON.parse(data);
        }
        catch {
            return {
                lastBackupTime: 0,
                lastCheckTime: 0,
                consecutiveFailures: 0
            };
        }
    }
    async saveContext(context) {
        await this.ensureBackupDir();
        await promises_1.default.writeFile(this.contextFile, JSON.stringify(context, null, 2));
    }
    async isBackupNeeded() {
        const context = await this.getContext();
        const now = Date.now();
        const twentyFourHours = 24 * 60 * 60 * 1000;
        return (now - context.lastBackupTime) > twentyFourHours;
    }
    async createBackup(restaurantId) {
        try {
            await this.ensureBackupDir();
            const dbName = `resto-${restaurantId}`;
            const timestamp = Date.now();
            const dateStr = new Date(timestamp).toISOString().split('T')[0];
            const backupFileName = `${dbName}_${dateStr}_${timestamp}.json`;
            const backupPath = path_1.default.join(this.backupDir, backupFileName);
            // Fetch all docs from CouchDB
            const response = await fetch(`${this.couchUrl}/${dbName}/_all_docs?include_docs=true`);
            if (!response.ok) {
                throw new Error(`CouchDB error: ${response.status} ${response.statusText}`);
            }
            const data = await response.json();
            // Save backup file
            await promises_1.default.writeFile(backupPath, JSON.stringify(data, null, 2));
            // Get file size
            const stats = await promises_1.default.stat(backupPath);
            const metadata = {
                timestamp,
                date: dateStr,
                dbName,
                size: stats.size,
                docCount: data.rows?.length || 0
            };
            // Update context
            const context = await this.getContext();
            context.lastBackupTime = timestamp;
            context.lastCheckTime = timestamp;
            context.consecutiveFailures = 0;
            await this.saveContext(context);
            console.log(`✅ Backup created: ${backupFileName} (${stats.size} bytes, ${metadata.docCount} docs)`);
            return { success: true, metadata };
        }
        catch (error) {
            console.error('❌ Backup failed:', error);
            // Update failure count
            const context = await this.getContext();
            context.consecutiveFailures++;
            context.lastCheckTime = Date.now();
            await this.saveContext(context);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    async listLocalBackups() {
        try {
            await this.ensureBackupDir();
            const files = await promises_1.default.readdir(this.backupDir);
            const backupFiles = files.filter(f => f.endsWith('.json') && f !== 'backup-context.json');
            const backups = [];
            for (const file of backupFiles) {
                try {
                    const filePath = path_1.default.join(this.backupDir, file);
                    const stats = await promises_1.default.stat(filePath);
                    // Parse filename: resto-uuid_2024-08-12_1723456789.json
                    const match = file.match(/^(resto-[^_]+)_(\d{4}-\d{2}-\d{2})_(\d+)\.json$/);
                    if (match) {
                        const [, dbName, date, timestampStr] = match;
                        backups.push({
                            timestamp: parseInt(timestampStr),
                            date,
                            dbName,
                            size: stats.size,
                            docCount: 0 // We'd need to read file to get this
                        });
                    }
                }
                catch (error) {
                    console.warn(`⚠️ Skipping invalid backup file: ${file}`);
                }
            }
            return backups.sort((a, b) => b.timestamp - a.timestamp);
        }
        catch {
            return [];
        }
    }
    async cleanupOldBackups() {
        try {
            const backups = await this.listLocalBackups();
            const now = Date.now();
            const toDelete = [];
            backups.forEach((backup, index) => {
                const ageInDays = (now - backup.timestamp) / (24 * 60 * 60 * 1000);
                let shouldKeep = false;
                if (ageInDays <= 3) {
                    // Keep all from last 3 days
                    shouldKeep = true;
                }
                else if (ageInDays <= 7) {
                    // Keep every 2nd backup (days 4-7)
                    shouldKeep = index % 2 === 0;
                }
                else if (ageInDays <= 14) {
                    // Keep every 3rd backup (days 8-14)
                    shouldKeep = index % 3 === 0;
                }
                else if (ageInDays <= 30) {
                    // Keep weekly backups (days 15-30)
                    shouldKeep = index % 7 === 0;
                }
                // Delete everything older than 30 days
                if (!shouldKeep) {
                    const fileName = `${backup.dbName}_${backup.date}_${backup.timestamp}.json`;
                    toDelete.push(fileName);
                }
            });
            // Delete files
            for (const fileName of toDelete) {
                try {
                    await promises_1.default.unlink(path_1.default.join(this.backupDir, fileName));
                    console.log(`🗑️ Deleted old backup: ${fileName}`);
                }
                catch (error) {
                    console.warn(`⚠️ Failed to delete backup ${fileName}:`, error);
                }
            }
        }
        catch (error) {
            console.error('❌ Backup cleanup failed:', error);
        }
    }
    async restoreFromBackup(backupTimestamp, restaurantId) {
        try {
            const dbName = `resto-${restaurantId}`;
            const backups = await this.listLocalBackups();
            const backup = backups.find(b => b.timestamp === backupTimestamp);
            if (!backup) {
                return { success: false, error: 'Backup not found' };
            }
            const backupFileName = `${backup.dbName}_${backup.date}_${backup.timestamp}.json`;
            const backupPath = path_1.default.join(this.backupDir, backupFileName);
            // Read backup data
            const backupData = JSON.parse(await promises_1.default.readFile(backupPath, 'utf8'));
            // Create safety backup of current DB
            const safetyResult = await this.createBackup(restaurantId);
            if (!safetyResult.success) {
                console.warn('⚠️ Failed to create safety backup before restore');
            }
            // Delete current database
            await fetch(`${this.couchUrl}/${dbName}`, { method: 'DELETE' });
            // Recreate database
            await fetch(`${this.couchUrl}/${dbName}`, { method: 'PUT' });
            // Restore documents
            const docs = backupData.rows?.map((row) => row.doc).filter((doc) => doc && !doc._id.startsWith('_design')) || [];
            if (docs.length > 0) {
                const bulkResponse = await fetch(`${this.couchUrl}/${dbName}/_bulk_docs`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ docs })
                });
                if (!bulkResponse.ok) {
                    throw new Error(`Bulk restore failed: ${bulkResponse.status}`);
                }
            }
            console.log(`✅ Database restored from backup: ${backupFileName} (${docs.length} docs)`);
            return { success: true };
        }
        catch (error) {
            console.error('❌ Restore failed:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
}
exports.BackupService = BackupService;
//# sourceMappingURL=backup-service.js.map