// Stub file for backward compatibility
// The original complex Google Drive service has been replaced with a simple backup system
// See google-drive-simple.ts for the new implementation

export class GoogleDriveService {
  constructor() {
    console.warn('⚠️ GoogleDriveService is deprecated. Use the new backup system in Settings > Backup');
  }

  async initializeFromMongoDB(): Promise<boolean> {
    console.warn('⚠️ GoogleDriveService is deprecated. Use the new backup system in Settings > Backup');
    return false;
  }

  isInitialized(): boolean {
    return false;
  }

  reset(): void {
    // No-op
  }
}

// Export types for compatibility
export interface GoogleDriveConfig {
  type: 'oauth' | 'service_account';
}

export interface GoogleDriveFile {
  id: string;
  name: string;
  mimeType: string;
}

export interface UploadResult {
  success: boolean;
  error?: string;
}

export interface DownloadResult {
  success: boolean;
  error?: string;
}

export interface TestResult {
  success: boolean;
  error?: string;
}