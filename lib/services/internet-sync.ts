import { mainDbInstance } from '@/lib/db/v4/core/db-instance';
import { getRestaurantDbName } from '@/lib/db/db-utils';
import { refreshAccessToken, isTokenExpiringSoon } from '@/lib/auth/new-auth-service';
import { validateSecureUrl } from '@/lib/utils/ssl-validation';

interface InternetSyncConfig {
  vpsBaseUrl: string;
  authToken: string;
  refreshToken?: string;
  deviceId: string;
}

interface DiscoveredPeer {
  id: string;
  deviceType: string;
  ipAddress: string;
  couchdbPort: number;
  hostname: string;
  platform: string;
  lastSeen: Date;
}

interface InternetSyncStatus {
  connected: boolean;
  syncing: boolean;
  lastSync?: Date;
  error?: string;
  docsReceived: number;
  docsSent: number;
  proxyUrl?: string;
}

class InternetSyncService {
  private config: InternetSyncConfig | null = null;
  private syncHandler: any = null;
  private currentPeer: DiscoveredPeer | null = null;
  private status: InternetSyncStatus = {
    connected: false,
    syncing: false,
    docsReceived: 0,
    docsSent: 0
  };
  private listeners: ((status: InternetSyncStatus) => void)[] = [];
  private tokenRefreshInterval: NodeJS.Timeout | null = null;
  private heartbeatInterval: NodeJS.Timeout | null = null;

  configure(config: InternetSyncConfig): void {
    // Validate SSL security
    const sslValidation = validateSecureUrl(config.vpsBaseUrl);
    if (!sslValidation.isValid) {
      throw new Error(`SSL validation failed: ${sslValidation.error}`);
    }
    
    if (sslValidation.warnings) {
      sslValidation.warnings.forEach(warning => console.warn('⚠️ SSL Warning:', warning));
    }

    this.config = config;
    this.startTokenRefresh();
    this.startHeartbeat();
    console.log('✅ Internet sync configured with secure connection');
  }

  private startTokenRefresh(): void {
    this.stopTokenRefresh();
    
    // Check token every 4 minutes (tokens expire after 24h, refresh if expiring in 5min)
    this.tokenRefreshInterval = setInterval(async () => {
      await this.refreshTokenIfNeeded();
    }, 4 * 60 * 1000);
  }

  private stopTokenRefresh(): void {
    if (this.tokenRefreshInterval) {
      clearInterval(this.tokenRefreshInterval);
      this.tokenRefreshInterval = null;
    }
  }

  private startHeartbeat(): void {
    this.stopHeartbeat();
    
    // Send heartbeat every 2 minutes to keep device alive in registry
    this.heartbeatInterval = setInterval(async () => {
      await this.sendHeartbeat();
    }, 2 * 60 * 1000);
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  private async sendHeartbeat(): Promise<void> {
    if (!this.config) return;

    try {
      const isMobile = typeof window !== 'undefined' && (window as any).Capacitor;
      
      if (isMobile) {
        const { CapacitorHttp } = await import('@capacitor/core');
        await CapacitorHttp.post({
          url: `${this.config.vpsBaseUrl}/api/sync/heartbeat`,
          headers: {
            'Authorization': `Bearer ${this.getAuthToken()}`,
            'Content-Type': 'application/json'
          },
          data: {
            deviceId: this.config.deviceId
          }
        });
      } else {
        await fetch(`${this.config.vpsBaseUrl}/api/sync/heartbeat`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.getAuthToken()}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            deviceId: this.config.deviceId
          })
        });
      }
      
      console.log('💓 Heartbeat sent successfully');
    } catch (error) {
      console.warn('⚠️ Heartbeat failed:', error);
      // Don't throw error - heartbeat failures shouldn't break sync
    }
  }

  private async refreshTokenIfNeeded(): Promise<void> {
    if (!this.config || !this.config.refreshToken) return;

    try {
      if (isTokenExpiringSoon(this.config.authToken)) {
        console.log('🔄 Refreshing access token...');
        
        // Retry token refresh up to 3 times with exponential backoff
        let retryCount = 0;
        const maxRetries = 3;
        
        while (retryCount < maxRetries) {
          try {
            const newToken = await refreshAccessToken(this.config.refreshToken);
            
            if (newToken) {
              this.config.authToken = newToken;
              console.log('✅ Access token refreshed successfully');
              
              // Clear any previous error
              if (this.status.error?.includes('Authentication')) {
                this.status.error = undefined;
                this.emitStatusUpdate();
              }
              return;
            }
          } catch (refreshError) {
            retryCount++;
            console.warn(`⚠️ Token refresh attempt ${retryCount} failed:`, refreshError);
            
            if (retryCount < maxRetries) {
              // Wait with exponential backoff: 2s, 4s, 8s
              const delay = Math.pow(2, retryCount) * 1000;
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }
        }
        
        // All retries failed
        console.error('❌ Failed to refresh access token after all retries');
        this.status.error = 'Authentication token refresh failed - please re-authenticate';
        this.emitStatusUpdate();
      }
    } catch (error) {
      console.error('❌ Token refresh error:', error);
      this.status.error = 'Authentication error - please check connection';
      this.emitStatusUpdate();
    }
  }

  private getAuthToken(): string {
    if (!this.config) throw new Error('Service not configured');
    return this.config.authToken;
  }

  private emitStatusUpdate(): void {
    this.listeners.forEach(listener => listener({ ...this.status }));
  }

  onStatusChange(listener: (status: InternetSyncStatus) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) this.listeners.splice(index, 1);
    };
  }

  async discoverPeers(): Promise<DiscoveredPeer[]> {
    if (!this.config) {
      throw new Error('Internet sync service not configured');
    }

    // Retry discovery up to 3 times with exponential backoff
    let retryCount = 0;
    const maxRetries = 3;
    
    while (retryCount < maxRetries) {
      try {
        const isMobile = typeof window !== 'undefined' && (window as any).Capacitor;
        const isWebBrowser = typeof window !== 'undefined' && !isMobile && !(window as any).electronAPI;
        
        if (isMobile) {
          const { CapacitorHttp } = await import('@capacitor/core');
          const response = await CapacitorHttp.get({
            url: `${this.config.vpsBaseUrl}/api/sync/discover-peers`,
            headers: {
              'Authorization': `Bearer ${this.getAuthToken()}`,
              'Accept': 'application/json'
            }
          });

          if (response.status !== 200) {
            throw new Error(`Discovery failed: ${response.status}`);
          }

          const data = response.data;
          
          // 👑 Handle leader response format
          if (data.leader) {
            const leader = {
              ...data.leader,
              lastSeen: new Date(data.leader.lastSeen)
            };
            return [leader];
          }

          console.log(`ℹ️ No internet leader available`);
          return [];
        } else {
          // Web browser or Electron - enforce HTTPS to bistro.icu only
          const url = new URL(`${this.config.vpsBaseUrl}/api/sync/discover-peers`);
          if (url.protocol !== 'https:') {
            throw new Error('Sync server must use HTTPS for security');
          }
          if (url.hostname !== 'bistro.icu') {
            throw new Error('Internet sync only allowed to bistro.icu');
          }

          const response = await fetch(url.toString(), {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${this.getAuthToken()}`,
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            },
            mode: isWebBrowser ? 'cors' : 'no-cors'
          });

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Discovery failed: ${response.status} - ${errorText}`);
          }

          const data = await response.json();
          
          // 👑 Handle leader response format
          if (data.leader) {
            const leader = {
              ...data.leader,
              lastSeen: new Date(data.leader.lastSeen)
            };
            return [leader];
          }

          console.log(`ℹ️ No internet leader available`);
          return [];
        }
        
      } catch (error: any) {
        retryCount++;
        console.warn(`⚠️ Peer discovery attempt ${retryCount} failed:`, error);
        
        // Don't retry for authentication errors
        if (error.message?.includes('401') || error.message?.includes('403')) {
          throw error;
        }
        
        if (retryCount < maxRetries) {
          // Wait with exponential backoff: 1s, 2s, 4s
          const delay = Math.pow(2, retryCount - 1) * 1000;
          console.log(`⏱️ Retrying peer discovery in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          // All retries failed - provide specific error messages
          console.error('❌ Internet peer discovery failed after all retries:', error);
          
          if (error.message?.includes('CORS')) {
            throw new Error('Network access blocked - check CORS configuration');
          } else if (error.message?.includes('Failed to fetch')) {
            throw new Error('Cannot reach sync server - check internet connection');
          }
          
          throw new Error(`Peer discovery failed: ${error.message}`);
        }
      }
    }
    
    // If we get here, all retries failed
    throw new Error('All discovery attempts failed');
  }

  async startSync(peer: DiscoveredPeer): Promise<boolean> {
    if (!this.config) {
      throw new Error('Internet sync service not configured');
    }

    console.log(`🌐 Starting internet sync with ${peer.hostname}...`);

    try {
      await this.waitForDatabase();
      
      const localDb = mainDbInstance.getPouchDBForSync();
      if (!localDb) {
        throw new Error('Local database not available for sync');
      }

      const restaurantId = mainDbInstance.getCurrentRestaurantId();
      if (!restaurantId) {
        throw new Error('Restaurant ID not available');
      }

      const dbName = getRestaurantDbName(restaurantId);

      // Construct proxy URL through VPS with HTTPS enforcement
      const proxyUrl = new URL(`/api/sync/proxy/${peer.id}/${dbName}`, this.config.vpsBaseUrl);
      
      // Enforce HTTPS to bistro.icu only
      if (proxyUrl.protocol !== 'https:') {
        throw new Error('Proxy URL must use HTTPS for security');
      }
      if (proxyUrl.hostname !== 'bistro.icu') {
        throw new Error('Internet sync only allowed to bistro.icu');
      }
      
      console.log(`📡 Internet sync via proxy: ${proxyUrl.toString()}`);

      this.currentPeer = peer;
      this.status = {
        connected: true,
        syncing: true,
        docsReceived: 0,
        docsSent: 0,
        proxyUrl: proxyUrl.toString()
      };
      this.emitStatusUpdate();

      // PouchDB sync with custom headers for proxy authentication
      this.syncHandler = localDb.sync(proxyUrl.toString(), {
        live: true,
        retry: true,
        ajax: {
          headers: {
            'Authorization': `Bearer ${this.getAuthToken()}`
          }
        }
      })
        .on('change', (info: any) => {
          console.log('🌐 Internet sync change:', info);
          if (info.direction === 'pull') {
            this.status.docsReceived += info.change?.docs_read || 0;
            
            // Emit database change events
            if (info.change?.docs && typeof window !== 'undefined') {
              info.change.docs.forEach((doc: any) => {
                window.dispatchEvent(new CustomEvent('pouchdb-change', {
                  detail: {
                    doc,
                    isLocal: false,
                    direction: 'pull',
                    source: 'internet'
                  }
                }));
              });
            }
          } else if (info.direction === 'push') {
            this.status.docsSent += info.change?.docs_written || 0;
          }
          this.status.lastSync = new Date();
          this.emitStatusUpdate();
        })
        .on('paused', () => {
          console.log('⏸️ Internet sync paused');
          this.status.syncing = false;
          this.emitStatusUpdate();
        })
        .on('active', () => {
          console.log('▶️ Internet sync active');
          this.status.syncing = true;
          this.emitStatusUpdate();
        })
        .on('denied', (err: any) => {
          console.error('❌ Internet sync denied:', err);
          this.status.error = `Access denied: ${err.message || err.reason || 'Authentication failed'}`;
          this.emitStatusUpdate();
        })
        .on('complete', (info: any) => {
          console.log('✅ Internet sync complete:', info);
          this.status.syncing = false;
          this.status.lastSync = new Date();
          this.emitStatusUpdate();
        })
        .on('error', (err: any) => {
          console.error('❌ Internet sync error:', err);
          
          let errorMessage = err.message || err.reason || 'Unknown error';
          let shouldRetry = true;
          
          if (err.status === 401) {
            errorMessage = 'Authentication failed - check VPS connection';
            shouldRetry = false; // Don't retry auth errors
          } else if (err.status === 403) {
            errorMessage = 'Access forbidden - check permissions';
            shouldRetry = false; // Don't retry permission errors
          } else if (err.status === 404) {
            errorMessage = 'Target device not found or offline';
          } else if (err.status === 502) {
            errorMessage = 'Proxy error - desktop may be offline';
          }

          this.status.error = `Internet sync failed: ${errorMessage}`;
          this.status.connected = false;
          this.status.syncing = false;
          this.emitStatusUpdate();
          
          // Auto-retry connection for temporary errors
          if (shouldRetry && this.currentPeer) {
            console.log('🔄 Scheduling auto-reconnection in 10 seconds...');
            setTimeout(() => {
              if (this.currentPeer && !this.status.connected) {
                console.log('🔄 Attempting auto-reconnection...');
                this.startSync(this.currentPeer);
              }
            }, 10000);
          }
        });

      return true;
    } catch (error: any) {
      console.error('❌ Failed to start internet sync:', error);
      this.status.error = error.message;
      this.status.connected = false;
      this.status.syncing = false;
      this.emitStatusUpdate();
      return false;
    }
  }

  async stopSync(): Promise<void> {
    if (this.syncHandler) {
      console.log('🛑 Stopping internet sync...');
      this.syncHandler.cancel();
      this.syncHandler = null;
    }

    this.stopTokenRefresh();
    this.stopHeartbeat();
    
    this.currentPeer = null;
    this.status = {
      connected: false,
      syncing: false,
      docsReceived: this.status.docsReceived,
      docsSent: this.status.docsSent,
      lastSync: this.status.lastSync
    };
    this.emitStatusUpdate();
  }

  private async waitForDatabase(maxWaitMs = 10000): Promise<void> {
    const startTime = Date.now();
    
    while (!mainDbInstance.isInitialized || !mainDbInstance.getPouchDBForSync()) {
      if (Date.now() - startTime > maxWaitMs) {
        throw new Error('Database initialization timeout - not ready for sync operations');
      }
      
      console.log('⏳ Waiting for database initialization...');
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('✅ Database ready for internet sync operations');
  }

  getStatus(): InternetSyncStatus {
    return { ...this.status };
  }

  getCurrentPeer(): DiscoveredPeer | null {
    return this.currentPeer;
  }

  isConnected(): boolean {
    return this.status.connected;
  }

  isSyncing(): boolean {
    return this.status.syncing;
  }

  isConfigured(): boolean {
    return this.config !== null;
  }
}

export const internetSyncService = new InternetSyncService();
export type { InternetSyncConfig, DiscoveredPeer, InternetSyncStatus };