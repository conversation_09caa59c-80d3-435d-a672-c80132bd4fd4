/**
 * Hold Storage Service
 * 
 * Handles all database operations for the Order Hold System using the app's existing DatabaseV4 system.
 * Provides efficient storage, retrieval, and management of held orders.
 */

import { 
  HoldDocument, 
  HoldStorageService, 
  HoldQueryOptions, 
  HoldStatus,
  HoldPriority,
  HoldStorageError,
  HoldNotFoundError,
  HoldCorruptedError
} from '../../types/orderHold';

import { mainDbInstance } from '../db/v4/core/db-main-instance';
import { getCurrentRestaurantId } from '../db/v4/utils/restaurant-id';

/**
 * DatabaseV4-based implementation of Hold Storage Service
 * Uses the app's existing database infrastructure
 */
export class DatabaseV4HoldStorage implements HoldStorageService {
  private collectionName = 'order_holds';

  constructor() {
    // No initialization needed - uses existing database
  }

  /**
   * Get the current database instance
   */
  private getDatabase() {
    if (!mainDbInstance.isInitialized) {
      throw new HoldStorageError('Database not initialized');
    }
    
    // Use DatabaseV4 directly instead of trying to get PouchDB instance
    return mainDbInstance;
  }

  /**
   * Wait for database to be initialized
   */
  private async ensureDatabaseReady(maxWaitMs = 5000): Promise<void> {
    const startTime = Date.now();
    
    while (!mainDbInstance.isInitialized && (Date.now() - startTime) < maxWaitMs) {
      console.log('[HoldStorage] Waiting for database initialization...');
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    if (!mainDbInstance.isInitialized) {
      throw new HoldStorageError('Database initialization timeout');
    }
  }

  /**
   * Generate a unique hold ID
   */
  private generateHoldId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `hold_${timestamp}_${random}`;
  }

  /**
   * Validate hold document structure
   */
  private validateHoldDocument(doc: HoldDocument): void {
    const errors: string[] = [];

    if (!doc.holdId) errors.push('Missing holdId');
    if (!doc.label) errors.push('Missing label');
    if (!doc.createdAt) errors.push('Missing createdAt');
    if (!doc.createdBy) errors.push('Missing createdBy');
    if (!doc.orderState) errors.push('Missing orderState');
    if (!doc.uiState) errors.push('Missing uiState');
    if (!doc.context) errors.push('Missing context');
    if (!doc.orderState?.items?.length) errors.push('Order must have items');

    if (errors.length > 0) {
      throw new HoldStorageError(`Invalid hold document: ${errors.join(', ')}`, doc.holdId);
    }
  }

  /**
   * Sanitize hold document for storage
   */
  private sanitizeForStorage(doc: HoldDocument): HoldDocument {
    return {
      ...doc,
      // Ensure required fields
      type: 'order_hold',
      schemaVersion: 'v4.0',
      status: doc.status || 'active',
      
      // Sanitize order state
      orderState: {
        ...doc.orderState,
        items: doc.orderState.items.map(item => ({
          ...item,
          // Ensure all required item fields
          id: item.id || `item_${Date.now()}_${Math.random().toString(36).substring(2, 6)}`,
          quantity: Math.max(1, item.quantity || 1),
          price: Math.max(0, item.price || 0),
          addons: item.addons || []
        }))
      },

      // Sanitize UI state
      uiState: {
        selectedCategory: doc.uiState.selectedCategory || '',
        selectedItemSizes: doc.uiState.selectedItemSizes || {},
        selectedSupplements: doc.uiState.selectedSupplements || {},
        itemNotes: doc.uiState.itemNotes || {}
      },

      // Sanitize context
      context: {
        priority: doc.context.priority || 'normal',
        customerWaiting: doc.context.customerWaiting ?? false,
        estimatedItems: doc.context.estimatedItems || doc.orderState.items.length,
        ...doc.context
      }
    };
  }

  /**
   * Save a hold document
   */
  async save(holdDoc: HoldDocument): Promise<HoldDocument> {
    try {
      await this.ensureDatabaseReady();
      const db = this.getDatabase();
      
      // Generate ID if not provided
      if (!holdDoc._id) {
        holdDoc.holdId = holdDoc.holdId || this.generateHoldId();
        holdDoc._id = `hold:${holdDoc.holdId}`;
      }

      // Validate document
      this.validateHoldDocument(holdDoc);

      // Sanitize for storage
      const sanitizedDoc = this.sanitizeForStorage(holdDoc);

      // Save to database using existing database methods
      const result = await db.putDoc(sanitizedDoc);

      console.log(`[HoldStorage] Hold saved: ${holdDoc.holdId}`);

      return {
        ...sanitizedDoc,
        _rev: result.rev
      };
    } catch (error) {
      console.error('[HoldStorage] Failed to save hold:', error);
      throw new HoldStorageError(
        `Failed to save hold: ${error.message}`,
        holdDoc.holdId,
        error
      );
    }
  }

  /**
   * Get a hold document by ID
   */
  async get(holdId: string): Promise<HoldDocument> {
    try {
      const db = this.getDatabase();
      const docId = holdId.startsWith('hold:') ? holdId : `hold:${holdId}`;
      
      const doc = await db.getDoc<HoldDocument>(docId);
      
      if (!doc || doc.type !== 'order_hold') {
        throw new HoldNotFoundError(holdId);
      }

      console.log(`[HoldStorage] Hold retrieved: ${holdId}`);
      return doc;
    } catch (error) {
      if (error.name === 'not_found' || error.status === 404) {
        throw new HoldNotFoundError(holdId);
      }
      
      console.error(`[HoldStorage] Failed to get hold ${holdId}:`, error);
      throw new HoldStorageError(
        `Failed to get hold: ${error.message}`,
        holdId,
        error
      );
    }
  }

  /**
   * Update a hold document
   */
  async update(holdId: string, updates: Partial<HoldDocument>): Promise<HoldDocument> {
    try {
      const db = this.getDatabase();
      const docId = holdId.startsWith('hold:') ? holdId : `hold:${holdId}`;
      
      // Get current document
      const currentDoc = await this.get(holdId);
      
      // Merge updates
      const updatedDoc = {
        ...currentDoc,
        ...updates,
        _id: currentDoc._id,
        _rev: currentDoc._rev,
        holdId: currentDoc.holdId, // Don't allow changing holdId
        updatedAt: new Date().toISOString()
      };

      // Validate and sanitize
      this.validateHoldDocument(updatedDoc);
      const sanitizedDoc = this.sanitizeForStorage(updatedDoc);

      // Save updated document
      const result = await db.putDoc(sanitizedDoc);

      console.log(`[HoldStorage] Hold updated: ${holdId}`);

      return {
        ...sanitizedDoc,
        _rev: result.rev
      };
    } catch (error) {
      console.error(`[HoldStorage] Failed to update hold ${holdId}:`, error);
      throw new HoldStorageError(
        `Failed to update hold: ${error.message}`,
        holdId,
        error
      );
    }
  }

  /**
   * Delete a hold document
   */
  async delete(holdId: string): Promise<boolean> {
    try {
      const db = this.getDatabase();
      const docId = holdId.startsWith('hold:') ? holdId : `hold:${holdId}`;
      
      // Get current document to get revision
      const currentDoc = await this.get(holdId);
      
      // Delete document
      await db.deleteDoc(currentDoc._id, currentDoc._rev);

      console.log(`[HoldStorage] Hold deleted: ${holdId}`);
      return true;
    } catch (error) {
      if (error.name === 'not_found' || error.status === 404) {
        console.log(`[HoldStorage] Hold ${holdId} not found for deletion`);
        return false;
      }
      
      console.error(`[HoldStorage] Failed to delete hold ${holdId}:`, error);
      throw new HoldStorageError(
        `Failed to delete hold: ${error.message}`,
        holdId,
        error
      );
    }
  }

  /**
   * Query hold documents with options
   */
  async query(options: HoldQueryOptions = {}): Promise<HoldDocument[]> {
    try {
      await this.ensureDatabaseReady();
      const db = this.getDatabase();
      
      // Build selector for DatabaseV4 findDocs API
      console.log('[HoldStorage] Using DatabaseV4 findDocs approach for querying holds');
      
      const selector: any = {
        type: 'order_hold'
      };
      
      // Add status filter to selector if specified
      if (options.status) {
        if (Array.isArray(options.status)) {
          selector.status = { $in: options.status };
        } else {
          selector.status = options.status;
        }
      }
      
      // Add priority filter to selector if specified
      if (options.priority) {
        if (Array.isArray(options.priority)) {
          selector['context.priority'] = { $in: options.priority };
        } else {
          selector['context.priority'] = options.priority;
        }
      }
      
      // Add createdBy filter
      if (options.createdBy) {
        selector.createdBy = options.createdBy;
      }
      
      // Add date filters
      if (options.createdAfter || options.createdBefore) {
        selector.createdAt = {};
        if (options.createdAfter) {
          selector.createdAt.$gte = options.createdAfter;
        }
        if (options.createdBefore) {
          selector.createdAt.$lte = options.createdBefore;
        }
      }
      
      // Prepare sort options
      const sort: any[] = [];
      if (options.sortBy) {
        switch (options.sortBy) {
          case 'priority':
            sort.push({ 'context.priority': options.sortOrder === 'asc' ? 'asc' : 'desc' });
            break;
          case 'expiresAt':
            sort.push({ expiresAt: options.sortOrder === 'asc' ? 'asc' : 'desc' });
            break;
          default: // createdAt
            sort.push({ createdAt: options.sortOrder === 'asc' ? 'asc' : 'desc' });
        }
      } else {
        // Default sort by createdAt descending (newest first)
        sort.push({ createdAt: 'desc' });
      }
      
      // Execute query using DatabaseV4's findDocs method
      const result = await db.findDocs({
        selector,
        sort,
        limit: options.limit || 100
      });
      
      let docs = result.docs as HoldDocument[];
      
      // Apply manual filtering for complex queries that might not work in all DB modes
      if (options.status && Array.isArray(options.status)) {
        docs = docs.filter(doc => options.status!.includes(doc.status));
      }
      
      if (options.priority && Array.isArray(options.priority)) {
        docs = docs.filter(doc => options.priority!.includes(doc.context.priority));
      }
      
      if (options.createdBy) {
        docs = docs.filter(doc => doc.createdBy === options.createdBy);
      }
      
      if (options.createdAfter) {
        docs = docs.filter(doc => doc.createdAt >= options.createdAfter!);
      }
      
      if (options.createdBefore) {
        docs = docs.filter(doc => doc.createdAt <= options.createdBefore!);
      }
      
      // Apply manual sorting as fallback
      if (options.sortBy) {
        docs.sort((a, b) => {
          let aVal, bVal;
          
          switch (options.sortBy) {
            case 'priority':
              const priorityOrder = { urgent: 3, high: 2, normal: 1 };
              aVal = priorityOrder[a.context.priority] || 1;
              bVal = priorityOrder[b.context.priority] || 1;
              break;
            case 'expiresAt':
              aVal = new Date(a.expiresAt || '9999-12-31').getTime();
              bVal = new Date(b.expiresAt || '9999-12-31').getTime();
              break;
            default: // createdAt
              aVal = new Date(a.createdAt).getTime();
              bVal = new Date(b.createdAt).getTime();
          }
          
          const comparison = aVal - bVal;
          return options.sortOrder === 'asc' ? comparison : -comparison;
        });
      }
      
      console.log(`[HoldStorage] Query executed: found ${docs.length} holds`);
      return docs;
    } catch (error) {
      console.error('[HoldStorage] Failed to query holds:', error);
      throw new HoldStorageError(
        `Failed to query holds: ${error.message}`,
        undefined,
        error
      );
    }
  }

  /**
   * Clean up expired holds
   */
  async cleanup(): Promise<number> {
    try {
      const expiredHolds = await this.query({
        status: 'expired',
        limit: 1000
      });

      let cleanedCount = 0;
      for (const hold of expiredHolds) {
        try {
          await this.delete(hold.holdId);
          cleanedCount++;
        } catch (error) {
          console.warn(`[HoldStorage] Failed to clean up expired hold ${hold.holdId}:`, error);
        }
      }

      console.log(`[HoldStorage] Cleaned up ${cleanedCount} expired holds`);
      return cleanedCount;
    } catch (error) {
      console.error('[HoldStorage] Failed to cleanup holds:', error);
      throw new HoldStorageError(
        `Failed to cleanup holds: ${error.message}`,
        undefined,
        error
      );
    }
  }

  /**
   * Get hold statistics
   */
  async getStatistics(): Promise<{
    totalHolds: number;
    activeHolds: number;
    expiredHolds: number;
    priorityBreakdown: Record<HoldPriority, number>;
  }> {
    try {
      const allHolds = await this.query({ limit: 1000 });
      
      const stats = {
        totalHolds: allHolds.length,
        activeHolds: allHolds.filter(h => h.status === 'active').length,
        expiredHolds: allHolds.filter(h => h.status === 'expired').length,
        priorityBreakdown: {
          normal: 0,
          high: 0,
          urgent: 0
        } as Record<HoldPriority, number>
      };

      // Calculate priority breakdown
      allHolds.forEach(hold => {
        const priority = hold.context.priority || 'normal';
        stats.priorityBreakdown[priority]++;
      });

      return stats;
    } catch (error) {
      console.error('[HoldStorage] Failed to get statistics:', error);
      throw new HoldStorageError(
        `Failed to get statistics: ${error.message}`,
        undefined,
        error
      );
    }
  }
}

// Export singleton instance
export const holdStorage = new DatabaseV4HoldStorage();