import { internetSyncService, InternetSyncConfig } from './internet-sync';
import { isWebBrowser } from '@/lib/utils/build-utils';

/**
 * Web Browser Sync Service
 * 
 * Handles sync operations specifically for web browsers which cannot
 * perform local network discovery or direct P2P connections due to
 * browser security restrictions (CORS, private network access, etc.)
 */

interface WebBrowserSyncStatus {
  isWebBrowser: boolean;
  canSyncLocally: boolean;
  internetSyncEnabled: boolean;
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
  lastSync?: Date;
  error?: string;
}

class WebBrowserSyncService {
  private status: WebBrowserSyncStatus = {
    isWebBrowser: false,
    canSyncLocally: false,
    internetSyncEnabled: false,
    connectionStatus: 'disconnected'
  };

  private listeners: ((status: WebBrowserSyncStatus) => void)[] = [];

  constructor() {
    this.detectEnvironment();
  }

  private detectEnvironment(): void {
    this.status.isWebBrowser = isWebBrowser();
    this.status.canSyncLocally = !this.status.isWebBrowser;

    if (this.status.isWebBrowser) {
      console.log('🌐 Web browser detected - local network sync disabled');
      console.log('📡 Internet sync only mode enabled');
    }
  }

  configure(config: Partial<InternetSyncConfig>): boolean {
    if (!this.status.isWebBrowser) {
      console.warn('⚠️ WebBrowserSyncService should only be used in web browsers');
      return false;
    }

    try {
      // Use bistro.icu as default VPS URL
      const finalConfig: InternetSyncConfig = {
        vpsBaseUrl: 'https://bistro.icu',
        authToken: '',
        deviceId: '',
        ...config
      };

      // Validate HTTPS
      const url = new URL(finalConfig.vpsBaseUrl);
      if (url.protocol !== 'https:') {
        throw new Error('Web browser sync requires HTTPS for security');
      }

      internetSyncService.configure(finalConfig);
      this.status.internetSyncEnabled = true;
      this.status.connectionStatus = 'disconnected';
      this.emitStatusUpdate();
      return true;
    } catch (error) {
      console.error('❌ Failed to configure web browser sync:', error);
      this.status.error = error instanceof Error ? error.message : 'Configuration failed';
      this.status.connectionStatus = 'error';
      this.emitStatusUpdate();
      return false;
    }
  }

  async startInternetSync(): Promise<boolean> {
    if (!this.status.isWebBrowser) {
      throw new Error('Not in web browser environment');
    }

    if (!this.status.internetSyncEnabled) {
      throw new Error('Internet sync not configured');
    }

    this.status.connectionStatus = 'connecting';
    this.emitStatusUpdate();

    try {
      // Discover available peers via internet
      const peers = await internetSyncService.discoverPeers();
      
      if (peers.length === 0) {
        this.status.error = 'No desktop peers available for sync';
        this.status.connectionStatus = 'error';
        this.emitStatusUpdate();
        return false;
      }

      // Find best desktop peer
      const desktopPeers = peers.filter(p => p.deviceType === 'desktop');
      if (desktopPeers.length === 0) {
        this.status.error = 'No desktop devices found - sync requires a desktop POS device';
        this.status.connectionStatus = 'error';
        this.emitStatusUpdate();
        return false;
      }

      const bestPeer = desktopPeers.sort((a, b) => 
        new Date(b.lastSeen).getTime() - new Date(a.lastSeen).getTime()
      )[0];

      // Start sync with best peer
      const success = await internetSyncService.startSync(bestPeer);
      
      if (success) {
        this.status.connectionStatus = 'connected';
        this.status.lastSync = new Date();
        this.status.error = undefined;
      } else {
        this.status.connectionStatus = 'error';
        this.status.error = 'Failed to establish sync connection';
      }

      this.emitStatusUpdate();
      return success;

    } catch (error) {
      console.error('❌ Web browser sync failed:', error);
      this.status.error = error instanceof Error ? error.message : 'Unknown sync error';
      this.status.connectionStatus = 'error';
      this.emitStatusUpdate();
      return false;
    }
  }

  async stopSync(): Promise<void> {
    await internetSyncService.stopSync();
    this.status.connectionStatus = 'disconnected';
    this.status.error = undefined;
    this.emitStatusUpdate();
  }

  getStatus(): WebBrowserSyncStatus {
    return { ...this.status };
  }

  onStatusChange(listener: (status: WebBrowserSyncStatus) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) this.listeners.splice(index, 1);
    };
  }

  private emitStatusUpdate(): void {
    this.listeners.forEach(listener => listener({ ...this.status }));
  }

  // Check if local network sync is possible (always false for web browsers)
  canPerformLocalSync(): boolean {
    return false;
  }

  // Get user-friendly error messages
  getUserFriendlyError(): string | null {
    if (!this.status.error) return null;

    if (this.status.error.includes('No desktop peers')) {
      return 'يجب تشغيل تطبيق نقطة البيع على جهاز الكمبيوتر أولاً';
    }

    if (this.status.error.includes('Authentication')) {
      return 'خطأ في المصادقة - تحقق من إعدادات الاتصال';
    }

    if (this.status.error.includes('Network')) {
      return 'خطأ في الشبكة - تحقق من اتصال الإنترنت';
    }

    return this.status.error;
  }

  // Get sync instructions for web users
  getSyncInstructions(): string[] {
    if (!this.status.isWebBrowser) {
      return [];
    }

    return [
      '1. تأكد من تشغيل تطبيق نقطة البيع على جهاز الكمبيوتر',
      '2. تأكد من أن الجهاز متصل بالإنترنت',
      '3. تحقق من إعدادات المطعم متطابقة',
      '4. اضغط على "بدء المزامنة" للاتصال'
    ];
  }
}

export const webBrowserSyncService = new WebBrowserSyncService();
export type { WebBrowserSyncStatus };