/**
 * Hold Manager Service
 * 
 * High-level service that orchestrates order hold operations.
 * Provides business logic for creating, resuming, and managing held orders.
 */

import { format } from 'date-fns';
import { 
  HoldDocument, 
  HoldManagerService, 
  CreateHoldRequest,
  ResumeHoldResponse,
  HoldValidationResult,
  HoldPriority,
  HoldContext,
  HoldOrderState,
  HoldUiState,
  OrderState,
  HoldError,
  HoldValidationError,
  HoldNotFoundError,
  HoldExpiredError
} from '../../types/orderHold';

import { holdStorage } from './holdStorage';
import { useAuth } from '../context/multi-user-auth-provider';

/**
 * Implementation of Hold Manager Service
 */
export class OrderHoldManager implements HoldManagerService {
  private static instance: OrderHoldManager | null = null;

  // Singleton pattern for consistent state management
  public static getInstance(): OrderHoldManager {
    if (!OrderHoldManager.instance) {
      OrderHoldManager.instance = new OrderHoldManager();
    }
    return OrderHoldManager.instance;
  }

  private constructor() {
    // Private constructor for singleton
  }

  /**
   * Get current user information
   */
  private getCurrentUser(): { id: string; name: string } | null {
    try {
      // Access user context - this would typically be injected
      // For now, we'll use a fallback approach
      if (typeof window !== 'undefined') {
        const userStr = localStorage.getItem('current_user');
        if (userStr) {
          const user = JSON.parse(userStr);
          return {
            id: user.id || user._id || 'unknown',
            name: user.name || user.username || 'Unknown User'
          };
        }
      }
      
      return {
        id: 'default_user',
        name: 'Default User'
      };
    } catch (error) {
      console.warn('[HoldManager] Failed to get current user:', error);
      return {
        id: 'default_user',
        name: 'Default User'
      };
    }
  }

  /**
   * Generate automatic label for a hold
   */
  private generateAutoLabel(orderState: HoldOrderState): string {
    const { items, tableId, orderType, customer } = orderState;
    
    // Priority 1: Table identification
    if (tableId) {
      return `Table ${tableId}`;
    }
    
    // Priority 2: Customer identification
    if (customer?.name) {
      return customer.name;
    }
    
    // Priority 3: Order characteristics
    if (items.length > 5) {
      return `Large Order (${items.length} items)`;
    }
    
    // Priority 4: Order type + time
    const timeLabel = format(new Date(), 'HH:mm');
    const typeLabel = orderType === 'dine-in' ? 'Dine-in' : 
                     orderType === 'takeaway' ? 'Takeaway' : 
                     orderType === 'delivery' ? 'Delivery' : 'Order';
    
    return `${typeLabel} • ${timeLabel}`;
  }

  /**
   * Convert NewOrderingInterface state to hold format
   */
  private convertToHoldOrderState(orderState: any): HoldOrderState {
    return {
      items: orderState.items || [],
      orderType: orderState.orderType || 'dine-in',
      tableId: orderState.tableId,
      customer: orderState.customer,
      deliveryPerson: orderState.deliveryPerson,
      notes: orderState.notes || '',
      total: orderState.total || 0
    };
  }

  /**
   * Convert UI state to hold format (sanitize Sets to arrays)
   */
  private convertToHoldUiState(uiState: any): HoldUiState {
    const selectedSupplements: Record<string, string[]> = {};
    
    // Convert Sets to arrays for JSON storage
    if (uiState.selectedSupplements) {
      Object.entries(uiState.selectedSupplements).forEach(([key, value]) => {
        if (value instanceof Set) {
          selectedSupplements[key] = Array.from(value);
        } else if (Array.isArray(value)) {
          selectedSupplements[key] = value;
        } else {
          selectedSupplements[key] = [];
        }
      });
    }

    return {
      selectedCategory: uiState.selectedCategory || '',
      selectedItemSizes: uiState.selectedItemSizes || {},
      selectedSupplements,
      itemNotes: uiState.itemNotes || {},
      selectedItemForSupplements: uiState.selectedItemForSupplements,
      lastAddedItem: uiState.lastAddedItem
    };
  }

  /**
   * Convert hold state back to NewOrderingInterface format
   */
  private convertFromHoldOrderState(holdOrderState: HoldOrderState): any {
    return {
      _id: '',
      id: '',
      type: 'order_document',
      orderType: holdOrderState.orderType,
      status: 'pending',
      tableId: holdOrderState.tableId || '',
      items: holdOrderState.items,
      total: holdOrderState.total,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      schemaVersion: 'v4.0',
      notes: holdOrderState.notes,
      customer: holdOrderState.customer,
      deliveryPerson: holdOrderState.deliveryPerson
    };
  }

  /**
   * Convert hold UI state back to NewOrderingInterface format
   */
  private convertFromHoldUiState(holdUiState: HoldUiState): any {
    const selectedSupplements: Record<string, Set<string>> = {};
    
    // Convert arrays back to Sets
    Object.entries(holdUiState.selectedSupplements || {}).forEach(([key, value]) => {
      selectedSupplements[key] = new Set(value);
    });

    return {
      selectedCategory: holdUiState.selectedCategory,
      selectedItemSizes: holdUiState.selectedItemSizes,
      selectedSupplements,
      itemNotes: holdUiState.itemNotes,
      selectedItemForSupplements: holdUiState.selectedItemForSupplements,
      lastAddedItem: holdUiState.lastAddedItem
    };
  }

  /**
   * Validate order state before holding
   */
  private validateOrderState(orderState: any): HoldValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if order has items
    if (!orderState.items || orderState.items.length === 0) {
      errors.push('Cannot hold empty order');
    }

    // Validate order items
    if (orderState.items) {
      orderState.items.forEach((item: any, index: number) => {
        if (!item.id) {
          errors.push(`Item ${index + 1} missing ID`);
        }
        if (!item.name) {
          errors.push(`Item ${index + 1} missing name`);
        }
        if (typeof item.quantity !== 'number' || item.quantity <= 0) {
          errors.push(`Item ${index + 1} has invalid quantity`);
        }
        if (typeof item.price !== 'number' || item.price < 0) {
          errors.push(`Item ${index + 1} has invalid price`);
        }
      });
    }

    // Check for table requirement
    if (orderState.orderType === 'dine-in' && !orderState.tableId) {
      warnings.push('Dine-in order without table assignment');
    }

    // Check for customer info on delivery orders
    if (orderState.orderType === 'delivery' && !orderState.customer?.phone) {
      warnings.push('Delivery order missing customer phone');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  /**
   * Create a hold from current order state
   */
  async holdCurrentOrder(request: CreateHoldRequest): Promise<string> {
    try {
      console.log('[HoldManager] Creating hold for order...');

      // Validate order state
      const validation = this.validateOrderState(request.orderState);
      if (!validation.isValid) {
        throw new HoldValidationError(
          'Invalid order state for hold',
          undefined,
          validation.errors
        );
      }

      // Get current user
      const currentUser = this.getCurrentUser();
      if (!currentUser) {
        throw new HoldError('User not authenticated', 'AUTH_ERROR');
      }

      // Convert states to hold format
      const holdOrderState = this.convertToHoldOrderState(request.orderState);
      const holdUiState = this.convertToHoldUiState(request.uiState);

      // Generate hold ID and label
      const holdId = `hold_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
      const autoLabel = this.generateAutoLabel(holdOrderState);
      const label = request.label || autoLabel;

      // Build context
      const context: HoldContext = {
        priority: request.priority || 'normal',
        customerWaiting: request.context?.customerWaiting ?? false,
        estimatedItems: holdOrderState.items.length,
        interruptionReason: request.context?.interruptionReason,
        originalTable: holdOrderState.tableId,
        ...request.context,
        label
      };

      // Create hold document
      const holdDoc: HoldDocument = {
        _id: `hold:${holdId}`,
        type: 'order_hold',
        schemaVersion: 'v4.0',
        holdId,
        label,
        createdAt: new Date().toISOString(),
        createdBy: currentUser.id,
        status: 'active',
        orderState: holdOrderState,
        uiState: holdUiState,
        context
      };

      // Save to storage
      const savedHold = await holdStorage.save(holdDoc);

      console.log(`[HoldManager] Hold created successfully: ${holdId}`);

      // Dispatch global event for UI updates
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('order-held', {
          detail: { holdId, label, itemCount: holdOrderState.items.length }
        }));
      }

      return savedHold.holdId;
    } catch (error) {
      console.error('[HoldManager] Failed to create hold:', error);
      
      if (error instanceof HoldError) {
        throw error;
      }
      
      throw new HoldError(
        `Failed to create hold: ${error.message}`,
        'CREATE_FAILED',
        undefined,
        error
      );
    }
  }

  /**
   * Resume a held order
   */
  async resumeOrder(holdId: string): Promise<ResumeHoldResponse> {
    try {
      console.log(`[HoldManager] Resuming hold: ${holdId}`);

      // Get hold document
      const holdDoc = await holdStorage.get(holdId);

      // Check if hold is still active
      if (holdDoc.status !== 'active') {
        if (holdDoc.status === 'expired') {
          throw new HoldExpiredError(holdId, holdDoc.expiresAt || 'unknown');
        }
        throw new HoldError(
          `Cannot resume hold with status: ${holdDoc.status}`,
          'INVALID_STATUS',
          holdId
        );
      }

      // Check expiration
      if (holdDoc.expiresAt && new Date(holdDoc.expiresAt) < new Date()) {
        // Update status to expired
        await holdStorage.update(holdId, { status: 'expired' });
        throw new HoldExpiredError(holdId, holdDoc.expiresAt);
      }

      // Convert back to NewOrderingInterface format
      const orderState = this.convertFromHoldOrderState(holdDoc.orderState);
      const uiState = this.convertFromHoldUiState(holdDoc.uiState);

      // Calculate hold duration
      const heldFor = Date.now() - new Date(holdDoc.createdAt).getTime();

      // Mark hold as resumed
      const currentUser = this.getCurrentUser();
      await holdStorage.update(holdId, {
        status: 'resumed',
        resumedAt: new Date().toISOString(),
        resumedBy: currentUser?.id || 'unknown',
        totalHoldDuration: heldFor
      });

      console.log(`[HoldManager] Hold resumed successfully: ${holdId}`);

      // Dispatch global event
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('order-resumed', {
          detail: { 
            holdId, 
            label: holdDoc.label, 
            heldFor: Math.round(heldFor / 1000) 
          }
        }));
      }

      return {
        orderState,
        uiState,
        holdInfo: {
          label: holdDoc.label,
          heldFor,
          priority: holdDoc.context.priority
        }
      };
    } catch (error) {
      console.error(`[HoldManager] Failed to resume hold ${holdId}:`, error);
      
      if (error instanceof HoldError) {
        throw error;
      }
      
      throw new HoldError(
        `Failed to resume hold: ${error.message}`,
        'RESUME_FAILED',
        holdId,
        error
      );
    }
  }

  /**
   * Clear a held order
   */
  async clearHold(holdId: string): Promise<boolean> {
    try {
      console.log(`[HoldManager] Clearing hold: ${holdId}`);

      // Get hold document first to check if it exists
      const holdDoc = await holdStorage.get(holdId);

      // Mark as cleared instead of deleting (for audit trail)
      const currentUser = this.getCurrentUser();
      await holdStorage.update(holdId, {
        status: 'cleared',
        clearedAt: new Date().toISOString(),
        clearedBy: currentUser?.id || 'unknown'
      });

      console.log(`[HoldManager] Hold cleared successfully: ${holdId}`);

      // Dispatch global event
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('order-hold-cleared', {
          detail: { holdId, label: holdDoc.label }
        }));
      }

      return true;
    } catch (error) {
      console.error(`[HoldManager] Failed to clear hold ${holdId}:`, error);
      
      if (error instanceof HoldNotFoundError) {
        // Already cleared/not found
        return false;
      }
      
      throw new HoldError(
        `Failed to clear hold: ${error.message}`,
        'CLEAR_FAILED',
        holdId,
        error
      );
    }
  }

  /**
   * Get all holds
   */
  async getAllHolds(): Promise<HoldDocument[]> {
    try {
      return await holdStorage.query({
        sortBy: 'createdAt',
        sortOrder: 'desc',
        limit: 100
      });
    } catch (error) {
      console.error('[HoldManager] Failed to get all holds:', error);
      throw new HoldError(
        `Failed to retrieve holds: ${error.message}`,
        'QUERY_FAILED',
        undefined,
        error
      );
    }
  }

  /**
   * Get active holds only
   */
  async getActiveHolds(): Promise<HoldDocument[]> {
    try {
      return await holdStorage.query({
        status: 'active',
        sortBy: 'createdAt',
        sortOrder: 'desc',
        limit: 50
      });
    } catch (error) {
      console.error('[HoldManager] Failed to get active holds:', error);
      throw new HoldError(
        `Failed to retrieve active holds: ${error.message}`,
        'QUERY_FAILED',
        undefined,
        error
      );
    }
  }

  /**
   * Get expired holds
   */
  async getExpiredHolds(): Promise<HoldDocument[]> {
    try {
      return await holdStorage.query({
        status: 'expired',
        sortBy: 'createdAt',
        sortOrder: 'desc',
        limit: 20
      });
    } catch (error) {
      console.error('[HoldManager] Failed to get expired holds:', error);
      throw new HoldError(
        `Failed to retrieve expired holds: ${error.message}`,
        'QUERY_FAILED',
        undefined,
        error
      );
    }
  }

  /**
   * Clear expired holds
   */
  async clearExpiredHolds(): Promise<number> {
    try {
      console.log('[HoldManager] Clearing expired holds...');
      const cleanedCount = await holdStorage.cleanup();
      
      console.log(`[HoldManager] Cleared ${cleanedCount} expired holds`);
      
      // Dispatch global event
      if (typeof window !== 'undefined' && cleanedCount > 0) {
        window.dispatchEvent(new CustomEvent('holds-cleaned', {
          detail: { count: cleanedCount }
        }));
      }
      
      return cleanedCount;
    } catch (error) {
      console.error('[HoldManager] Failed to clear expired holds:', error);
      throw new HoldError(
        `Failed to clear expired holds: ${error.message}`,
        'CLEANUP_FAILED',
        undefined,
        error
      );
    }
  }

  /**
   * Validate a specific hold
   */
  async validateHold(holdId: string): Promise<HoldValidationResult> {
    try {
      const holdDoc = await holdStorage.get(holdId);
      
      const errors: string[] = [];
      const warnings: string[] = [];

      // Check document structure
      if (!holdDoc.orderState?.items?.length) {
        errors.push('Hold has no order items');
      }

      if (!holdDoc.label) {
        errors.push('Hold missing label');
      }

      if (!holdDoc.createdBy) {
        errors.push('Hold missing creator');
      }

      // Check expiration
      if (holdDoc.expiresAt && new Date(holdDoc.expiresAt) < new Date()) {
        warnings.push('Hold has expired');
      }

      // Check age
      const ageMinutes = (Date.now() - new Date(holdDoc.createdAt).getTime()) / (1000 * 60);
      if (ageMinutes > 120) { // 2 hours
        warnings.push('Hold is very old');
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings: warnings.length > 0 ? warnings : undefined
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [`Failed to validate hold: ${error.message}`]
      };
    }
  }

  /**
   * Update hold label
   */
  async updateHoldLabel(holdId: string, label: string): Promise<boolean> {
    try {
      await holdStorage.update(holdId, { label });
      
      // Dispatch global event
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('hold-updated', {
          detail: { holdId, field: 'label', value: label }
        }));
      }
      
      return true;
    } catch (error) {
      console.error(`[HoldManager] Failed to update hold label ${holdId}:`, error);
      return false;
    }
  }

  /**
   * Update hold priority
   */
  async updateHoldPriority(holdId: string, priority: HoldPriority): Promise<boolean> {
    try {
      const holdDoc = await holdStorage.get(holdId);
      await holdStorage.update(holdId, {
        context: {
          ...holdDoc.context,
          priority
        }
      });
      
      // Dispatch global event
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('hold-updated', {
          detail: { holdId, field: 'priority', value: priority }
        }));
      }
      
      return true;
    } catch (error) {
      console.error(`[HoldManager] Failed to update hold priority ${holdId}:`, error);
      return false;
    }
  }

  /**
   * Update hold context
   */
  async updateHoldContext(holdId: string, context: Partial<HoldContext>): Promise<boolean> {
    try {
      const holdDoc = await holdStorage.get(holdId);
      await holdStorage.update(holdId, {
        context: {
          ...holdDoc.context,
          ...context
        }
      });
      
      return true;
    } catch (error) {
      console.error(`[HoldManager] Failed to update hold context ${holdId}:`, error);
      return false;
    }
  }
}

// Export singleton instance
export const holdManager = OrderHoldManager.getInstance();