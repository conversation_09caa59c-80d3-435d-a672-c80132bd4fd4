'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { jwtDecode } from 'jwt-decode';
import { JwtPayload, User } from '@/lib/auth/new-auth-service';
import { multiUserSessionManager, UserSession } from '@/lib/auth/multi-user-session-manager';
import { isAdmin, isOwner, canManageStaff } from '@/lib/auth/role-utils';
import { cleanRestaurantId } from '@/lib/db/db-utils';
import { getApiUrl, checkRemoteConnectivity, getFallbackApiUrl, BUILD_CONFIG } from '@/lib/build-config';
import { httpClient } from '@/lib/utils/http-client';
import loading from '@/app/orders/loading';

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Storage keys
const TOKEN_KEY = 'auth_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const OFFLINE_MODE_KEY = 'offline_mode';

export interface MultiUserAuthState {
  isAuthenticated: boolean;
  currentUser: User | null;
  availableUsers: UserSession[];
  loading: boolean;
  error: string | null;
  isOfflineMode: boolean;
  restaurantId: string | null;
  isRestricted: boolean;
}

export interface UseMultiUserAuthReturn extends MultiUserAuthState {
  // Authentication actions
  login: (credentials: { identifier: string; password: string; restaurantId?: string; isStaffLogin?: boolean }) => Promise<boolean>;
  register: (userData: { name: string; email: string; password: string; phoneNumber?: string }) => Promise<boolean>;
  logout: () => void;
  logoutAll: () => void;
  
  // User switching
  switchToUser: (userId: string) => Promise<boolean>;
  addUserSession: (credentials: { identifier: string; password: string; isStaffLogin?: boolean }) => Promise<boolean>;
  removeUserSession: (userId: string) => void;
  
  // Offline capabilities
  offlineLogin: (role?: string) => Promise<boolean>;
  
  // Utility functions
  refreshCurrentSession: () => Promise<boolean>;
  checkRestrictionStatus: () => Promise<boolean>;
  isAdmin: boolean;
  isOwner: boolean;
  canManageStaff: boolean;
  
  // Session management
  getSessionStats: () => {
    totalSessions: number;
    onlineSessions: number;
    offlineSessions: number;
    roles: Record<string, number>;
    lastActiveSession: string | null;
  };
}

// Helper functions
function isElectron(): boolean {
  return isBrowser && !!(window as any).electronAPI;
}

async function isServerReachable(): Promise<boolean> {
  try {
    // Use the proper connectivity check that handles remote servers
    return await checkRemoteConnectivity();
  } catch (error) {
    console.log('❌ Server reachability check failed:', error);
    return false;
  }
}

export function useMultiUserAuth(): UseMultiUserAuthReturn {
  // Initialize state
  const [state, setState] = useState<MultiUserAuthState>({
    isAuthenticated: false,
    currentUser: null,
    availableUsers: [],
    loading: true,
    error: null,
    isOfflineMode: false,
    restaurantId: null,
    isRestricted: false
  });

  const [isClient, setIsClient] = useState(false);
  const router = useRouter();

  // Check if we're on the client side
  useEffect(() => {
    if (!isBrowser) {
      setState(prev => ({ ...prev, loading: false }));
      return;
    }
    setIsClient(true);
  }, []);

  // Subscribe to session manager changes
  useEffect(() => {
    if (!isClient) return;

    const unsubscribe = multiUserSessionManager.subscribe((sessions, activeSession) => {
      const offlineMode = localStorage.getItem(OFFLINE_MODE_KEY) === 'true';
      
      // Check if user is restricted from JWT token
      let isRestricted = false;
      if (activeSession?.token) {
        try {
          const decoded = jwtDecode<JwtPayload>(activeSession.token);
          isRestricted = decoded.restricted === true;
        } catch (error) {
          console.warn('Failed to decode token for restriction check:', error);
        }
      }
      
      setState(prev => ({
        ...prev,
        isAuthenticated: !!activeSession,
        currentUser: activeSession?.user || null,
        availableUsers: sessions,
        isOfflineMode: offlineMode,
        restaurantId: multiUserSessionManager.getRestaurantId(),
        isRestricted,
        loading: false
      }));

      // Update legacy localStorage for v4 compatibility
      if (activeSession?.user) {
        const user = activeSession.user;
        localStorage.setItem('auth_data', JSON.stringify({
          restaurantId: user.restaurantId,
          userId: user.id,
          role: user.role,
          name: user.name
        }));
        
        // Update current token in localStorage
        localStorage.setItem(TOKEN_KEY, activeSession.token);
        if (activeSession.refreshToken) {
          localStorage.setItem(REFRESH_TOKEN_KEY, activeSession.refreshToken);
        }

        // Update CouchDB credentials with actual restaurant ID (desktop app only)
        if (typeof window !== 'undefined' && (window as any).electronAPI) {
          try {
            console.log(`🔄 Updating CouchDB credentials for restaurant: ${user.restaurantId}`);
            (window as any).electronAPI.invoke('update-couchdb-credentials', user.restaurantId);
          } catch (error) {
            console.warn('Failed to update CouchDB credentials:', error);
          }
        }
      }
    });

    return unsubscribe;
  }, [isClient]);

  // Initialize sessions on mount
  useEffect(() => {
    if (!isClient) return;

    const initializeSessions = async () => {
      try {
        // Check for offline mode
        const offlineMode = localStorage.getItem(OFFLINE_MODE_KEY) === 'true';
        console.log('🔄 Initializing sessions - Current offline mode:', offlineMode);
        
        if (isElectron()) {
          console.log('🖥️ Running in Electron, checking server reachability...');
          // Make server reachability check non-blocking to prevent auth page hanging
          isServerReachable().then(reachable => {
            console.log('📡 Server reachable:', reachable);
            // Don't automatically set offline mode - let user decide
            if (!reachable) {
              console.log('❌ Server not reachable - user can choose offline mode if needed');
            } else {
              console.log('✅ Server reachable - online mode available');
            }
          }).catch(error => {
            console.log('❌ Server reachability check error:', error);
          });
        }

        // Clean up invalid sessions
        multiUserSessionManager.cleanupInvalidSessions();

        // If no active session but we have a token, try to restore
        const activeSession = multiUserSessionManager.getActiveSession();
        if (!activeSession) {
          const token = localStorage.getItem(TOKEN_KEY);
          if (token) {
            try {
              const decoded = jwtDecode<JwtPayload>(token);
              const user: User = {
                id: decoded.sub,
                name: decoded.name,
                email: decoded.email || '',
                role: decoded.role || 'staff',
                restaurantId: cleanRestaurantId(String(decoded.restaurantId || '')),
                permissions: decoded.permissions,
                metadata: decoded.metadata
              };

              const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
              await multiUserSessionManager.addSession(user, token, refreshToken || undefined);
              
              // REMOVED: Cookie updates to eliminate localStorage/cookie desync
              // Auth state managed through localStorage only
              console.log('🚫 Cookie updates disabled - session restored to localStorage only');
              
              console.log('🔄 Restored session from localStorage');
            } catch (error) {
              console.warn('❌ Failed to restore session from token:', error);
              localStorage.removeItem(TOKEN_KEY);
              localStorage.removeItem(REFRESH_TOKEN_KEY);
            }
          }
        }

        setState(prev => ({ ...prev, loading: false }));
      } catch (error) {
        console.error('❌ Error initializing sessions:', error);
        setState(prev => ({ ...prev, loading: false, error: 'Failed to initialize sessions' }));
      }
    };

    initializeSessions();
  }, [isClient]);

  // Login function
  const login = useCallback(async (credentials: { 
    identifier: string; 
    password: string; 
    restaurantId?: string; 
    isStaffLogin?: boolean 
  }): Promise<boolean> => {
    console.log('🔐 Login attempt started:', { 
      identifier: credentials.identifier, 
      isStaffLogin: credentials.isStaffLogin,
      isClient 
    });

    if (!isClient) {
      console.log('❌ Not client side, aborting login');
      return false;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Determine API endpoint
      const endpoint = credentials.isStaffLogin ? 'auth/staff/login' : 'auth/login';
      const apiUrl = getApiUrl(endpoint);
      
      // Debug BUILD_CONFIG
      console.log('🔍 BUILD_CONFIG Debug:', {
        remoteServerUrl: BUILD_CONFIG.remoteServerUrl,
        isDevelopment: BUILD_CONFIG.isDevelopment,
        isStatic: BUILD_CONFIG.isStatic,
        hostname: typeof window !== 'undefined' ? window.location.hostname : 'server',
        apiRoutes: BUILD_CONFIG.features.apiRoutes
      });
      
      if (!apiUrl) {
        console.log('❌ No API URL available - server not reachable');
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: 'Server not available. Try offline mode if you have previous login data.' 
        }));
        return false;
      }
      
      console.log('🌐 Using API endpoint:', apiUrl);
      
      const payload = credentials.isStaffLogin 
        ? { username: credentials.identifier, password: credentials.password }
        : { identifier: credentials.identifier, password: credentials.password };
      
      console.log('📦 Payload:', { ...payload, password: '[HIDDEN]' });

      // Try online login first
      const offlineMode = localStorage.getItem(OFFLINE_MODE_KEY) === 'true';
      console.log('📱 Offline mode:', offlineMode);
      
      // If offline mode is enabled but no sessions exist, reset to online mode
      const existingSessions = multiUserSessionManager.getAllSessions();
      if (offlineMode && existingSessions.length === 0) {
        console.log('🔄 No offline sessions found, switching to online mode');
        localStorage.setItem(OFFLINE_MODE_KEY, 'false');
      }
      
      const shouldTryOnline = !offlineMode || existingSessions.length === 0;
      
      if (shouldTryOnline) {
        try {
          console.log('🚀 Making API request...');
          
          // Try localhost:3000 first
          let response;
          let apiUrl = getApiUrl(endpoint);
          console.log('🌐 Trying primary endpoint:', apiUrl);
          
          try {
            const httpResponse = await httpClient.post(apiUrl, payload, {
              timeout: 5000
            });
            
            // Convert httpClient response to fetch-like response
            response = {
              ok: httpResponse.ok,
              status: httpResponse.status,
              json: async () => httpResponse.data
            } as Response;
          } catch (primaryError) {
            console.log('❌ Primary endpoint failed:', primaryError);
            
            // Only try fallback if we're NOT in development mode
            if (!BUILD_CONFIG.isDevelopment) {
              console.log('🌐 Trying fallback endpoint (production mode)...');
              
              // Fallback to bistro.icu
              apiUrl = getFallbackApiUrl(endpoint);
              console.log('🌐 Fallback endpoint:', apiUrl);
              
              const httpResponse = await httpClient.post(apiUrl, payload, {
                timeout: 8000
              });
              
              // Convert httpClient response to fetch-like response
              response = {
                ok: httpResponse.ok,
                status: httpResponse.status,
                json: async () => httpResponse.data
              } as Response;
            } else {
              console.log('🚫 Development mode - not trying fallback server');
              throw primaryError; // Re-throw the original error
            }
          }

          console.log('📡 API response status:', response.status, response.ok);

          if (response.ok) {
            const data = await response.json();
            console.log('✅ Login API success, got token:', !!data.token);
            
            // Decode user from token
            const decoded = jwtDecode<JwtPayload>(data.token);
            console.log('🔓 Decoded token:', { 
              sub: decoded.sub, 
              name: decoded.name, 
              email: decoded.email, 
              role: decoded.role 
            });
            
            const user: User = {
              id: decoded.sub,
              name: decoded.name,
              email: decoded.email,
              role: decoded.role,
              restaurantId: cleanRestaurantId(String(decoded.restaurantId || '')),
              permissions: decoded.permissions,
              metadata: decoded.metadata
            };

            console.log('👤 Created user object:', user);

            // Add session to manager
            console.log('💾 Adding session to manager...');
            await multiUserSessionManager.addSession(user, data.token, data.refreshToken);
            
            // REMOVED: Cookie updates to eliminate localStorage/cookie desync
            // Auth state managed through localStorage only
            console.log('🚫 Cookie updates disabled - tokens stored in localStorage only');
            
            // Mark as online
            localStorage.setItem(OFFLINE_MODE_KEY, 'false');
            
            setState(prev => ({ ...prev, loading: false, error: null }));
            console.log('🎉 Login successful!');
            return true;
          } else {
            // Handle API error responses (like invalid credentials)
            console.log('❌ API response not ok, status:', response.status);
            try {
              const errorData = await response.json();
              console.log('📄 Error response data:', errorData);
              setState(prev => ({ 
                ...prev, 
                loading: false, 
                error: errorData.error || 'Login failed' 
              }));
              return false;
            } catch (parseError) {
              console.log('❌ Failed to parse error response:', parseError);
              // If we can't parse the error response, show a generic message
              setState(prev => ({ 
                ...prev, 
                loading: false, 
                error: `Login failed (${response.status})` 
              }));
              return false;
            }
          }
        } catch (networkError) {
          console.warn('🌐 Network error during login, trying offline mode:', networkError);
          // Only try offline mode for actual network errors, not API errors
        }
      }

      // For web builds, don't try offline login - show proper error instead
      if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
        console.log('🌐 Web build in dev mode - not trying offline login');
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: 'Authentication failed. Check your internet connection or credentials.'
        }));
        return false;
      }

      // Try offline login if online failed or in offline mode (non-web builds only)
      console.log('📱 Trying offline login...');
      const matchingSession = existingSessions.find(session => {
        const user = session.user;
        return (user.email && user.email === credentials.identifier) ||
               (user.name && user.name === credentials.identifier) ||
               (user.username && user.username === credentials.identifier);
      });

      if (matchingSession) {
        console.log('✅ Found matching offline session:', matchingSession.user.name);
        multiUserSessionManager.switchToUser(matchingSession.user.id);
        multiUserSessionManager.markSessionOffline(matchingSession.id);
        localStorage.setItem(OFFLINE_MODE_KEY, 'true');
        
        setState(prev => ({ ...prev, loading: false, error: null }));
        return true;
      }

      console.log('❌ No matching offline session found');
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: 'Invalid credentials. No offline session available.'
      }));
      return false;

    } catch (error) {
      console.error('❌ Login error:', error);
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error instanceof Error ? error.message : 'Login failed'
      }));
      return false;
    }
  }, [isClient]);

  // Register function
  const register = useCallback(async (userData: { 
    name: string; 
    email: string; 
    password: string; 
    phoneNumber?: string 
  }): Promise<boolean> => {
    if (!isClient) return false;

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // Try localhost:3000 first, then fallback to bistro.icu
      let response;
      let apiUrl = getApiUrl('auth/register');
      console.log('🌐 Trying primary registration endpoint:', apiUrl);
      
      try {
        response = await fetch(apiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: userData.name,
            email: userData.email,
            password: userData.password,
            phoneNumber: userData.phoneNumber
          }),
          signal: AbortSignal.timeout(5000)
        });
      } catch (primaryError) {
        console.log('❌ Primary registration endpoint failed, trying fallback...');
        
        // Fallback to bistro.icu
        apiUrl = getFallbackApiUrl('auth/register');
        console.log('🌐 Trying fallback registration endpoint:', apiUrl);
        
        response = await fetch(apiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: userData.name,
            email: userData.email,
            password: userData.password,
            phoneNumber: userData.phoneNumber
          }),
          signal: AbortSignal.timeout(8000)
        });
      }

      const data = await response.json();

      if (!response.ok) {
        setState(prev => ({
          ...prev,
          loading: false,
          error: data.error || 'Registration failed'
        }));
        return false;
      }

      // Registration successful, now auto-login the user
      const loginSuccess = await login({
        identifier: userData.email,
        password: userData.password
      });

      if (loginSuccess) {
        setState(prev => ({ ...prev, loading: false, error: null }));
      }

      return loginSuccess;

    } catch (error) {
      console.error('❌ Registration error:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Registration failed'
      }));
      return false;
    }
  }, [isClient, login]);

  // Add user session (for adding additional users)
  const addUserSession = useCallback(async (credentials: { 
    identifier: string; 
    password: string; 
    isStaffLogin?: boolean 
  }): Promise<boolean> => {
    return await login(credentials);
  }, [login]);

  // Switch to user
  const switchToUser = useCallback(async (userId: string): Promise<boolean> => {
    const success = await multiUserSessionManager.switchToUser(userId);
    if (success) {
      setState(prev => ({ ...prev, error: null }));
    }
    return success;
  }, []);

  // Remove user session
  const removeUserSession = useCallback((userId: string): void => {
    multiUserSessionManager.removeUserSessions(userId);
  }, []);

  // Logout current user
  const logout = useCallback(() => {
    const activeSession = multiUserSessionManager.getActiveSession();
    if (activeSession) {
      multiUserSessionManager.removeSession(activeSession.id);
    }
    
    // Clear legacy storage
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem('auth_data');
    
    // REMOVED: Cookie clearing to eliminate localStorage/cookie desync
    // Auth state managed through localStorage only
    console.log('🚫 Cookie clearing disabled - localStorage cleared only');
    
    // Always redirect to auth page on logout
    localStorage.removeItem(OFFLINE_MODE_KEY);
    router.push('/auth');
  }, [router]);

  // Logout all users
  const logoutAll = useCallback(() => {
    multiUserSessionManager.clearAllSessions();
    
    // Clear all auth-related storage
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem(OFFLINE_MODE_KEY);
    localStorage.removeItem('auth_data');
    localStorage.removeItem('offline_user');
    
    // REMOVED: Cookie clearing to eliminate localStorage/cookie desync
    // Auth state managed through localStorage only
    console.log('🚫 Cookie clearing disabled - localStorage cleared only');
    
    router.push('/auth');
  }, [router]);

  // Offline login - only use existing auth data
  const offlineLogin = useCallback(async (role: string = 'owner'): Promise<boolean> => {
    if (!isClient) return false;

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      // Check for existing auth data from previous online login
      const existingToken = localStorage.getItem(TOKEN_KEY);
      const existingAuthData = localStorage.getItem('auth_data');
      
      if (!existingToken || !existingAuthData) {
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: 'No previous login found. Please connect to server first.' 
        }));
        return false;
      }
      
      try {
        const decoded = jwtDecode<JwtPayload>(existingToken);
        const authData = JSON.parse(existingAuthData);
        
        // Use existing user data (even if token is expired - offline mode)
        const user: User = {
          id: authData.userId || decoded.sub,
          name: authData.name || decoded.name,
          email: decoded.email || '',
          role: authData.role || decoded.role || role,
          restaurantId: authData.restaurantId || decoded.restaurantId || '',
          permissions: decoded.permissions,
          metadata: { ...decoded.metadata, offline: true }
        };
        
        await multiUserSessionManager.addSession(user, existingToken);
        localStorage.setItem(OFFLINE_MODE_KEY, 'true');
        
        setState(prev => ({ ...prev, loading: false, error: null }));
        console.log('✅ [Auth] Offline login successful with existing account:', user.name);
        return true;
      } catch (parseError) {
        console.error('❌ [Auth] Error parsing existing auth data:', parseError);
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: 'Invalid existing auth data. Please login online first.' 
        }));
        return false;
      }
    } catch (error) {
      console.error('❌ Offline login error:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Offline login failed'
      }));
      return false;
    }
  }, [isClient]);

  // Refresh current session
  const refreshCurrentSession = useCallback(async (): Promise<boolean> => {
    const activeSession = multiUserSessionManager.getActiveSession();
    if (!activeSession || !activeSession.refreshToken) return false;

    try {
      const apiUrl = getApiUrl('auth/refresh');
      
      if (!apiUrl) {
        console.log('❌ No API URL available for token refresh - server not reachable');
        return false;
      }

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken: activeSession.refreshToken }),
      });

      if (response.ok) {
        const data = await response.json();
        multiUserSessionManager.updateSessionToken(activeSession.id, data.token, data.refreshToken);
        return true;
      }
    } catch (error) {
      console.error('❌ Token refresh failed:', error);
    }

    return false;
  }, []);

  // Check restriction status from server (now includes owner inheritance)
  const checkRestrictionStatus = useCallback(async (): Promise<boolean> => {
    const activeSession = multiUserSessionManager.getActiveSession();
    if (!activeSession) return false;

    // Don't check restrictions if we're currently loading
    if (loading) {
      console.log('🔒 [Auth] Skipping restriction check - auth is loading');
      return false;
    }

    console.log('🔒 [Auth] Checking restriction status for user:', activeSession.user.name, 'role:', activeSession.user.role);

    // Always try server first for the most up-to-date status
    try {
      const apiUrl = getApiUrl('auth/check-restriction');
      
      if (apiUrl) {
        console.log('🌐 [Auth] Checking restriction status from server...');
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${activeSession.token}`
          },
          body: JSON.stringify({ 
            userId: activeSession.user.id,
            role: activeSession.user.role,
            restaurantId: activeSession.user.restaurantId
          }),
          signal: AbortSignal.timeout(5000) // Reduced to 5 second timeout
        });

        if (response.ok) {
          const data = await response.json();
          const isRestricted = data.isRestricted === true;
          
          console.log(`🔒 [Auth] Server restriction check result: ${isRestricted ? 'RESTRICTED' : 'NOT RESTRICTED'}`);
          if (data.reason) {
            console.log(`🔒 [Auth] Restriction reason: ${data.reason}${data.ownerName ? ` (owner: ${data.ownerName})` : ''}`);
          }
          
          // Update state with new restriction status
          setState(prev => ({ ...prev, isRestricted }));
          
          return isRestricted;
        } else {
          console.warn('❌ [Auth] Server restriction check failed with status:', response.status);
          // Don't throw error, just fall back to token check
        }
      } else {
        console.log('❌ [Auth] No API URL available for restriction check - server not reachable');
      }
    } catch (error) {
      console.warn('❌ [Auth] Restriction check failed, falling back to token:', error);
      // Don't throw error, just fall back to token check
    }

    // Fallback to JWT token check only if server is unreachable
    // Note: This only checks user's own restriction, not owner inheritance
    try {
      const decoded = jwtDecode<JwtPayload>(activeSession.token);
      const userRestricted = decoded.restricted === true;
      
      console.log(`🔒 [Auth] JWT token restriction check result: ${userRestricted ? 'RESTRICTED' : 'NOT RESTRICTED'}`);
      console.log('⚠️ [Auth] Offline mode - cannot check owner restriction for staff users');
      
      // Update state with token-based restriction status
      setState(prev => ({ ...prev, isRestricted: userRestricted }));
      
      return userRestricted;
    } catch (decodeError) {
      console.warn('❌ [Auth] Failed to decode token for restriction fallback:', decodeError);
      return false;
    }
  }, []);

  // Get session statistics
  const getSessionStats = useCallback(() => {
    return multiUserSessionManager.getSessionStats();
  }, []);

  // Derived properties
  const currentUser = state.currentUser;
  const isAdminUser = isAdmin(currentUser);
  const isOwnerUser = isOwner(currentUser);
  const canManageStaffUser = canManageStaff(currentUser);

  return {
    ...state,
    login,
    register,
    logout,
    logoutAll,
    switchToUser,
    addUserSession,
    removeUserSession,
    offlineLogin,
    refreshCurrentSession,
    checkRestrictionStatus,
    isAdmin: isAdminUser,
    isOwner: isOwnerUser,
    canManageStaff: canManageStaffUser,
    getSessionStats
  };
}