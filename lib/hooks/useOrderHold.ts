/**
 * useOrderHold Hook
 * 
 * Custom React hook for managing order hold state and operations.
 * Provides a clean interface for components to interact with the hold system.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  HoldDocument, 
  UseOrderHoldResult,
  HoldPriority,
  HoldContext,
  ResumeHoldResponse,
  HoldError
} from '../../types/orderHold';

import { holdManager } from '../services/holdManager';

// Import OrderState type for proper typing
type OrderState = any; // This will be properly typed when integrated

/**
 * Custom hook for order hold functionality
 */
export function useOrderHold(): UseOrderHoldResult {
  // State management
  const [heldOrders, setHeldOrders] = useState<HoldDocument[]>([]);
  const [activeHolds, setActiveHolds] = useState<HoldDocument[]>([]);
  const [isHolding, setIsHolding] = useState(false);
  const [isResuming, setIsResuming] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Refs for cleanup and performance
  const mounted = useRef(true);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-refresh interval (every 30 seconds)
  const REFRESH_INTERVAL = 30000;

  /**
   * Set error state with automatic clearing
   */
  const setErrorWithTimeout = useCallback((errorMessage: string | null) => {
    setError(errorMessage);
    
    if (errorMessage) {
      setTimeout(() => {
        if (mounted.current) {
          setError(null);
        }
      }, 5000); // Clear error after 5 seconds
    }
  }, []);

  /**
   * Refresh held orders from storage
   */
  const refreshHolds = useCallback(async () => {
    try {
      if (!mounted.current) return;

      console.log('[useOrderHold] Refreshing holds...');
      
      // Get all holds and active holds in parallel
      const [allHolds, activeHolds] = await Promise.all([
        holdManager.getAllHolds(),
        holdManager.getActiveHolds()
      ]);

      if (mounted.current) {
        setHeldOrders(allHolds);
        setActiveHolds(activeHolds);
        console.log(`[useOrderHold] Refreshed: ${allHolds.length} total, ${activeHolds.length} active`);
      }
    } catch (err) {
      console.error('[useOrderHold] Failed to refresh holds:', err);
      if (mounted.current) {
        setErrorWithTimeout(`Failed to refresh holds: ${err.message}`);
      }
    }
  }, [setErrorWithTimeout]);

  /**
   * Hold current order
   */
  const holdCurrentOrder = useCallback(async (
    orderState: any,
    uiState: any,
    options: {
      label?: string;
      priority?: HoldPriority;
      context?: Partial<HoldContext>;
    } = {}
  ): Promise<string> => {
    if (isHolding) {
      throw new HoldError('Hold operation already in progress', 'OPERATION_IN_PROGRESS');
    }

    setIsHolding(true);
    setError(null);

    try {
      console.log('[useOrderHold] Creating hold...');
      
      const holdId = await holdManager.holdCurrentOrder({
        orderState,
        uiState,
        label: options.label,
        priority: options.priority || 'normal',
        context: options.context
      });

      console.log(`[useOrderHold] Hold created: ${holdId}`);

      // Refresh holds to update UI
      await refreshHolds();

      return holdId;
    } catch (err) {
      console.error('[useOrderHold] Failed to create hold:', err);
      const errorMessage = err instanceof HoldError ? err.message : `Failed to create hold: ${err.message}`;
      setErrorWithTimeout(errorMessage);
      throw err;
    } finally {
      setIsHolding(false);
    }
  }, [isHolding, refreshHolds, setErrorWithTimeout]);

  /**
   * Resume a held order
   */
  const resumeOrder = useCallback(async (holdId: string): Promise<ResumeHoldResponse> => {
    if (isResuming) {
      throw new HoldError('Resume operation already in progress', 'OPERATION_IN_PROGRESS');
    }

    setIsResuming(true);
    setError(null);

    try {
      console.log(`[useOrderHold] Resuming order: ${holdId}`);
      
      const resumeResponse = await holdManager.resumeOrder(holdId);

      console.log(`[useOrderHold] Order resumed: ${holdId}`);

      // Refresh holds to update UI
      await refreshHolds();

      return resumeResponse;
    } catch (err) {
      console.error(`[useOrderHold] Failed to resume order ${holdId}:`, err);
      const errorMessage = err instanceof HoldError ? err.message : `Failed to resume order: ${err.message}`;
      setErrorWithTimeout(errorMessage);
      throw err;
    } finally {
      setIsResuming(false);
    }
  }, [isResuming, refreshHolds, setErrorWithTimeout]);

  /**
   * Clear a held order
   */
  const clearHold = useCallback(async (holdId: string): Promise<boolean> => {
    setError(null);

    try {
      console.log(`[useOrderHold] Clearing hold: ${holdId}`);
      
      const result = await holdManager.clearHold(holdId);

      if (result) {
        console.log(`[useOrderHold] Hold cleared: ${holdId}`);
        
        // Refresh holds to update UI
        await refreshHolds();
      }

      return result;
    } catch (err) {
      console.error(`[useOrderHold] Failed to clear hold ${holdId}:`, err);
      const errorMessage = err instanceof HoldError ? err.message : `Failed to clear hold: ${err.message}`;
      setErrorWithTimeout(errorMessage);
      return false;
    }
  }, [refreshHolds, setErrorWithTimeout]);

  /**
   * Clear expired holds
   */
  const clearExpiredHolds = useCallback(async (): Promise<number> => {
    try {
      console.log('[useOrderHold] Clearing expired holds...');
      
      const clearedCount = await holdManager.clearExpiredHolds();

      if (clearedCount > 0) {
        console.log(`[useOrderHold] Cleared ${clearedCount} expired holds`);
        
        // Refresh holds to update UI
        await refreshHolds();
      }

      return clearedCount;
    } catch (err) {
      console.error('[useOrderHold] Failed to clear expired holds:', err);
      const errorMessage = err instanceof HoldError ? err.message : `Failed to clear expired holds: ${err.message}`;
      setErrorWithTimeout(errorMessage);
      return 0;
    }
  }, [refreshHolds, setErrorWithTimeout]);

  /**
   * Update hold label
   */
  const updateHoldLabel = useCallback(async (holdId: string, label: string): Promise<boolean> => {
    try {
      console.log(`[useOrderHold] Updating hold label: ${holdId} -> ${label}`);
      
      const result = await holdManager.updateHoldLabel(holdId, label);

      if (result) {
        // Refresh holds to update UI
        await refreshHolds();
      }

      return result;
    } catch (err) {
      console.error(`[useOrderHold] Failed to update hold label ${holdId}:`, err);
      setErrorWithTimeout(`Failed to update label: ${err.message}`);
      return false;
    }
  }, [refreshHolds, setErrorWithTimeout]);

  /**
   * Update hold priority
   */
  const updateHoldPriority = useCallback(async (holdId: string, priority: HoldPriority): Promise<boolean> => {
    try {
      console.log(`[useOrderHold] Updating hold priority: ${holdId} -> ${priority}`);
      
      const result = await holdManager.updateHoldPriority(holdId, priority);

      if (result) {
        // Refresh holds to update UI
        await refreshHolds();
      }

      return result;
    } catch (err) {
      console.error(`[useOrderHold] Failed to update hold priority ${holdId}:`, err);
      setErrorWithTimeout(`Failed to update priority: ${err.message}`);
      return false;
    }
  }, [refreshHolds, setErrorWithTimeout]);

  /**
   * Handle global hold events
   */
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleOrderHeld = () => {
      console.log('[useOrderHold] Received order-held event');
      refreshHolds();
    };

    const handleOrderResumed = () => {
      console.log('[useOrderHold] Received order-resumed event');
      refreshHolds();
    };

    const handleHoldCleared = () => {
      console.log('[useOrderHold] Received order-hold-cleared event');
      refreshHolds();
    };

    const handleHoldUpdated = () => {
      console.log('[useOrderHold] Received hold-updated event');
      refreshHolds();
    };

    const handleHoldsCleaned = () => {
      console.log('[useOrderHold] Received holds-cleaned event');
      refreshHolds();
    };

    // Register event listeners
    window.addEventListener('order-held', handleOrderHeld);
    window.addEventListener('order-resumed', handleOrderResumed);
    window.addEventListener('order-hold-cleared', handleHoldCleared);
    window.addEventListener('hold-updated', handleHoldUpdated);
    window.addEventListener('holds-cleaned', handleHoldsCleaned);

    return () => {
      window.removeEventListener('order-held', handleOrderHeld);
      window.removeEventListener('order-resumed', handleOrderResumed);
      window.removeEventListener('order-hold-cleared', handleHoldCleared);
      window.removeEventListener('hold-updated', handleHoldUpdated);
      window.removeEventListener('holds-cleaned', handleHoldsCleaned);
    };
  }, [refreshHolds]);

  /**
   * Initial load and periodic refresh
   */
  useEffect(() => {
    // Initial load
    refreshHolds();

    // Set up periodic refresh
    const setupPeriodicRefresh = () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }

      refreshTimeoutRef.current = setTimeout(() => {
        if (mounted.current) {
          refreshHolds();
          setupPeriodicRefresh(); // Schedule next refresh
        }
      }, REFRESH_INTERVAL);
    };

    setupPeriodicRefresh();

    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [refreshHolds]);

  /**
   * Auto-cleanup expired holds (every 5 minutes)
   */
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      if (mounted.current) {
        clearExpiredHolds().catch(err => {
          console.warn('[useOrderHold] Auto-cleanup failed:', err);
        });
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => {
      clearInterval(cleanupInterval);
    };
  }, [clearExpiredHolds]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      mounted.current = false;
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  /**
   * Log state changes for debugging
   */
  useEffect(() => {
    console.log(`[useOrderHold] State update: ${heldOrders.length} total, ${activeHolds.length} active`);
  }, [heldOrders.length, activeHolds.length]);

  return {
    // State
    heldOrders,
    activeHolds,
    isHolding,
    isResuming,
    error,

    // Operations
    holdCurrentOrder,
    resumeOrder,
    clearHold,

    // Queue management
    refreshHolds,
    clearExpiredHolds,

    // Metadata operations
    updateHoldLabel,
    updateHoldPriority
  };
}

/**
 * Performance-optimized version with selective updates
 */
export function useOrderHoldOptimized(options: {
  autoRefresh?: boolean;
  refreshInterval?: number;
  maxHolds?: number;
} = {}) {
  const {
    autoRefresh = true,
    refreshInterval = 30000,
    maxHolds = 50
  } = options;

  const result = useOrderHold();

  // Limit the number of holds to prevent performance issues
  const limitedHeldOrders = result.heldOrders.slice(0, maxHolds);
  const limitedActiveHolds = result.activeHolds.slice(0, maxHolds);

  return {
    ...result,
    heldOrders: limitedHeldOrders,
    activeHolds: limitedActiveHolds
  };
}