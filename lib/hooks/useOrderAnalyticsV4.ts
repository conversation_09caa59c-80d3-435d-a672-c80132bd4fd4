"use client";

import { useState, useEffect } from 'react';
import { getAllOrders } from '@/lib/db/v4/operations/order-ops';
import type { OrderDocument } from '@/lib/db/v4/schemas/order-schema';
import { getAllMenuItemRecipes, getEffectiveCost } from '@/lib/db/v4/operations/menu-item-recipe-ops';
import { getHours, parseISO } from 'date-fns';

// Types
export interface SaleItem {
  id: string;
  name: string;
  category: string;
  quantity: number;
  sales: number;
  unitPrice: number;
  profit: number;
  profitMargin: number;
  profitabilityScore?: number;
  contributionToTotalProfit?: number;
}

export interface SalesByOrderType {
  type: string;
  orderCount: number;
  totalSales: number;
  avgTicket: number;
  percentage: number;
}

export interface PeakHour {
  hour: string;
  sales: number;
  orders: number;
  avgTicket: number;
}

export interface KPIs {
  netSales: number;
  orderCount: number;
  avgTicket: number;
  avgItemsPerOrder: number;
  totalProfit?: number;
  grossProfitMargin?: number;
}

export interface OrderAnalyticsData {
  salesByItem: SaleItem[];
  salesByOrderType: SalesByOrderType[];
  peakHours: PeakHour[];
  orders: OrderDocument[];
  kpis: KPIs;
  metadata: {
    restaurantId: string;
    startDate: string;
    endDate: string;
    totalOrdersProcessed: number;
    completedOrders: number;
  };
}

/**
 * Custom hook for fetching and processing order analytics
 */
export function useOrderAnalyticsV4(startDate?: string, endDate?: string, restaurantId: string = 'default') {
  const [data, setData] = useState<OrderAnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrderData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        console.log('🔍 [Analytics] Starting analytics data fetch...');
        console.log('📅 [Analytics] Date range:', { startDate, endDate });
        
        // Use database-level filtering for better performance
        const orders = await getAllOrders(startDate, endDate);
        console.log(`📊 [Analytics] Fetched ${orders.length} orders from database`);
        
        // No need for additional filtering since it's done at database level
        
        console.log('📊 [Analytics] Order status breakdown:', 
          orders.reduce((acc, o) => {
            acc[o.status] = (acc[o.status] || 0) + 1;
            return acc;
          }, {} as Record<string, number>)
        );

        // Process the real data
        if (!orders || orders.length === 0) {
          console.log('📊 [Analytics] No orders found - returning empty structure');
          setData(createEmptyAnalyticsData(restaurantId, startDate, endDate));
          setIsLoading(false);
          return;
        }

        console.log('🔄 [Analytics] Processing order data...');
        console.log('📊 [Analytics] Orders to process:', {
          total: orders.length,
          byStatus: orders.reduce((acc, o) => {
            acc[o.status] = (acc[o.status] || 0) + 1;
            return acc;
          }, {} as Record<string, number>),
          completedOrders: orders.filter(o => o.status === 'completed').length,
          sampleCompletedOrders: orders.filter(o => o.status === 'completed').slice(0, 2).map(o => ({
            id: o._id,
            total: o.total,
            items: o.items?.length || 0,
            createdAt: o.createdAt
          }))
        });
        
        // Batch fetch all recipes once to avoid N+1 queries
        console.log('🔄 [Analytics] Fetching recipes for COGS calculation...');
        const allRecipes = await getAllMenuItemRecipes();
        const recipeMap = new Map();
        allRecipes.forEach(recipe => {
          recipeMap.set(recipe.menuItemId, recipe);
        });
        console.log(`📊 [Analytics] Loaded ${allRecipes.length} recipes`);
        
        // Process real data for analytics with optimized recipe lookups
        const salesByItem = await calculateSalesByItemOptimized(orders, recipeMap);
        const salesByOrderType = calculateSalesByOrderType(orders);
        const peakHours = calculatePeakHours(orders);
        const kpis = calculateKPIs(orders, salesByItem);
        
        console.log('📊 [Analytics] Processing results:', {
          salesByItemCount: salesByItem.length,
          salesByOrderTypeCount: salesByOrderType.length,
          kpis: kpis,
          sampleSalesByItem: salesByItem.slice(0, 3)
        });
        
        // Only completed orders for visualizations
        const completedOrdersForHeatmap = orders.filter(o => o.status === 'completed');

        console.log('📈 [Analytics] Real data processed:', {
          salesByItemCount: salesByItem.length,
          salesByOrderTypeCount: salesByOrderType.length,
          completedOrdersCount: completedOrdersForHeatmap.length,
          totalRevenue: kpis.netSales
        });

        setData({
          salesByItem,
          salesByOrderType,
          peakHours,
          orders: completedOrdersForHeatmap,
          kpis,
          metadata: {
            restaurantId,
            startDate: startDate || 'all',
            endDate: endDate || 'all',
            totalOrdersProcessed: orders.length,
            completedOrders: completedOrdersForHeatmap.length
          }
        });

        console.log('✅ [Analytics] Real data processing completed');
      } catch (e) {
        console.error("❌ [Analytics] Error fetching real analytics data:", e);
        setError(e instanceof Error ? e.message : 'Unknown error occurred while loading analytics data');
        setData(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrderData();
  }, [startDate, endDate, restaurantId]);

  return { data, isLoading, error };
}

// 🚀 Enhancement 2: Analytics indexes are now created automatically during DB initialization
// The old createAnalyticsIndexes function has been removed since indexes are part of essential indexes

// Helper to create empty data structure
function createEmptyAnalyticsData(restaurantId: string, startDate?: string, endDate?: string): OrderAnalyticsData {
  const emptyPeakHours = Array(24).fill(null).map((_, i) => ({ 
    hour: `${String(i).padStart(2, '0')}:00`, 
    sales: 0, 
    orders: 0, 
    avgTicket: 0 
  }));
  
  return {
    salesByItem: [],
    salesByOrderType: [],
    peakHours: emptyPeakHours,
    orders: [],
    kpis: { 
      netSales: 0, 
      orderCount: 0, 
      avgTicket: 0, 
      avgItemsPerOrder: 0, 
      totalProfit: 0, 
      grossProfitMargin: 0 
    },
    metadata: {
      restaurantId,
      startDate: startDate || 'all',
      endDate: endDate || 'all',
      totalOrdersProcessed: 0,
      completedOrders: 0
    }
  };
}

// Helper functions for processing order data
async function calculateSalesByItem(orders: OrderDocument[]): Promise<SaleItem[]> {
  const itemMap = new Map<string, SaleItem>();
  
  // Fetch all recipes once for efficient lookups
  const recipes = await getAllMenuItemRecipes();
  const recipeMap = new Map();
  recipes.forEach(recipe => {
    recipeMap.set(recipe.menuItemId, recipe);
  });
  
  console.log(`[Analytics] Loaded ${recipes.length} recipes for cost calculation`);
  
  return calculateSalesByItemOptimized(orders, recipeMap);
}

// Optimized version that accepts recipes map to avoid repeated DB calls
function calculateSalesByItemOptimized(orders: OrderDocument[], recipeMap: Map<string, any>): SaleItem[] {
  const itemMap = new Map<string, SaleItem>();
  
  console.log(`[Analytics] Processing ${orders.length} orders with ${recipeMap.size} recipes loaded`);
  
  // Only process completed and paid orders
  const processableOrders = orders.filter(order => 
    order.status === 'completed' && 
    (order.paymentStatus === 'paid' || order.paymentStatus === 'partially_paid')
  );
  
  console.log(`[Analytics] Processing ${processableOrders.length} completed/paid orders`);
  
  processableOrders.forEach(order => {
    order.items.forEach(item => {
      // Skip voided items
      if (item.isVoided) return;
      
      const effectiveQuantity = (item.originalQuantity || item.quantity) - (item.voidedQuantity || 0);
      if (effectiveQuantity <= 0) return;
      
      const existingItem = itemMap.get(item.menuItemId);
      
      // 🚀 HYBRID COSTING: Historical > Fixed > Recipe > Zero
      const recipe = recipeMap.get(item.menuItemId);
      let itemCogs = 0;
      let costSource = 'none';
      
      // Priority 1: Historical order COGS (most accurate)
      if (item.cogs && item.cogs > 0) {
        itemCogs = item.cogs;
        costSource = 'historical';
      }
      // Priority 2: Fixed cost (global, stable)
      else if (recipe?.costingMethod === 'fixed' && recipe.fixedCost && recipe.fixedCost > 0) {
        itemCogs = recipe.fixedCost;
        costSource = 'fixed';
      }
      // Priority 3: Recipe calculation (requires purchase history)
      else if (recipe) {
        itemCogs = getEffectiveCost(recipe);
        costSource = itemCogs > 0 ? 'recipe' : 'zero';
      }
      
      const itemSales = item.price * effectiveQuantity;
      const itemProfit = itemCogs > 0 ? itemSales - (itemCogs * effectiveQuantity) : 0;
      
      if (existingItem) {
        existingItem.quantity += effectiveQuantity;
        existingItem.sales += itemSales;
        existingItem.profit += itemProfit;
      } else {
        itemMap.set(item.menuItemId, {
          id: item.menuItemId,
          name: item.name,
          category: item.category || 'N/A',
          quantity: effectiveQuantity,
          sales: itemSales,
          unitPrice: item.price,
          profit: itemProfit,
          profitMargin: item.price > 0 && itemCogs > 0 ? ((item.price - itemCogs) / item.price) * 100 : 0,
        });
      }
    });
  });
  
  let allItems = Array.from(itemMap.values());
  const totalProfit = allItems.reduce((sum, item) => sum + item.profit, 0);
  
  console.log(`[Analytics] Calculated ${allItems.length} unique items, total profit: ${totalProfit}`);
  
  // Calculate advanced metrics
  allItems = allItems.map(item => ({
    ...item,
    profitabilityScore: item.quantity > 0 ? parseFloat((item.profit / item.quantity).toFixed(2)) : 0,
    contributionToTotalProfit: totalProfit > 0 ? parseFloat(((item.profit / totalProfit) * 100).toFixed(2)) : 0,
    profitMargin: item.sales > 0 ? parseFloat(((item.profit / item.sales) * 100).toFixed(2)) : 0,
  }));
  
  return allItems.sort((a, b) => b.sales - a.sales); // Sort by sales descending
}

function calculateSalesByOrderType(orders: OrderDocument[]): SalesByOrderType[] {
  const typeMap = new Map<string, { count: number; totalSales: number; totalItems: number }>();
  
  // Only process completed and paid orders
  const processableOrders = orders.filter(order => 
    order.status === 'completed' && 
    (order.paymentStatus === 'paid' || order.paymentStatus === 'partially_paid')
  );
  
  processableOrders.forEach(order => {
    const orderType = order.orderType || 'unknown';
    const existingType = typeMap.get(orderType);
    
    // Calculate effective sales amount (consider discounts and voids)
    const effectiveSales = order.paymentDetails?.amountPaid || order.total;
    const orderTotalItems = order.items.reduce((sum, item) => {
      const effectiveQuantity = (item.originalQuantity || item.quantity) - (item.voidedQuantity || 0);
      return sum + (item.isVoided ? 0 : effectiveQuantity);
    }, 0);
    
    if (existingType) {
      existingType.count += 1;
      existingType.totalSales += effectiveSales;
      existingType.totalItems += orderTotalItems;
    } else {
      typeMap.set(orderType, {
        count: 1,
        totalSales: effectiveSales,
        totalItems: orderTotalItems,
      });
    }
  });
  
  const results: SalesByOrderType[] = [];
  const totalOrders = processableOrders.length;
  
  typeMap.forEach((data, type) => {
    results.push({
      type: type,
      orderCount: data.count,
      totalSales: data.totalSales,
      avgTicket: data.count > 0 ? data.totalSales / data.count : 0,
      percentage: totalOrders > 0 ? (data.count / totalOrders) * 100 : 0,
    });
  });
  
  return results.sort((a, b) => b.totalSales - a.totalSales);
}

function calculatePeakHours(orders: OrderDocument[]): PeakHour[] {
  const hoursMap = new Map<number, { sales: number; orders: number }>();
  for (let i = 0; i < 24; i++) {
    hoursMap.set(i, { sales: 0, orders: 0 });
  }
  
  // Only process completed and paid orders
  const processableOrders = orders.filter(order => 
    order.status === 'completed' && 
    (order.paymentStatus === 'paid' || order.paymentStatus === 'partially_paid')
  );
  
  processableOrders.forEach(order => {
    const hour = getHours(parseISO(order.createdAt));
    const currentHourData = hoursMap.get(hour);
    if (currentHourData) {
      const effectiveSales = order.paymentDetails?.amountPaid || order.total;
      currentHourData.sales += effectiveSales;
      currentHourData.orders += 1;
    }
  });
  
  const results: PeakHour[] = [];
  hoursMap.forEach((data, hour) => {
    results.push({
      hour: `${String(hour).padStart(2, '0')}:00`,
      sales: data.sales,
      orders: data.orders,
      avgTicket: data.orders > 0 ? data.sales / data.orders : 0,
    });
  });
  
  return results.sort((a,b) => parseInt(a.hour) - parseInt(b.hour));
}

function calculateKPIs(orders: OrderDocument[], salesByItem: SaleItem[]): KPIs {
  // Only process completed and paid orders
  const processableOrders = orders.filter(order => 
    order.status === 'completed' && 
    (order.paymentStatus === 'paid' || order.paymentStatus === 'partially_paid')
  );
  
  // Calculate net sales using actual paid amounts
  const netSales = processableOrders.reduce((sum, order) => {
    const effectiveSales = order.paymentDetails?.amountPaid || order.total;
    return sum + effectiveSales;
  }, 0);
  
  const orderCount = processableOrders.length;
  
  // Calculate total items sold excluding voided items
  const totalItemsSold = processableOrders.reduce((sum, order) => 
    sum + order.items.reduce((itemSum, item) => {
      if (item.isVoided) return itemSum;
      const effectiveQuantity = (item.originalQuantity || item.quantity) - (item.voidedQuantity || 0);
      return itemSum + Math.max(0, effectiveQuantity);
    }, 0), 0);
  
  // 🚀 FIX: Always use recipe-based profit calculation from salesByItem
  // This now includes fixed cost feature integration
  const totalProfit = salesByItem.reduce((acc, item) => acc + item.profit, 0);
    
  const grossProfitMargin = netSales > 0 ? (totalProfit / netSales) * 100 : 0;
  
  console.log(`[Analytics] KPIs calculated: sales=${netSales}, profit=${totalProfit}, margin=${grossProfitMargin.toFixed(2)}%`);
  
  return {
    netSales,
    orderCount,
    avgTicket: orderCount > 0 ? netSales / orderCount : 0,
    avgItemsPerOrder: orderCount > 0 ? totalItemsSold / orderCount : 0,
    totalProfit,
    grossProfitMargin,
  };
} 