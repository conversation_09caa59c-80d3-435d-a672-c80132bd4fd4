/**
 * Unified Sync Hook 👑
 * 
 * Single hook for all sync operations:
 * - Leader-based sync (prevents split-brain)
 * - Auto-discovery and connection
 * - Real-time status updates
 * - Cross-platform support (LAN + Internet)
 */

import { useState, useEffect, useCallback } from 'react';
import { leaderSyncService, type LeaderConfig, type LeaderStatus } from '@/lib/services/leader-sync-service';

interface UseSyncReturn {
  // Status
  status: LeaderStatus;
  isRunning: boolean;
  isConnected: boolean;
  isSyncing: boolean;
  connectionType: 'local' | 'internet' | null;
  
  // Leader info
  currentLeader: string | null; // IP address of current leader
  isThisDeviceLeader: boolean;
  leaderEpoch: number | null;
  
  // Discovery info
  localServers: number;
  internetPeers: number;
  lastLocalDiscovery: Date | null;
  lastInternetDiscovery: Date | null;
  
  // Sync statistics
  docsReceived: number;
  docsSent: number;
  lastSync: Date | null;
  
  // Error state
  error: string | null;
  
  // Control functions
  start: () => Promise<void>;
  stop: () => Promise<void>;
  discover: () => Promise<void>;
  updateConfig: (config: Partial<LeaderConfig>) => void;
  
  // Quick status checks
  hasLeader: boolean;
  isFollower: boolean;
}

interface UseSyncOptions {
  autoStart?: boolean;
  config?: Partial<LeaderConfig>;
}

export function useSync(options: UseSyncOptions = {}): UseSyncReturn {
  const [status, setStatus] = useState<LeaderStatus>(leaderSyncService.getStatus());
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Subscribe to status updates
  useEffect(() => {
    const unsubscribe = leaderSyncService.onStatusChange((newStatus) => {
      setStatus(newStatus);
      
      if (newStatus.error) {
        setError(newStatus.error);
      } else if (error && newStatus.currentLeader) {
        setError(null);
      }
    });

    return unsubscribe;
  }, [error]);

  // Initialize with config
  useEffect(() => {
    if (options.config && !isInitialized) {
      const defaultConfig: LeaderConfig = {
        localDiscoveryInterval: 30000,
        localReconnectInterval: 60000,
        internetDiscoveryInterval: 120000,
        internetReconnectInterval: 180000,
        maxReconnectAttempts: 5,
        autoStart: options.autoStart ?? false,
        preferLocalSync: true,
        vpsBaseUrl: 'https://bistro.icu',
        authToken: '',
        deviceId: '',
        deviceType: 'mobile',
        ...options.config
      };

      leaderSyncService.initialize(defaultConfig).then(() => {
        setIsInitialized(true);
      }).catch(err => {
        console.error('Failed to initialize leader sync:', err);
        setError(err.message);
      });
    }
  }, [options.config, options.autoStart, isInitialized]);

  const start = useCallback(async () => {
    try {
      setError(null);
      await leaderSyncService.start();
    } catch (err: any) {
      console.error('Failed to start sync:', err);
      setError(err.message || 'Failed to start sync');
      throw err;
    }
  }, []);

  const stop = useCallback(async () => {
    try {
      await leaderSyncService.stop();
      setError(null);
    } catch (err: any) {
      console.error('Failed to stop sync:', err);
      setError(err.message || 'Failed to stop sync');
      throw err;
    }
  }, []);

  const discover = useCallback(async () => {
    try {
      setError(null);
      await leaderSyncService.discover();
    } catch (err: any) {
      console.error('Discovery failed:', err);
      setError(err.message || 'Discovery failed');
      throw err;
    }
  }, []);

  const updateConfig = useCallback((config: Partial<LeaderConfig>) => {
    leaderSyncService.updateConfig(config);
  }, []);

  return {
    // Status
    status,
    isRunning: status.isRunning,
    isConnected: status.syncStatus.connected,
    isSyncing: status.syncStatus.syncing,
    connectionType: status.currentConnection.type,
    
    // Leader info
    currentLeader: status.currentLeader?.ip || null,
    isThisDeviceLeader: leaderSyncService.isConnected() && status.currentConnection.server?.ip === '127.0.0.1',
    leaderEpoch: status.currentLeader?.leaderEpoch || null,
    
    // Discovery info
    localServers: status.localServers.length,
    internetPeers: status.internetPeers.length,
    lastLocalDiscovery: status.lastLocalDiscovery,
    lastInternetDiscovery: status.lastInternetDiscovery,
    
    // Sync statistics
    docsReceived: status.syncStatus.docsReceived,
    docsSent: status.syncStatus.docsSent,
    lastSync: status.syncStatus.lastSync,
    
    // Error state
    error: error || status.error,
    
    // Control functions
    start,
    stop,
    discover,
    updateConfig,
    
    // Quick status checks
    hasLeader: !!status.currentLeader,
    isFollower: !leaderSyncService.isConnected() && !!status.currentLeader
  };
}

// 🏠 Simplified hook for LAN-only sync
export function useLANSync(options: { autoStart?: boolean } = {}) {
  return useSync({
    autoStart: options.autoStart ?? true,
    config: {
      localDiscoveryInterval: 30000,
      localReconnectInterval: 60000,
      internetDiscoveryInterval: 999999999, // Disabled
      internetReconnectInterval: 999999999,
      maxReconnectAttempts: 5,
      preferLocalSync: true,
      vpsBaseUrl: '',
      authToken: '',
      deviceId: `lan-${Date.now()}`,
      deviceType: 'mobile'
    }
  });
}

// 🌐 Simplified hook for Internet-only sync  
export function useInternetSync(config: {
  authToken: string;
  deviceId: string;
  deviceType: 'desktop' | 'mobile';
  vpsBaseUrl?: string;
  deviceRegistration?: {
    ipAddress: string;
    couchdbPort?: number;
  };
  autoStart?: boolean;
}) {
  return useSync({
    autoStart: config.autoStart ?? true,
    config: {
      localDiscoveryInterval: 999999999, // Disabled
      localReconnectInterval: 999999999,
      internetDiscoveryInterval: 60000,
      internetReconnectInterval: 120000,
      maxReconnectAttempts: 3,
      preferLocalSync: false,
      vpsBaseUrl: config.vpsBaseUrl || 'https://bistro.icu',
      authToken: config.authToken,
      deviceId: config.deviceId,
      deviceType: config.deviceType,
      deviceRegistration: config.deviceRegistration
    }
  });
}

// 🔄 Full hybrid sync (LAN + Internet)
export function useHybridSync(config: {
  authToken: string;
  deviceId: string;
  deviceType: 'desktop' | 'mobile';
  vpsBaseUrl?: string;
  deviceRegistration?: {
    ipAddress: string;
    couchdbPort?: number;
  };
  autoStart?: boolean;
}) {
  return useSync({
    autoStart: config.autoStart ?? true,
    config: {
      localDiscoveryInterval: 30000,
      localReconnectInterval: 60000,
      internetDiscoveryInterval: 120000,
      internetReconnectInterval: 180000,
      maxReconnectAttempts: 5,
      preferLocalSync: true, // 🏠 Prefer LAN when available
      vpsBaseUrl: config.vpsBaseUrl || 'https://bistro.icu',
      authToken: config.authToken,
      deviceId: config.deviceId,
      deviceType: config.deviceType,
      deviceRegistration: config.deviceRegistration
    }
  });
}

export type { UseSyncReturn, UseSyncOptions };