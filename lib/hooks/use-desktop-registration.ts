'use client';

import { useEffect, useState } from 'react';
import { simpleDesktopRegistration } from '@/lib/services/simple-desktop-registration';

interface RegistrationStatus {
  deviceId: string | null;
  isRegistered: boolean;
  lastRegistration: Date | null;
  lastHeartbeat: Date | null;
  failureCount: number;
  error: string | null;
}

export function useDesktopRegistration() {
  const [status, setStatus] = useState<RegistrationStatus>({
    deviceId: null,
    isRegistered: false,
    lastRegistration: null,
    lastHeartbeat: null,
    failureCount: 0,
    error: null
  });

  useEffect(() => {
    console.log('🪝 [useDesktopRegistration] Hook initialized');

    // Start the registration service immediately
    simpleDesktopRegistration.start().catch(error => {
      console.error('🪝 [useDesktopRegistration] Failed to start:', error);
    });

    // Poll status every 10 seconds
    const statusInterval = setInterval(() => {
      const currentStatus = simpleDesktopRegistration.getStatus();
      setStatus(currentStatus);
    }, 10000);

    // Get initial status
    const initialStatus = simpleDesktopRegistration.getStatus();
    setStatus(initialStatus);

    // Cleanup on unmount
    return () => {
      clearInterval(statusInterval);
      simpleDesktopRegistration.stop();
    };
  }, []);

  const forceRegistration = async () => {
    try {
      const success = await simpleDesktopRegistration.forceRegistration();
      const newStatus = simpleDesktopRegistration.getStatus();
      setStatus(newStatus);
      return success;
    } catch (error) {
      console.error('🪝 [useDesktopRegistration] Force registration failed:', error);
      return false;
    }
  };

  return {
    ...status,
    forceRegistration
  };
}