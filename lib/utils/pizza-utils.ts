import { PizzaQuarter } from '@/lib/db/v4/schemas/order-schema';

/**
 * Calculate the total price for a custom pizza based on its quarters
 * @param quarters Array of 4 pizza quarters (can contain null for empty quarters)
 * @param pricingMethod Either 'max' (highest quarter price), 'average' (average of all quarter prices), or 'fixed' (fixed price per size)
 * @returns The calculated price for the custom pizza
 */
export function calculateCustomPizzaPrice(
  quarters: (PizzaQuarter | null)[],
  pricingMethod: 'max' | 'average' | 'fixed',
  fixedPrice?: number
): number {
  // Filter out null quarters and get prices
  const validQuarters = quarters.filter((q): q is PizzaQuarter => q !== null);
  
  if (validQuarters.length === 0) {
    return 0;
  }
  
  const prices = validQuarters.map(q => q.price);
  
  switch (pricingMethod) {
    case 'max':
      return Math.max(...prices);
    
    case 'average':
      return prices.reduce((sum, price) => sum + price, 0) / prices.length;
      
    case 'fixed':
      return fixedPrice || 0;
    
    default:
      return Math.max(...prices);
  }
}

/**
 * Generate a display name for a custom pizza based on its quarters
 * @param quarters Array of 4 pizza quarters
 * @returns A formatted name like "Pizza Personnalisée (Margherita + 4 Fromages + Végétarienne)"
 */
export function generateCustomPizzaName(quarters: (PizzaQuarter | null)[]): string {
  const validQuarters = quarters.filter((q): q is PizzaQuarter => q !== null);
  
  if (validQuarters.length === 0) {
    return "Pizza Personnalisée (Vide)";
  }
  
  if (validQuarters.length === 1) {
    return `Pizza Personnalisée (${validQuarters[0].name})`;
  }
  
  const names = validQuarters.map(q => q.name);
  return `Pizza Personnalisée (${names.join(' + ')})`;
}

/**
 * Validate that all quarters are properly configured
 * @param quarters Array of 4 pizza quarters
 * @returns Object with validation status and error message if any
 */
export function validateCustomPizza(quarters: (PizzaQuarter | null)[]): {
  isValid: boolean;
  error?: string;
} {
  const validQuarters = quarters.filter((q): q is PizzaQuarter => q !== null);
  
  if (validQuarters.length === 0) {
    return {
      isValid: false,
      error: "Au moins un quart doit être sélectionné"
    };
  }
  
  // Check if all quarters have valid prices
  const hasInvalidPrice = validQuarters.some(q => !q.price || q.price <= 0);
  if (hasInvalidPrice) {
    return {
      isValid: false,
      error: "Tous les quarts doivent avoir un prix valide"
    };
  }
  
  return { isValid: true };
}

/**
 * Get quarter position label for UI display
 * @param index Quarter index (0-3)
 * @returns French label for the quarter position
 */
export function getQuarterLabel(index: number): string {
  const labels = [
    "Quart Supérieur Gauche",
    "Quart Supérieur Droit", 
    "Quart Inférieur Gauche",
    "Quart Inférieur Droit"
  ];
  
  return labels[index] || `Quart ${index + 1}`;
}

/**
 * Get SVG path for a specific quarter in the pizza circle
 * @param index Quarter index (0-3)
 * @returns SVG path string for the quarter
 */
export function getQuarterPath(index: number): string {
  const centerX = 50;
  const centerY = 50;
  const radius = 45;
  
  // Calculate angles for each quarter (in degrees)
  const startAngle = index * 90 - 90; // Start from top
  const endAngle = startAngle + 90;
  
  // Convert to radians
  const startRad = (startAngle * Math.PI) / 180;
  const endRad = (endAngle * Math.PI) / 180;
  
  // Calculate points
  const x1 = centerX + radius * Math.cos(startRad);
  const y1 = centerY + radius * Math.sin(startRad);
  const x2 = centerX + radius * Math.cos(endRad);
  const y2 = centerY + radius * Math.sin(endRad);
  
  // Create arc path
  return `M ${centerX} ${centerY} L ${x1} ${y1} A ${radius} ${radius} 0 0 1 ${x2} ${y2} Z`;
} 