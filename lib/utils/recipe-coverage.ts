"use client";

import { OrderDocument } from '@/lib/db/v4/schemas/order-schema';
import { MenuItemRecipe } from '@/types/cogs';
import { getAllMenuItemRecipes } from '@/lib/db/v4';

export interface RecipeCoverageResult {
  hasCompleteRecipes: boolean;
  missingRecipeItems: {
    id: string;
    name: string;
    category: string;
    salesAmount: number;
    revenuePercentage: number;
  }[];
  totalRevenue: number;
}

export async function validateRecipeCoverage(orders: OrderDocument[]): Promise<RecipeCoverageResult> {
  // Only process completed and paid orders
  const processableOrders = orders.filter(order => 
    order.status === 'completed' && 
    (order.paymentStatus === 'paid' || order.paymentStatus === 'partially_paid')
  );

  // Get all recipes
  const recipes = await getAllMenuItemRecipes();
  const recipeMap = new Map<string, MenuItemRecipe>();
  recipes.forEach(recipe => {
    recipeMap.set(recipe.menuItemId, recipe);
  });

  // Calculate sales by item
  const itemSalesMap = new Map<string, {
    name: string;
    category: string;
    salesAmount: number;
  }>();

  let totalRevenue = 0;

  processableOrders.forEach(order => {
    const effectiveSales = order.paymentDetails?.amountPaid || order.total;
    totalRevenue += effectiveSales;

    order.items.forEach(item => {
      if (item.isVoided) return;
      
      const effectiveQuantity = (item.originalQuantity || item.quantity) - (item.voidedQuantity || 0);
      if (effectiveQuantity <= 0) return;

      const itemSales = item.price * effectiveQuantity;
      const existing = itemSalesMap.get(item.menuItemId);
      
      if (existing) {
        existing.salesAmount += itemSales;
      } else {
        itemSalesMap.set(item.menuItemId, {
          name: item.name,
          category: item.category || 'N/A',
          salesAmount: itemSales
        });
      }
    });
  });

  // Find items without ANY cost method (hybrid costing support)
  const missingRecipeItems: RecipeCoverageResult['missingRecipeItems'] = [];
  
  for (const [itemId, itemData] of itemSalesMap) {
    const recipe = recipeMap.get(itemId);
    
    // Check if item has ANY valid cost method:
    // 1. Fixed cost method
    // 2. Recipe with ingredients
    // 3. Historical COGS (checked in analytics, not here)
    const hasFixedCost = recipe?.costingMethod === 'fixed' && recipe.fixedCost && recipe.fixedCost > 0;
    const hasRecipeWithIngredients = recipe && recipe.ingredients && recipe.ingredients.length > 0;
    const hasValidCostMethod = hasFixedCost || hasRecipeWithIngredients;
    
    if (!hasValidCostMethod) {
      missingRecipeItems.push({
        id: itemId,
        name: itemData.name,
        category: itemData.category,
        salesAmount: itemData.salesAmount,
        revenuePercentage: totalRevenue > 0 ? (itemData.salesAmount / totalRevenue) * 100 : 0
      });
    }
  }

  // Sort by revenue impact (highest first)
  missingRecipeItems.sort((a, b) => b.salesAmount - a.salesAmount);

  return {
    hasCompleteRecipes: missingRecipeItems.length === 0,
    missingRecipeItems,
    totalRevenue
  };
}