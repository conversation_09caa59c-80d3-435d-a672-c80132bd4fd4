// Minimal ignore-loader shim used in webpack aliases to stub out server-only modules
// when building static/web/electron exports.

// Export safe defaults for commonly aliased server modules.
const noop = () => {};

module.exports = {
  __esModule: true,
  // For libraries that are expected to export functions
  default: {},
  // Provide common named exports to avoid runtime undefined errors
  createClient: noop,
  connect: noop,
  MongoClient: function() { return { connect: noop, close: noop }; },
  // bcryptjs/ bcrypt placeholder
  genSaltSync: () => '',
  hashSync: (s) => s,
  compareSync: (a, b) => a === b,
};
