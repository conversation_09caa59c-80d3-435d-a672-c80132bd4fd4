/**
 * Format a number as Algerian Dinar currency with enhanced error handling
 * 
 * @param amount - The amount to format
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number): string {
  // 🔧 EDGE CASE FIX: Handle invalid inputs
  if (typeof amount !== 'number' || isNaN(amount) || !isFinite(amount)) {
    console.warn('[formatCurrency] Invalid amount provided:', amount);
    return 'DZD 0';
  }
  
  // 🔧 ROUNDING FIX: Ensure proper rounding to avoid floating point issues
  const roundedAmount = Math.round(amount * 100) / 100; // Round to 2 decimal places first
  const finalAmount = Math.round(roundedAmount); // Then round to whole number for DZD
  
  try {
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'DZD',
      maximumFractionDigits: 0, // No decimal places for DZD
      minimumFractionDigits: 0
    }).format(finalAmount);
  } catch (error) {
    console.error('[formatCurrency] Formatting error:', error);
    return `DZD ${finalAmount}`;
  }
}

/**
 * Format a currency amount with a + or - sign
 * 
 * @param amount - The amount to format
 * @returns Formatted currency string with sign
 */
export function formatCurrencyWithSign(amount: number): string {
  const formatted = formatCurrency(Math.abs(amount));
  return amount >= 0 ? `+${formatted}` : `-${formatted}`;
}

/**
 * Parse a currency string into a number with enhanced validation
 * 
 * @param currencyString - The currency string to parse
 * @returns Parsed number value
 */
export function parseCurrency(currencyString: string): number {
  if (typeof currencyString !== 'string') {
    console.warn('[parseCurrency] Invalid input type:', typeof currencyString);
    return 0;
  }
  
  // Remove currency symbol, commas and spaces, keep numbers, dots, and minus
  const cleanedString = currencyString.replace(/[^\d.-]/g, '');
  
  if (!cleanedString) {
    return 0;
  }
  
  const parsed = parseFloat(cleanedString);
  
  // 🔧 VALIDATION FIX: Check for valid number
  if (isNaN(parsed) || !isFinite(parsed)) {
    console.warn('[parseCurrency] Could not parse currency string:', currencyString);
    return 0;
  }
  
  // 🔧 ROUNDING FIX: Round to avoid floating point issues
  return Math.round(parsed * 100) / 100;
}

/**
 * Safely add two currency amounts to avoid floating point errors
 */
export function addCurrency(amount1: number, amount2: number): number {
  return Math.round((amount1 + amount2) * 100) / 100;
}

/**
 * Safely subtract two currency amounts to avoid floating point errors
 */
export function subtractCurrency(amount1: number, amount2: number): number {
  return Math.round((amount1 - amount2) * 100) / 100;
}

/**
 * Safely multiply currency amount to avoid floating point errors
 */
export function multiplyCurrency(amount: number, multiplier: number): number {
  return Math.round(amount * multiplier * 100) / 100;
} 