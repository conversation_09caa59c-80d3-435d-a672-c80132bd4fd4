/**
 * SSL/TLS validation utilities for secure sync connections
 */

interface SSLValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
}

/**
 * Validate that a URL uses HTTPS in production environments
 */
export function validateSecureUrl(url: string): SSLValidationResult {
  try {
    const parsedUrl = new URL(url);
    const isLocalhost = parsedUrl.hostname === 'localhost' || 
                       parsedUrl.hostname === '127.0.0.1' || 
                       parsedUrl.hostname.startsWith('192.168.') ||
                       parsedUrl.hostname.startsWith('10.') ||
                       parsedUrl.hostname.startsWith('172.');

    // Allow HTTP only for localhost/local networks
    if (parsedUrl.protocol === 'http:' && !isLocalhost) {
      return {
        isValid: false,
        error: 'HTTP connections are not allowed in production. Use HTTPS for security.'
      };
    }

    // Warn about self-signed certificates in development
    if (parsedUrl.protocol === 'https:' && process.env.NODE_ENV === 'development') {
      return {
        isValid: true,
        warnings: ['Development mode: Ensure SSL certificate is valid in production']
      };
    }

    return { isValid: true };
  } catch (error) {
    return {
      isValid: false,
      error: `Invalid URL format: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Test SSL connection to a server
 */
export async function testSSLConnection(url: string): Promise<SSLValidationResult> {
  try {
    const validation = validateSecureUrl(url);
    if (!validation.isValid) {
      return validation;
    }

    // Test connection with a simple HEAD request
    const response = await fetch(url, {
      method: 'HEAD',
      mode: 'cors',
      // Add timeout
      signal: AbortSignal.timeout(10000) // 10 second timeout
    });

    if (!response.ok) {
      return {
        isValid: false,
        error: `Server responded with status ${response.status}`
      };
    }

    // Check if response indicates secure connection
    const isSecure = response.url.startsWith('https://');
    
    return {
      isValid: true,
      warnings: isSecure ? undefined : ['Connection may not be fully secure']
    };

  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return {
          isValid: false,
          error: 'Connection timeout - server may be unreachable'
        };
      } else if (error.message.includes('CORS')) {
        return {
          isValid: false,
          error: 'CORS error - server may not allow cross-origin requests'
        };
      } else if (error.message.includes('Failed to fetch')) {
        return {
          isValid: false,
          error: 'Network error - check internet connection and server availability'
        };
      }
    }

    return {
      isValid: false,
      error: `SSL connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Validate bistro.icu server connection
 */
export async function validateBistroICUConnection(): Promise<SSLValidationResult> {
  const bistroUrl = 'https://bistro.icu';
  
  try {
    console.log('🔒 Testing SSL connection to bistro.icu...');
    
    const result = await testSSLConnection(`${bistroUrl}/api/health`);
    
    if (result.isValid) {
      console.log('✅ bistro.icu SSL connection validated');
    } else {
      console.error('❌ bistro.icu SSL validation failed:', result.error);
    }
    
    return result;
  } catch (error) {
    return {
      isValid: false,
      error: `Failed to validate bistro.icu: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Get SSL certificate info (web browser only)
 */
export function getSSLCertificateInfo(): { isSecure: boolean; protocol?: string } {
  if (typeof window === 'undefined') {
    return { isSecure: false };
  }

  const isSecure = window.location.protocol === 'https:';
  
  return {
    isSecure,
    protocol: window.location.protocol
  };
}

/**
 * Validate sync endpoint URLs for security
 */
export function validateSyncEndpoints(baseUrl: string): SSLValidationResult {
  const endpoints = [
    '/api/sync/discover-peers',
    '/api/sync/register-device',
    '/api/sync/proxy'
  ];

  const results: SSLValidationResult[] = [];

  for (const endpoint of endpoints) {
    const fullUrl = new URL(endpoint, baseUrl).toString();
    const validation = validateSecureUrl(fullUrl);
    results.push(validation);
  }

  const hasErrors = results.some(r => !r.isValid);
  const allWarnings = results.flatMap(r => r.warnings || []);
  const firstError = results.find(r => !r.isValid)?.error;

  return {
    isValid: !hasErrors,
    error: firstError,
    warnings: allWarnings.length > 0 ? allWarnings : undefined
  };
}

export type { SSLValidationResult };