export interface OrderItem {
  id: string;
  menuItemId: string;
  name: string;
  size?: string;
  price: number;
  quantity: number;
  addons: {
    id: string;
    name: string;
    price: number;
  }[];
  notes?: string;
  categoryId?: string;
  categoryName?: string;
}

export interface Order {
  id: string;
  type: 'table' | 'delivery' | 'takeout';
  status: 'pending' | 'preparing' | 'served' | 'completed';
  items: OrderItem[];
  total: number;
  createdAt: Date;
  tableId?: string;
  notes?: string;
  customer?: {
    name: string;
    phone: string;
    address?: string;
  };
  deliveryPerson?: {
    name: string;
    phone: string;
  };
}

export interface TableStatus {
  id: string;
  status: 'free' | 'occupied' | 'reserved';
  occupiedSince?: Date;
  currentOrderId?: string;
}

// Tab types for management components
export type TabType = 'pending' | 'active';