# Enhanced NSIS Uninstall Script for Bistro
# Fixes incomplete uninstall issues that prevent fresh installations

!macro customUnInstall
  # Stop any running CouchDB processes
  DetailPrint "Stopping CouchDB processes..."
  nsExec::ExecToLog 'taskkill /F /IM beam.smp.exe /T'
  nsExec::ExecToLog 'taskkill /F /IM epmd.exe /T'
  nsExec::ExecToLog 'taskkill /F /IM couchdb.exe /T'
  nsExec::ExecToLog 'taskkill /F /IM erl.exe /T'
  
  # Wait for processes to terminate
  Sleep 2000
  
  # Remove application data directories
  DetailPrint "Cleaning application data..."
  RMDir /r "$APPDATA\bistro"
  RMDir /r "$LOCALAPPDATA\bistro"
  RMDir /r "$APPDATA\Bistro"
  RMDir /r "$LOCALAPPDATA\Bistro"
  
  # Remove PouchDB/CouchDB data
  DetailPrint "Cleaning database files..."
  RMDir /r "$APPDATA\bistro\pouchdb-data"
  RMDir /r "$LOCALAPPDATA\bistro\pouchdb-data"
  RMDir /r "$APPDATA\Bistro\pouchdb-data"
  RMDir /r "$LOCALAPPDATA\Bistro\pouchdb-data"
  
  # Remove portable CouchDB copies
  DetailPrint "Cleaning portable CouchDB installations..."
  RMDir /r "$APPDATA\bistro\couchdb-portable"
  RMDir /r "$LOCALAPPDATA\bistro\couchdb-portable"
  RMDir /r "$APPDATA\Bistro\couchdb-portable"
  RMDir /r "$LOCALAPPDATA\Bistro\couchdb-portable"
  
  # Remove log files
  DetailPrint "Cleaning log files..."
  Delete "$APPDATA\bistro\*.log"
  Delete "$LOCALAPPDATA\bistro\*.log"
  Delete "$APPDATA\Bistro\*.log"
  Delete "$LOCALAPPDATA\Bistro\*.log"
  
  # Remove temporary files
  DetailPrint "Cleaning temporary files..."
  RMDir /r "$TEMP\bistro"
  RMDir /r "$TEMP\Bistro"
  RMDir /r "$TEMP\electron-*"
  
  # Clean registry entries
  DetailPrint "Cleaning registry entries..."
  DeleteRegKey HKCU "Software\bistro"
  DeleteRegKey HKCU "Software\Bistro"
  DeleteRegKey HKLM "Software\bistro"
  DeleteRegKey HKLM "Software\Bistro"
  
  # Remove from Windows startup (if added)
  DeleteRegValue HKCU "Software\Microsoft\Windows\CurrentVersion\Run" "Bistro"
  DeleteRegValue HKLM "Software\Microsoft\Windows\CurrentVersion\Run" "Bistro"
  
  # Clean Windows Firewall rules (if any were added)
  nsExec::ExecToLog 'netsh advfirewall firewall delete rule name="Bistro CouchDB"'
  nsExec::ExecToLog 'netsh advfirewall firewall delete rule name="Bistro App"'
  
  # Remove desktop shortcuts
  DetailPrint "Removing shortcuts..."
  Delete "$DESKTOP\Bistro.lnk"
  Delete "$SMPROGRAMS\Bistro.lnk"
  Delete "$SMPROGRAMS\Bistro\*.*"
  RMDir "$SMPROGRAMS\Bistro"
  
  # Force remove installation directory (in case some files are locked)
  DetailPrint "Force removing installation directory..."
  Sleep 1000
  RMDir /r "$INSTDIR"
  
  # If directory still exists, try harder
  IfFileExists "$INSTDIR" 0 +3
    nsExec::ExecToLog 'rd /s /q "$INSTDIR"'
    Sleep 1000
  
  # Final cleanup - remove any remaining traces
  DetailPrint "Final cleanup..."
  
  # Remove from Add/Remove Programs (redundant but ensures cleanup)
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}"
  DeleteRegKey HKCU "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}"
  
  # Clear Windows thumbnail cache (in case app icons are cached)
  nsExec::ExecToLog 'del /f /s /q "%LOCALAPPDATA%\Microsoft\Windows\Explorer\thumbcache_*.db"'
  
  DetailPrint "Uninstall completed successfully!"
!macroend

!macro customInstall
  # Pre-installation cleanup to ensure fresh install
  DetailPrint "Preparing for fresh installation..."
  
  # Stop any existing processes
  nsExec::ExecToLog 'taskkill /F /IM beam.smp.exe /T'
  nsExec::ExecToLog 'taskkill /F /IM epmd.exe /T'
  nsExec::ExecToLog 'taskkill /F /IM couchdb.exe /T'
  nsExec::ExecToLog 'taskkill /F /IM erl.exe /T'
  
  # Wait for processes to stop
  Sleep 2000
  
  # Clean any existing portable installations
  RMDir /r "$APPDATA\bistro\couchdb-portable"
  RMDir /r "$LOCALAPPDATA\bistro\couchdb-portable"
  RMDir /r "$APPDATA\Bistro\couchdb-portable"
  RMDir /r "$LOCALAPPDATA\Bistro\couchdb-portable"
  
  # Create application data directory
  CreateDirectory "$APPDATA\bistro"
  CreateDirectory "$LOCALAPPDATA\bistro"
  
  DetailPrint "Fresh installation preparation completed!"
!macroend

# Custom header for better uninstall detection
!macro customHeader
  # Add version info for better tracking
  VIAddVersionKey "FileDescription" "Bistro Restaurant POS System"
  VIAddVersionKey "ProductName" "Bistro"
  VIAddVersionKey "CompanyName" "Bistro Restaurant POS"
  VIAddVersionKey "LegalCopyright" "© 2024 Bistro Restaurant POS"
  VIAddVersionKey "FileVersion" "${VERSION}"
  VIAddVersionKey "ProductVersion" "${VERSION}"
!macroend

# Enhanced uninstaller detection
!macro preInit
  # Check if another instance is running
  System::Call 'kernel32::CreateMutex(i 0, i 0, t "BistroInstaller") i .r1 ?e'
  Pop $R0
  StrCmp $R0 0 +3
    MessageBox MB_OK|MB_ICONEXCLAMATION "Another instance of Bistro installer is already running."
    Abort
  
  # Check for existing installation and offer to uninstall
  ReadRegStr $R0 HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}" "UninstallString"
  StrCmp $R0 "" done
  
  MessageBox MB_OKCANCEL|MB_ICONEXCLAMATION \
    "Bistro is already installed. $\n$\nClick 'OK' to remove the previous version or 'Cancel' to cancel this upgrade." \
    IDOK uninst
  Abort
  
  uninst:
    ClearErrors
    ExecWait '$R0 /S _?=$INSTDIR'

    IfErrors no_remove_uninstaller done
    IfFileExists "$R0" 0 no_remove_uninstaller
      Delete "$R0"
      RMDir $INSTDIR

    no_remove_uninstaller:
  
  done:
!macroend
