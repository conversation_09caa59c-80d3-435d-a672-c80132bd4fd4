/**
 * Order Hold System Type Definitions
 * 
 * This file defines all types and interfaces for the Order Hold System,
 * which allows cashiers to temporarily park orders and resume them later.
 */

// Use any for OrderState to avoid circular dependencies - will be properly typed in implementation
export type OrderState = any;
export type OrderType = 'dine-in' | 'takeaway' | 'delivery';

export interface OrderItem {
  id: string;
  menuItemId: string;
  name: string;
  size?: string;
  price: number;
  quantity: number;
  addons: {
    id: string;
    name: string;
    price: number;
  }[];
  notes?: string;
  categoryId?: string;
  categoryName?: string;
}

// =====================================================
// CORE HOLD TYPES
// =====================================================

export type HoldPriority = 'normal' | 'high' | 'urgent';

export type HoldStatus = 'active' | 'expired' | 'resumed' | 'cleared';

// Compact representation of order state for hold storage
export interface HoldOrderState {
  items: OrderItem[];
  orderType: OrderType;
  tableId?: string;
  customer?: {
    name: string;
    phone: string;
    address?: string;
  };
  deliveryPerson?: {
    name: string;
    phone: string;
  };
  notes: string;
  total: number;
}

// UI state preservation for seamless resume
export interface HoldUiState {
  selectedCategory: string;
  selectedItemSizes: Record<string, string>;
  selectedSupplements: Record<string, string[]>; // Converted from Set to array for JSON
  itemNotes: Record<string, string>;
  selectedItemForSupplements?: string;
  lastAddedItem?: string;
}

// Context information for better hold management
export interface HoldContext {
  label?: string;                    // User-defined or auto-generated label
  priority: HoldPriority;           // Priority level for sorting and expiration
  customerWaiting: boolean;         // Indicates if customer is physically waiting
  estimatedItems: number;           // Rough estimate of final order size
  interruptionReason?: string;      // Optional reason for holding
  originalTable?: string;           // Table where hold was initiated
}

// =====================================================
// MAIN HOLD DOCUMENT SCHEMA
// =====================================================

export interface HoldDocument {
  _id: string;                      // Format: hold:{timestamp}_{uuid}
  _rev?: string;                    // PouchDB revision
  type: 'order_hold';
  schemaVersion: 'v4.0';
  
  // Hold metadata
  holdId: string;                   // Unique identifier for this hold
  label: string;                    // Display label (auto-generated or custom)
  createdAt: string;               // ISO timestamp
  createdBy: string;               // Staff member ID who created hold
  expiresAt?: string;              // Optional expiration time
  status: HoldStatus;              // Current status of the hold
  
  // Saved states
  orderState: HoldOrderState;      // Complete order state at time of hold
  uiState: HoldUiState;           // UI state for perfect restoration
  context: HoldContext;            // Additional context and metadata
  
  // Tracking fields
  resumedAt?: string;              // When hold was resumed (if applicable)
  resumedBy?: string;              // Who resumed the hold
  clearedAt?: string;              // When hold was cleared/deleted
  clearedBy?: string;              // Who cleared the hold
  totalHoldDuration?: number;      // Total time held in milliseconds
}

// =====================================================
// HOLD OPERATION INTERFACES
// =====================================================

export interface CreateHoldRequest {
  orderState: OrderState;          // Current order state from NewOrderingInterface
  uiState: any;                   // Current UI state (will be converted to HoldUiState)
  label?: string;                 // Optional custom label
  priority?: HoldPriority;        // Optional priority (defaults to 'normal')
  context?: Partial<HoldContext>; // Optional additional context
}

export interface ResumeHoldResponse {
  orderState: OrderState;         // Restored order state for NewOrderingInterface
  uiState: any;                  // Restored UI state (converted back to interface format)
  holdInfo: {
    label: string;
    heldFor: number;             // Duration in milliseconds
    priority: HoldPriority;
  };
}

export interface HoldValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

// =====================================================
// HOLD MANAGER SERVICE INTERFACE
// =====================================================

export interface HoldManagerService {
  // Primary operations
  holdCurrentOrder(request: CreateHoldRequest): Promise<string>;
  resumeOrder(holdId: string): Promise<ResumeHoldResponse>;
  clearHold(holdId: string): Promise<boolean>;
  
  // Queue management
  getAllHolds(): Promise<HoldDocument[]>;
  getActiveHolds(): Promise<HoldDocument[]>;
  getExpiredHolds(): Promise<HoldDocument[]>;
  
  // Maintenance
  clearExpiredHolds(): Promise<number>;
  validateHold(holdId: string): Promise<HoldValidationResult>;
  
  // Metadata operations
  updateHoldLabel(holdId: string, label: string): Promise<boolean>;
  updateHoldPriority(holdId: string, priority: HoldPriority): Promise<boolean>;
  updateHoldContext(holdId: string, context: Partial<HoldContext>): Promise<boolean>;
}

// =====================================================
// HOLD STORAGE SERVICE INTERFACE
// =====================================================

export interface HoldStorageService {
  save(holdDoc: HoldDocument): Promise<HoldDocument>;
  get(holdId: string): Promise<HoldDocument>;
  update(holdId: string, updates: Partial<HoldDocument>): Promise<HoldDocument>;
  delete(holdId: string): Promise<boolean>;
  query(options?: HoldQueryOptions): Promise<HoldDocument[]>;
  cleanup(): Promise<number>; // Returns number of cleaned up holds
}

export interface HoldQueryOptions {
  status?: HoldStatus | HoldStatus[];
  createdBy?: string;
  priority?: HoldPriority | HoldPriority[];
  createdAfter?: string;       // ISO date string
  createdBefore?: string;      // ISO date string
  limit?: number;
  sortBy?: 'createdAt' | 'priority' | 'expiresAt';
  sortOrder?: 'asc' | 'desc';
}

// =====================================================
// REACT HOOK INTERFACES
// =====================================================

export interface UseOrderHoldResult {
  // State
  heldOrders: HoldDocument[];
  activeHolds: HoldDocument[];
  isHolding: boolean;
  isResuming: boolean;
  error: string | null;
  
  // Operations
  holdCurrentOrder: (
    orderState: OrderState,
    uiState: any,
    options?: {
      label?: string;
      priority?: HoldPriority;
      context?: Partial<HoldContext>;
    }
  ) => Promise<string>;
  
  resumeOrder: (holdId: string) => Promise<ResumeHoldResponse>;
  clearHold: (holdId: string) => Promise<boolean>;
  
  // Queue management
  refreshHolds: () => Promise<void>;
  clearExpiredHolds: () => Promise<number>;
  
  // Metadata
  updateHoldLabel: (holdId: string, label: string) => Promise<boolean>;
  updateHoldPriority: (holdId: string, priority: HoldPriority) => Promise<boolean>;
}

// =====================================================
// COMPONENT PROP INTERFACES
// =====================================================

export interface HoldButtonProps {
  onHold: () => Promise<void>;
  disabled?: boolean;
  isHolding?: boolean;
  orderItemCount?: number;
  className?: string;
}

// =====================================================
// UTILITY TYPES
// =====================================================

export interface HoldStatistics {
  totalHolds: number;
  activeHolds: number;
  expiredHolds: number;
  averageHoldDuration: number;  // in milliseconds
  oldestActiveHold?: HoldDocument;
  priorityBreakdown: Record<HoldPriority, number>;
}

export interface HoldExpirationConfig {
  defaultExpiration: number;    // minutes
  maxHolds: number;            // per user/session
  cleanupInterval: number;     // seconds
  priorityExpirations: Record<HoldPriority, number>; // minutes
}

export interface AutoLabelConfig {
  useTableNames: boolean;
  useCustomerNames: boolean;
  useItemCounts: boolean;
  useOrderTypes: boolean;
  customFormat?: string;       // Template string for auto-labeling
}

// =====================================================
// ERROR TYPES
// =====================================================

export class HoldError extends Error {
  constructor(
    message: string,
    public code: string,
    public holdId?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'HoldError';
  }
}

export class HoldValidationError extends HoldError {
  constructor(message: string, holdId?: string, public validationErrors: string[] = []) {
    super(message, 'VALIDATION_ERROR', holdId, { validationErrors });
    this.name = 'HoldValidationError';
  }
}

export class HoldStorageError extends HoldError {
  constructor(message: string, holdId?: string, public storageDetails?: any) {
    super(message, 'STORAGE_ERROR', holdId, storageDetails);
    this.name = 'HoldStorageError';
  }
}

export class HoldNotFoundError extends HoldError {
  constructor(holdId: string) {
    super(`Hold not found: ${holdId}`, 'NOT_FOUND', holdId);
    this.name = 'HoldNotFoundError';
  }
}

export class HoldExpiredError extends HoldError {
  constructor(holdId: string, expiredAt: string) {
    super(`Hold expired: ${holdId}`, 'EXPIRED', holdId, { expiredAt });
    this.name = 'HoldExpiredError';
  }
}

export class HoldCorruptedError extends HoldError {
  constructor(holdId: string, corruptionDetails: any) {
    super(`Hold data corrupted: ${holdId}`, 'CORRUPTED', holdId, corruptionDetails);
    this.name = 'HoldCorruptedError';
  }
}