# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Exclude source maps to prevent source code leakage
*.js.map
*.ts.map
*.jsx.map
*.tsx.map
*.css.map
*.scss.map

# Electron dependencies, build, and data
/electron/node_modules/
/electron/dist/
/electron/app/
/electron/pouchdb-data/
/electron/release/
/electron/resources/
/electron/app/updates/

# Large binary files and installers
*.dmg
*.exe
*.msi
*.deb
*.rpm
*.pkg
*.app
*.zip
*.tar.gz
*.rar

# Large compiled binaries
acli

# CouchDB binaries and data (be more specific to avoid ignoring source files)
**/couchdb-*/bin/
**/couchdb-*/lib/
**/couchdb-*/share/
**/couchdb-*/*.beam
**/couch_*/bin/
**/couch_*/lib/
**/couch_*/share/
**/couch_*/*.beam
**/erts-*/bin/
**/erts-*/lib/

# Updates and releases
/public/updates/
/updates/
/releases/

# Capacitor
android/build/
android/.gradle/
ios/

# Android Studio
.idea/
.vscode/
.gradle/
.project
.settings/

# Added by Task Master AI
# Logs
logs
*.log
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/ 
# Android keystore configuration
android/keystore.properties
android/*.keystore
# Crush directory
.crush/
