<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Mobile Browser Internet Sync Test</title>
    <style>
        body { 
            font-family: monospace; 
            padding: 20px; 
            background: #1a1a1a; 
            color: #00ff00;
            max-width: 800px;
            margin: 0 auto;
        }
        .test { 
            background: #333; 
            padding: 10px; 
            margin: 10px 0; 
            border-left: 3px solid #00ff00;
        }
        .fail { border-left-color: #ff0000; color: #ff0000; }
        .pass { border-left-color: #00ff00; color: #00ff00; }
        .info { border-left-color: #0088ff; color: #0088ff; }
        button { 
            background: #00ff00; 
            color: #000; 
            border: none; 
            padding: 10px 20px; 
            margin: 10px 5px;
            cursor: pointer;
            font-family: monospace;
            font-weight: bold;
        }
        .log { 
            background: #000; 
            padding: 10px; 
            border: 1px solid #333;
            height: 300px;
            overflow-y: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🧪 Mobile Browser Internet Sync Test</h1>
    <p>This page tests internet sync from a mobile web browser to the restaurant leader.</p>
    
    <div class="test info">
        <strong>Environment Detection:</strong><br>
        Platform: <span id="platform">Detecting...</span><br>
        Is Mobile: <span id="isMobile">Detecting...</span><br>
        Is Capacitor: <span id="isCapacitor">Detecting...</span><br>
        Is Electron: <span id="isElectron">Detecting...</span><br>
        User Agent: <span id="userAgent">Detecting...</span>
    </div>

    <div class="test">
        <strong>Configuration:</strong><br>
        <label>VPS URL: <input type="text" id="vpsUrl" value="https://bistro.icu" style="width: 200px;"></label><br>
        <label>Auth Token: <input type="text" id="authToken" placeholder="Your JWT token" style="width: 300px;"></label><br>
        <label>Restaurant ID: <input type="text" id="restaurantId" value="test-restaurant-123" style="width: 200px;"></label><br>
    </div>

    <div class="test">
        <button onclick="runAllTests()">🚀 Run All Tests</button>
        <button onclick="clearLog()">🧹 Clear Log</button>
        <button onclick="testDiscovery()">🔍 Test Discovery Only</button>
        <button onclick="testProxy()">🔗 Test Proxy Only</button>
    </div>

    <div class="log" id="log"></div>

    <script>
        // Detect environment
        function detectEnvironment() {
            const userAgent = navigator.userAgent;
            const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            const isCapacitor = !!(window as any)?.Capacitor;
            const isElectron = !!(window as any)?.electronAPI || userAgent.includes('Electron');
            
            document.getElementById('platform').textContent = navigator.platform || 'unknown';
            document.getElementById('isMobile').textContent = isMobile ? '✅ Yes' : '❌ No';
            document.getElementById('isCapacitor').textContent = isCapacitor ? '✅ Yes' : '❌ No';
            document.getElementById('isElectron').textContent = isElectron ? '✅ Yes' : '❌ No';
            document.getElementById('userAgent').textContent = userAgent.substring(0, 100) + '...';

            return { userAgent, isMobile, isCapacitor, isElectron };
        }

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('log');
            const colorMap = {
                info: '#0088ff',
                success: '#00ff00', 
                error: '#ff0000',
                warn: '#ffaa00'
            };
            
            logDiv.innerHTML += `<div style="color: ${colorMap[type]};">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function testDiscovery() {
            const vpsUrl = document.getElementById('vpsUrl').value;
            const authToken = document.getElementById('authToken').value;

            if (!authToken) {
                log('❌ Please enter an auth token', 'error');
                return false;
            }

            try {
                log('🔍 Testing peer discovery...', 'info');
                
                const response = await fetch(`${vpsUrl}/api/sync/discover-peers`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    mode: 'cors' // Mobile browsers need CORS
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    log(`❌ Discovery failed: ${response.status} - ${errorText}`, 'error');
                    return false;
                }

                const data = await response.json();
                log(`📡 Discovery response: ${JSON.stringify(data, null, 2)}`, 'info');

                if (data.leader) {
                    log(`✅ Found leader: ${data.leader.id} at ${data.leader.ipAddress}:${data.leader.couchdbPort}`, 'success');
                    return data.leader;
                } else if (data.error) {
                    log(`⚠️ No leader available: ${data.message}`, 'warn');
                    return false;
                } else {
                    log(`❌ Unexpected response format`, 'error');
                    return false;
                }

            } catch (error) {
                log(`❌ Discovery error: ${error.message}`, 'error');
                return false;
            }
        }

        async function testProxy() {
            const leader = await testDiscovery();
            if (!leader) {
                log('❌ Cannot test proxy without leader', 'error');
                return false;
            }

            const vpsUrl = document.getElementById('vpsUrl').value;
            const authToken = document.getElementById('authToken').value;
            const restaurantId = document.getElementById('restaurantId').value;
            
            try {
                log('🔗 Testing proxy connection...', 'info');
                
                const dbName = `resto-${restaurantId}`;
                const proxyUrl = `${vpsUrl}/api/sync/proxy/${leader.id}/${dbName}`;
                
                log(`📡 Connecting to: ${proxyUrl}`, 'info');

                const response = await fetch(proxyUrl, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Accept': 'application/json'
                    },
                    mode: 'cors'
                });

                if (response.ok) {
                    const dbInfo = await response.json();
                    log(`✅ Proxy connection successful!`, 'success');
                    log(`📊 Database info: ${JSON.stringify(dbInfo, null, 2)}`, 'info');
                    return true;
                } else {
                    const errorText = await response.text();
                    log(`❌ Proxy failed: ${response.status} - ${errorText}`, 'error');
                    return false;
                }

            } catch (error) {
                log(`❌ Proxy error: ${error.message}`, 'error');
                return false;
            }
        }

        async function testNetworkConstraints() {
            log('🌐 Testing mobile browser network constraints...', 'info');
            
            // Test CORS
            try {
                const testResponse = await fetch('https://httpbin.org/headers', { mode: 'cors' });
                log('✅ CORS requests work', 'success');
            } catch (error) {
                log('❌ CORS requests blocked', 'error');
            }

            // Test HTTPS enforcement
            try {
                const vpsUrl = document.getElementById('vpsUrl').value;
                if (vpsUrl.startsWith('https://')) {
                    log('✅ HTTPS URL configured', 'success');
                } else {
                    log('⚠️ Non-HTTPS URL - may be blocked', 'warn');
                }
            } catch (error) {
                log('❌ URL validation failed', 'error');
            }

            return true;
        }

        async function runAllTests() {
            clearLog();
            log('🧪 Starting Mobile Browser Internet Sync Tests', 'info');
            
            const env = detectEnvironment();
            log(`🔍 Environment: ${env.isMobile ? 'Mobile' : 'Desktop'} ${env.isCapacitor ? 'Capacitor' : 'Browser'}`, 'info');
            
            let allPassed = true;

            // Test 1: Network constraints
            const networkTest = await testNetworkConstraints();
            if (!networkTest) allPassed = false;

            // Test 2: Discovery
            const discoveryTest = await testDiscovery();
            if (!discoveryTest) allPassed = false;

            // Test 3: Proxy (only if discovery worked)
            if (discoveryTest) {
                const proxyTest = await testProxy();
                if (!proxyTest) allPassed = false;
            }

            // Summary
            if (allPassed && discoveryTest) {
                log('🎉 ALL TESTS PASSED! Mobile browser can sync with leader.', 'success');
            } else {
                log('⚠️ Some tests failed. Check configuration and server status.', 'warn');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            detectEnvironment();
            log('📱 Mobile Browser Sync Tester loaded', 'info');
            log('ℹ️ Enter your auth token and click "Run All Tests"', 'info');
        });
    </script>
</body>
</html>