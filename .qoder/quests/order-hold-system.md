# Order Hold System Design

## Overview

The Order Hold System addresses a critical UX flaw in the current POS interface: the inability to handle order interruptions. Currently, the NewOrderingInterface operates as a "single-threaded" tool that can only handle one order at a time, forcing cashiers to abandon complex orders when interruptions occur. This design implements an "Order Parking" system that allows cashiers to temporarily save order states and seamlessly resume complex orders later.

## Architecture

### System Components

```mermaid
graph TB
    A[Order Interface] --> B[Hold Manager]
    B --> C[Hold Storage]
    B --> D[Hold Queue UI]
    A --> E[Active Order State]
    B --> F[Hold Metadata]
    
    C --> G[PouchDB Hold Collection]
    F --> H[Timestamps & Labels]
    D --> I[Quick Resume Actions]
    
    subgraph "Hold Operations"
        J[Hold Order] --> K[Store State]
        L[Resume Order] --> M[Restore State]
        N[Clear Hold] --> O[Delete State]
    end
```

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant C as Cashier
    participant UI as Order Interface
    participant HM as Hold Manager
    participant HS as Hold Storage
    participant QUI as Hold Queue UI
    
    C->>UI: Building complex order
    C->>UI: Clicks "Hold Order"
    UI->>HM: saveCurrentOrder()
    HM->>HS: Store order state + metadata
    HS-->>HM: Confirm save
    HM->>QUI: Update hold queue
    HM->>UI: Clear interface
    
    Note over C: Handle interruption
    
    C->>QUI: Click held order
    QUI->>HM: resumeOrder(holdId)
    HM->>HS: Retrieve order state
    HS-->>HM: Return order data
    HM->>UI: Restore complete state
    UI-->>C: Continue complex order
```

## Hold Management System

### Core Hold Document Schema

```typescript
interface HoldDocument {
  _id: string;                    // Format: hold:{timestamp}_{uuid}
  type: 'order_hold';
  schemaVersion: 'v4.0';
  
  // Hold metadata
  holdId: string;                 // Unique identifier
  label: string;                  // User-defined label or auto-generated
  createdAt: string;             // ISO timestamp
  createdBy: string;             // Staff member ID
  expiresAt?: string;            // Optional expiration
  
  // Saved order state
  orderState: {
    items: OrderItem[];
    orderType: OrderType;
    tableId?: string;
    customer?: CustomerInfo;
    deliveryPerson?: DeliveryPerson;
    notes: string;
    total: number;
  };
  
  // UI state preservation
  uiState: {
    selectedCategory: string;
    selectedItemSizes: Record<string, string>;
    selectedSupplements: Record<string, string[]>;
    itemNotes: Record<string, string>;
    selectedItemForSupplements?: string;
  };
  
  // Context information
  context: {
    tableStatus?: string;
    customerWaiting: boolean;
    priority: 'normal' | 'high' | 'urgent';
    estimatedItems: number;
  };
}
```

### Hold Manager Service

```typescript
interface HoldManagerService {
  // Primary operations
  holdCurrentOrder(label?: string, priority?: HoldPriority): Promise<string>;
  resumeOrder(holdId: string): Promise<boolean>;
  clearHold(holdId: string): Promise<boolean>;
  
  // Queue management
  getAllHolds(): Promise<HoldDocument[]>;
  getActiveHolds(): Promise<HoldDocument[]>;
  clearExpiredHolds(): Promise<void>;
  
  // Metadata operations
  updateHoldLabel(holdId: string, label: string): Promise<boolean>;
  updateHoldPriority(holdId: string, priority: HoldPriority): Promise<boolean>;
}
```

## User Interface Design

### Hold Button Integration

The hold functionality integrates directly into the NewOrderingInterface with minimal visual disruption:

#### Primary Hold Button
- **Location**: Right panel, above order summary
- **Appearance**: Orange accent button with pause icon
- **States**: 
  - Normal: "Hold Order" (visible when items exist)
  - Loading: "Saving..." with spinner
  - Success: Brief confirmation animation

#### Hold Queue Panel

```mermaid
graph LR
    A[Hold Queue Toggle] --> B[Compact Hold List]
    B --> C[Hold Card 1]
    B --> D[Hold Card 2]
    B --> E[Hold Card N]
    
    C --> F[Quick Info]
    C --> G[Resume Button]
    C --> H[Clear Button]
    
    F --> I[Label/Table]
    F --> J[Items Count]
    F --> K[Time Ago]
    F --> L[Priority Badge]
```

#### Hold Card Design

Each held order displays as a compact card:

```
┌─────────────────────────────┐
│ 🔶 Table 5 - Large Order    │
│ 📦 8 items • 2m ago         │
│ [Resume] [Edit] [×]         │
└─────────────────────────────┘
```

### Visual States and Feedback

#### Hold Creation Flow
1. **Pre-hold validation**: Ensure order has items
2. **Quick label prompt**: Optional 3-second overlay for custom label
3. **Smooth state save**: Loading indicator with progress
4. **Confirmation**: Brief success toast
5. **Interface reset**: Clean slate for new order

#### Resume Flow
1. **Queue visibility**: Toggle or persistent sidebar
2. **One-click resume**: Direct resume from hold card
3. **State restoration**: Loading indicator while restoring
4. **Seamless continuation**: Full context restoration

## Technical Implementation

### Hold Storage Operations

```typescript
// Hold creation
async function createHold(
  orderState: OrderState,
  uiState: UiState,
  context: HoldContext
): Promise<string> {
  const holdId = `hold_${Date.now()}_${generateUUID()}`;
  
  const holdDoc: HoldDocument = {
    _id: `hold:${holdId}`,
    type: 'order_hold',
    schemaVersion: 'v4.0',
    holdId,
    label: context.label || generateAutoLabel(orderState),
    createdAt: new Date().toISOString(),
    createdBy: getCurrentUser().id,
    orderState: sanitizeOrderState(orderState),
    uiState: serializeUiState(uiState),
    context
  };
  
  await holdStorage.save(holdDoc);
  return holdId;
}

// State restoration
async function restoreHold(holdId: string): Promise<{
  orderState: OrderState;
  uiState: UiState;
}> {
  const holdDoc = await holdStorage.get(`hold:${holdId}`);
  
  return {
    orderState: deserializeOrderState(holdDoc.orderState),
    uiState: deserializeUiState(holdDoc.uiState)
  };
}
```

### Integration with NewOrderingInterface

#### Hook Integration

```typescript
// Custom hook for hold functionality
const useOrderHold = () => {
  const [heldOrders, setHeldOrders] = useState<HoldDocument[]>([]);
  const [isHolding, setIsHolding] = useState(false);
  
  const holdCurrentOrder = useCallback(async (
    orderState: OrderState,
    uiState: UiState,
    label?: string
  ) => {
    setIsHolding(true);
    try {
      const holdId = await holdManager.holdCurrentOrder(
        orderState,
        uiState,
        { label, priority: 'normal' }
      );
      
      // Refresh hold queue
      await refreshHeldOrders();
      return holdId;
    } finally {
      setIsHolding(false);
    }
  }, []);
  
  const resumeOrder = useCallback(async (holdId: string) => {
    const { orderState, uiState } = await holdManager.resumeOrder(holdId);
    return { orderState, uiState };
  }, []);
  
  return {
    heldOrders,
    isHolding,
    holdCurrentOrder,
    resumeOrder,
    clearHold: holdManager.clearHold
  };
};
```

#### Reducer Integration

```typescript
// Extend existing orderReducer
const extendedOrderActions = {
  ...existingActions,
  'HOLD_ORDER': (state: OrderState) => ({ ...initialOrderState }),
  'RESUME_ORDER': (state: OrderState, action: { payload: OrderState }) => action.payload,
} as const;
```

### Performance Considerations

#### Storage Optimization
- **Lazy loading**: Load hold queue only when accessed
- **Background cleanup**: Automatic expiration of old holds
- **State compression**: Minimize stored UI state size
- **Memory management**: Clear unused hold data

#### User Experience
- **Fast hold creation**: < 500ms from click to completion
- **Instant resume**: < 200ms state restoration
- **Smooth animations**: Non-blocking UI transitions
- **Error recovery**: Graceful handling of corrupted holds

## User Experience Flows

### Primary Use Case: Complex Order Interruption

```mermaid
graph TD
    A[Cashier building large order] --> B[Customer asks quick question]
    B --> C[Click Hold Order button]
    C --> D[Optional: Add custom label]
    D --> E[Order saved & interface cleared]
    E --> F[Handle interruption/simple order]
    F --> G[Click held order in queue]
    G --> H[Full state restored instantly]
    H --> I[Continue complex order seamlessly]
```

### Secondary Use Case: Table Management

```mermaid
graph TD
    A[Start order for Table 5] --> B[Table 5 customer steps away]
    B --> C[Hold order with Table 5 label]
    C --> D[Serve other customers]
    D --> E[Table 5 customer returns]
    E --> F[Resume Table 5 order]
    F --> G[Complete original order]
```

### Tertiary Use Case: Staff Handoff

```mermaid
graph TD
    A[Staff A starts complex order] --> B[Shift change approaching]
    B --> C[Hold order with detailed label]
    C --> D[Staff B takes over]
    D --> E[Staff B sees held order]
    E --> F[Resume and complete order]
```

## Business Logic

### Auto-Labeling System

```typescript
function generateAutoLabel(orderState: OrderState): string {
  const { items, tableId, orderType, customer } = orderState;
  
  // Priority 1: Table identification
  if (tableId) {
    return `Table ${tableId}`;
  }
  
  // Priority 2: Customer identification
  if (customer?.name) {
    return customer.name;
  }
  
  // Priority 3: Order characteristics
  if (items.length > 5) {
    return `Large Order (${items.length} items)`;
  }
  
  // Priority 4: Order type + time
  const timeLabel = format(new Date(), 'HH:mm');
  return `${orderType} • ${timeLabel}`;
}
```

### Hold Expiration Policy

```typescript
interface ExpirationRules {
  defaultExpiration: 60; // minutes
  maxHolds: 10;         // per cashier
  cleanupInterval: 300; // seconds
  
  priorityExpirations: {
    normal: 60;  // minutes
    high: 120;   // minutes
    urgent: 180; // minutes
  };
}
```

### Hold Priority System

```typescript
type HoldPriority = 'normal' | 'high' | 'urgent';

const priorityRules = {
  normal: {
    color: 'blue',
    expiration: 60,
    sortOrder: 3
  },
  high: {
    color: 'orange', 
    expiration: 120,
    sortOrder: 2
  },
  urgent: {
    color: 'red',
    expiration: 180,
    sortOrder: 1
  }
};
```

## Error Handling & Edge Cases

### Data Consistency

```typescript
// Hold validation before save
async function validateHoldState(
  orderState: OrderState,
  uiState: UiState
): Promise<ValidationResult> {
  const errors: string[] = [];
  
  // Validate order state
  if (!orderState.items || orderState.items.length === 0) {
    errors.push('Cannot hold empty order');
  }
  
  // Validate UI state integrity
  if (uiState.selectedItemForSupplements) {
    const itemExists = orderState.items.find(
      item => item.id === uiState.selectedItemForSupplements
    );
    if (!itemExists) {
      errors.push('UI state references non-existent item');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
```

### Recovery Mechanisms

```typescript
// Corrupted hold recovery
async function recoverCorruptedHold(holdId: string): Promise<boolean> {
  try {
    const holdDoc = await holdStorage.get(`hold:${holdId}`);
    
    // Attempt to sanitize and repair
    const repairedState = sanitizeOrderState(holdDoc.orderState);
    const repairedUiState = sanitizeUiState(holdDoc.uiState);
    
    // Update hold with repaired data
    await holdStorage.update(holdId, {
      orderState: repairedState,
      uiState: repairedUiState
    });
    
    return true;
  } catch (error) {
    // If repair fails, archive and remove
    await archiveCorruptedHold(holdId);
    await holdStorage.delete(`hold:${holdId}`);
    return false;
  }
}
```

### Conflict Resolution

```typescript
// Handle concurrent modifications
async function handleHoldConflict(
  holdId: string,
  currentState: OrderState
): Promise<ConflictResolution> {
  const storedHold = await holdStorage.get(`hold:${holdId}`);
  
  // Compare timestamps and modifications
  const conflict = detectStateConflict(currentState, storedHold.orderState);
  
  if (conflict.hasConflict) {
    return {
      strategy: 'user_choice',
      options: {
        keepCurrent: currentState,
        keepStored: storedHold.orderState,
        merge: mergeOrderStates(currentState, storedHold.orderState)
      }
    };
  }
  
  return { strategy: 'auto_merge', result: currentState };
}
```

## Testing Strategy

### Unit Testing

```typescript
describe('OrderHoldSystem', () => {
  describe('Hold Creation', () => {
    it('should create hold with valid order state', async () => {
      const mockOrderState = createMockOrderState();
      const mockUiState = createMockUiState();
      
      const holdId = await holdManager.holdCurrentOrder(
        mockOrderState,
        mockUiState
      );
      
      expect(holdId).toBeDefined();
      expect(holdId).toMatch(/^hold_\d+_[a-f0-9-]{36}$/);
    });
    
    it('should reject empty orders', async () => {
      const emptyState = { ...initialOrderState, items: [] };
      
      await expect(holdManager.holdCurrentOrder(emptyState, {}))
        .rejects.toThrow('Cannot hold empty order');
    });
  });
  
  describe('State Restoration', () => {
    it('should restore complete order state', async () => {
      const originalState = createComplexOrderState();
      const originalUiState = createComplexUiState();
      
      const holdId = await holdManager.holdCurrentOrder(
        originalState,
        originalUiState
      );
      
      const restored = await holdManager.resumeOrder(holdId);
      
      expect(restored.orderState).toEqual(originalState);
      expect(restored.uiState).toEqual(originalUiState);
    });
  });
});
```

### Integration Testing

```typescript
describe('Hold System Integration', () => {
  it('should integrate seamlessly with NewOrderingInterface', async () => {
    // Simulate complex order building
    const { orderState, uiState } = await buildComplexOrder();
    
    // Hold the order
    const holdId = await holdManager.hold(orderState, uiState);
    
    // Verify interface reset
    expect(getCurrentOrderState()).toEqual(initialOrderState);
    
    // Resume order
    await holdManager.resume(holdId);
    
    // Verify complete restoration
    expect(getCurrentOrderState()).toEqual(orderState);
    expect(getCurrentUiState()).toEqual(uiState);
  });
});
```

### User Acceptance Testing

```typescript
describe('User Experience Scenarios', () => {
  it('should handle interruption scenario smoothly', async () => {
    // Complex order in progress
    await simulateComplexOrderBuilding();
    
    // Interruption occurs
    const holdId = await simulateOrderHold();
    
    // Handle interruption
    await simulateSimpleOrder();
    
    // Resume complex order
    await simulateOrderResume(holdId);
    
    // Verify seamless continuation
    expect(getOrderProgress()).toBe('can_continue_seamlessly');
  });
});
```
## Architecture

### System Components

```mermaid
graph TB
    A[Order Interface] --> B[Hold Manager]
    B --> C[Hold Storage]
    B --> D[Hold Queue UI]
    A --> E[Active Order State]
    B --> F[Hold Metadata]
    
    C --> G[PouchDB Hold Collection]
    F --> H[Timestamps & Labels]
    D --> I[Quick Resume Actions]
    
    subgraph "Hold Operations"
        J[Hold Order] --> K[Store State]
        L[Resume Order] --> M[Restore State]
        N[Clear Hold] --> O[Delete State]
    end
```

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant C as Cashier
    participant UI as Order Interface
    participant HM as Hold Manager
    participant HS as Hold Storage
    participant QUI as Hold Queue UI
    
    C->>UI: Building complex order
    C->>UI: Clicks "Hold Order"
    UI->>HM: saveCurrentOrder()
    HM->>HS: Store order state + metadata
    HS-->>HM: Confirm save
    HM->>QUI: Update hold queue
    HM->>UI: Clear interface
    
    Note over C: Handle interruption
    
    C->>QUI: Click held order
    QUI->>HM: resumeOrder(holdId)
    HM->>HS: Retrieve order state
    HS-->>HM: Return order data
    HM->>UI: Restore complete state
    UI-->>C: Continue complex order
```

## Hold Management System

### Core Hold Document Schema

```typescript
interface HoldDocument {
  _id: string;                    // Format: hold:{timestamp}_{uuid}
  type: 'order_hold';
  schemaVersion: 'v4.0';
  
  // Hold metadata
  holdId: string;                 // Unique identifier
  label: string;                  // User-defined label or auto-generated
  createdAt: string;             // ISO timestamp
  createdBy: string;             // Staff member ID
  expiresAt?: string;            // Optional expiration
  
  // Saved order state
  orderState: {
    items: OrderItem[];
    orderType: OrderType;
    tableId?: string;
    customer?: CustomerInfo;
    deliveryPerson?: DeliveryPerson;
    notes: string;
    total: number;
  };
  
  // UI state preservation
  uiState: {
    selectedCategory: string;
    selectedItemSizes: Record<string, string>;
    selectedSupplements: Record<string, string[]>;
    itemNotes: Record<string, string>;
    selectedItemForSupplements?: string;
  };
  
  // Context information
  context: {
    tableStatus?: string;
    customerWaiting: boolean;
    priority: 'normal' | 'high' | 'urgent';
    estimatedItems: number;
  };
}
```

### Hold Manager Service

```typescript
interface HoldManagerService {
  // Primary operations
  holdCurrentOrder(label?: string, priority?: HoldPriority): Promise<string>;
  resumeOrder(holdId: string): Promise<boolean>;
  clearHold(holdId: string): Promise<boolean>;
  
  // Queue management
  getAllHolds(): Promise<HoldDocument[]>;
  getActiveHolds(): Promise<HoldDocument[]>;
  clearExpiredHolds(): Promise<void>;
  
  // Metadata operations
  updateHoldLabel(holdId: string, label: string): Promise<boolean>;
  updateHoldPriority(holdId: string, priority: HoldPriority): Promise<boolean>;
}
```

## User Interface Design

### Hold Button Integration

The hold functionality integrates directly into the NewOrderingInterface with minimal visual disruption:

#### Primary Hold Button
- **Location**: Right panel, above order summary
- **Appearance**: Orange accent button with pause icon
- **States**: 
  - Normal: "Hold Order" (visible when items exist)
  - Loading: "Saving..." with spinner
  - Success: Brief confirmation animation

#### Hold Queue Panel

```mermaid
graph LR
    A[Hold Queue Toggle] --> B[Compact Hold List]
    B --> C[Hold Card 1]
    B --> D[Hold Card 2]
    B --> E[Hold Card N]
    
    C --> F[Quick Info]
    C --> G[Resume Button]
    C --> H[Clear Button]
    
    F --> I[Label/Table]
    F --> J[Items Count]
    F --> K[Time Ago]
    F --> L[Priority Badge]
```

#### Hold Card Design

Each held order displays as a compact card:

```
┌─────────────────────────────┐
│ 🔶 Table 5 - Large Order    │
│ 📦 8 items • 2m ago         │
│ [Resume] [Edit] [×]         │
└─────────────────────────────┘
```

### Visual States and Feedback

#### Hold Creation Flow
1. **Pre-hold validation**: Ensure order has items
2. **Quick label prompt**: Optional 3-second overlay for custom label
3. **Smooth state save**: Loading indicator with progress
4. **Confirmation**: Brief success toast
5. **Interface reset**: Clean slate for new order

#### Resume Flow
1. **Queue visibility**: Toggle or persistent sidebar
2. **One-click resume**: Direct resume from hold card
3. **State restoration**: Loading indicator while restoring
4. **Seamless continuation**: Full context restoration

## Technical Implementation

### Hold Storage Operations

```typescript
// Hold creation
async function createHold(
  orderState: OrderState,
  uiState: UiState,
  context: HoldContext
): Promise<string> {
  const holdId = `hold_${Date.now()}_${generateUUID()}`;
  
  const holdDoc: HoldDocument = {
    _id: `hold:${holdId}`,
    type: 'order_hold',
    schemaVersion: 'v4.0',
    holdId,
    label: context.label || generateAutoLabel(orderState),
    createdAt: new Date().toISOString(),
    createdBy: getCurrentUser().id,
    orderState: sanitizeOrderState(orderState),
    uiState: serializeUiState(uiState),
    context
  };
  
  await holdStorage.save(holdDoc);
  return holdId;
}

// State restoration
async function restoreHold(holdId: string): Promise<{
  orderState: OrderState;
  uiState: UiState;
}> {
  const holdDoc = await holdStorage.get(`hold:${holdId}`);
  
  return {
    orderState: deserializeOrderState(holdDoc.orderState),
    uiState: deserializeUiState(holdDoc.uiState)
  };
}
```

### Integration with NewOrderingInterface

#### Hook Integration

```typescript
// Custom hook for hold functionality
const useOrderHold = () => {
  const [heldOrders, setHeldOrders] = useState<HoldDocument[]>([]);
  const [isHolding, setIsHolding] = useState(false);
  
  const holdCurrentOrder = useCallback(async (
    orderState: OrderState,
    uiState: UiState,
    label?: string
  ) => {
    setIsHolding(true);
    try {
      const holdId = await holdManager.holdCurrentOrder(
        orderState,
        uiState,
        { label, priority: 'normal' }
      );
      
      // Refresh hold queue
      await refreshHeldOrders();
      return holdId;
    } finally {
      setIsHolding(false);
    }
  }, []);
  
  const resumeOrder = useCallback(async (holdId: string) => {
    const { orderState, uiState } = await holdManager.resumeOrder(holdId);
    return { orderState, uiState };
  }, []);
  
  return {
    heldOrders,
    isHolding,
    holdCurrentOrder,
    resumeOrder,
    clearHold: holdManager.clearHold
  };
};
```

#### Reducer Integration

```typescript
// Extend existing orderReducer
const extendedOrderActions = {
  ...existingActions,
  'HOLD_ORDER': (state: OrderState) => ({ ...initialOrderState }),
  'RESUME_ORDER': (state: OrderState, action: { payload: OrderState }) => action.payload,
} as const;
```

### Performance Considerations

#### Storage Optimization
- **Lazy loading**: Load hold queue only when accessed
- **Background cleanup**: Automatic expiration of old holds
- **State compression**: Minimize stored UI state size
- **Memory management**: Clear unused hold data

#### User Experience
- **Fast hold creation**: < 500ms from click to completion
- **Instant resume**: < 200ms state restoration
- **Smooth animations**: Non-blocking UI transitions
- **Error recovery**: Graceful handling of corrupted holds

## User Experience Flows

### Primary Use Case: Complex Order Interruption

```mermaid
graph TD
    A[Cashier building large order] --> B[Customer asks quick question]
    B --> C[Click Hold Order button]
    C --> D[Optional: Add custom label]
    D --> E[Order saved & interface cleared]
    E --> F[Handle interruption/simple order]
    F --> G[Click held order in queue]
    G --> H[Full state restored instantly]
    H --> I[Continue complex order seamlessly]
```

### Secondary Use Case: Table Management

```mermaid
graph TD
    A[Start order for Table 5] --> B[Table 5 customer steps away]
    B --> C[Hold order with Table 5 label]
    C --> D[Serve other customers]
    D --> E[Table 5 customer returns]
    E --> F[Resume Table 5 order]
    F --> G[Complete original order]
```

### Tertiary Use Case: Staff Handoff

```mermaid
graph TD
    A[Staff A starts complex order] --> B[Shift change approaching]
    B --> C[Hold order with detailed label]
    C --> D[Staff B takes over]
    D --> E[Staff B sees held order]
    E --> F[Resume and complete order]
```

## Business Logic

### Auto-Labeling System

```typescript
function generateAutoLabel(orderState: OrderState): string {
  const { items, tableId, orderType, customer } = orderState;
  
  // Priority 1: Table identification
  if (tableId) {
    return `Table ${tableId}`;
  }
  
  // Priority 2: Customer identification
  if (customer?.name) {
    return customer.name;
  }
  
  // Priority 3: Order characteristics
  if (items.length > 5) {
    return `Large Order (${items.length} items)`;
  }
  
  // Priority 4: Order type + time
  const timeLabel = format(new Date(), 'HH:mm');
  return `${orderType} • ${timeLabel}`;
}
```

### Hold Expiration Policy

```typescript
interface ExpirationRules {
  defaultExpiration: 60; // minutes
  maxHolds: 10;         // per cashier
  cleanupInterval: 300; // seconds
  
  priorityExpirations: {
    normal: 60;  // minutes
    high: 120;   // minutes
    urgent: 180; // minutes
  };
}
```

### Hold Priority System

```typescript
type HoldPriority = 'normal' | 'high' | 'urgent';

const priorityRules = {
  normal: {
    color: 'blue',
    expiration: 60,
    sortOrder: 3
  },
  high: {
    color: 'orange', 
    expiration: 120,
    sortOrder: 2
  },
  urgent: {
    color: 'red',
    expiration: 180,
    sortOrder: 1
  }
};
```

## Error Handling & Edge Cases

### Data Consistency

```typescript
// Hold validation before save
async function validateHoldState(
  orderState: OrderState,
  uiState: UiState
): Promise<ValidationResult> {
  const errors: string[] = [];
  
  // Validate order state
  if (!orderState.items || orderState.items.length === 0) {
    errors.push('Cannot hold empty order');
  }
  
  // Validate UI state integrity
  if (uiState.selectedItemForSupplements) {
    const itemExists = orderState.items.find(
      item => item.id === uiState.selectedItemForSupplements
    );
    if (!itemExists) {
      errors.push('UI state references non-existent item');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
```

### Recovery Mechanisms

```typescript
// Corrupted hold recovery
async function recoverCorruptedHold(holdId: string): Promise<boolean> {
  try {
    const holdDoc = await holdStorage.get(`hold:${holdId}`);
    
    // Attempt to sanitize and repair
    const repairedState = sanitizeOrderState(holdDoc.orderState);
    const repairedUiState = sanitizeUiState(holdDoc.uiState);
    
    // Update hold with repaired data
    await holdStorage.update(holdId, {
      orderState: repairedState,
      uiState: repairedUiState
    });
    
    return true;
  } catch (error) {
    // If repair fails, archive and remove
    await archiveCorruptedHold(holdId);
    await holdStorage.delete(`hold:${holdId}`);
    return false;
  }
}
```

### Conflict Resolution

```typescript
// Handle concurrent modifications
async function handleHoldConflict(
  holdId: string,
  currentState: OrderState
): Promise<ConflictResolution> {
  const storedHold = await holdStorage.get(`hold:${holdId}`);
  
  // Compare timestamps and modifications
  const conflict = detectStateConflict(currentState, storedHold.orderState);
  
  if (conflict.hasConflict) {
    return {
      strategy: 'user_choice',
      options: {
        keepCurrent: currentState,
        keepStored: storedHold.orderState,
        merge: mergeOrderStates(currentState, storedHold.orderState)
      }
    };
  }
  
  return { strategy: 'auto_merge', result: currentState };
}
```

## Testing Strategy

### Unit Testing

```typescript
describe('OrderHoldSystem', () => {
  describe('Hold Creation', () => {
    it('should create hold with valid order state', async () => {
      const mockOrderState = createMockOrderState();
      const mockUiState = createMockUiState();
      
      const holdId = await holdManager.holdCurrentOrder(
        mockOrderState,
        mockUiState
      );
      
      expect(holdId).toBeDefined();
      expect(holdId).toMatch(/^hold_\d+_[a-f0-9-]{36}$/);
    });
    
    it('should reject empty orders', async () => {
      const emptyState = { ...initialOrderState, items: [] };
      
      await expect(holdManager.holdCurrentOrder(emptyState, {}))
        .rejects.toThrow('Cannot hold empty order');
    });
  });
  
  describe('State Restoration', () => {
    it('should restore complete order state', async () => {
      const originalState = createComplexOrderState();
      const originalUiState = createComplexUiState();
      
      const holdId = await holdManager.holdCurrentOrder(
        originalState,
        originalUiState
      );
      
      const restored = await holdManager.resumeOrder(holdId);
      
      expect(restored.orderState).toEqual(originalState);
      expect(restored.uiState).toEqual(originalUiState);
    });
  });
});
```

### Integration Testing

```typescript
describe('Hold System Integration', () => {
  it('should integrate seamlessly with NewOrderingInterface', async () => {
    // Simulate complex order building
    const { orderState, uiState } = await buildComplexOrder();
    
    // Hold the order
    const holdId = await holdManager.hold(orderState, uiState);
    
    // Verify interface reset
    expect(getCurrentOrderState()).toEqual(initialOrderState);
    
    // Resume order
    await holdManager.resume(holdId);
    
    // Verify complete restoration
    expect(getCurrentOrderState()).toEqual(orderState);
    expect(getCurrentUiState()).toEqual(uiState);
  });
});
```

### User Acceptance Testing

```typescript
describe('User Experience Scenarios', () => {
  it('should handle interruption scenario smoothly', async () => {
    // Complex order in progress
    await simulateComplexOrderBuilding();
    
    // Interruption occurs
    const holdId = await simulateOrderHold();
    
    // Handle interruption
    await simulateSimpleOrder();
    
    // Resume complex order
    await simulateOrderResume(holdId);
    
    // Verify seamless continuation
    expect(getOrderProgress()).toBe('can_continue_seamlessly');
  });
});
```












































































































































































































































































































































































































































































































































































































